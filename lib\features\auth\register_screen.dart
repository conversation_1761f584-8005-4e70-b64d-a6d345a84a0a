// ignore_for_file: library_private_types_in_public_api

import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:country_picker/country_picker.dart';
import 'package:guest_posts_buyer/core/services/auth_service.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class SignUpScreen extends StatefulWidget {
  final AuthService authService; // Add AuthService as a dependency

  const SignUpScreen({super.key, required this.authService});

  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController phoneCodeController = TextEditingController();

  bool _isLoading = false; // To show a loading indicator

  // Signup method using AuthService
  Future<void> _signUp() async {
    if (passwordController.text != confirmPasswordController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Passwords do not match!')),
      );
      return;
    }

    setState(() => _isLoading = true);

    final mobileNumber =
        phoneCodeController.text.isNotEmpty && phoneController.text.isNotEmpty
            ? '${phoneCodeController.text}${phoneController.text}'
            : null;

    final success = await widget.authService.registerWithEmail(
      name: usernameController.text.trim(),
      email: emailController.text.trim(),
      password: passwordController.text.trim(),
      mobileNumber: mobileNumber,
      profilePictureUrl:
          null, // Add logic if you have a profile picture uploader
      isPublisher: false, // Default value; adjust as needed
      additionalDetails: {
        'country': countryController.text.trim(),
      },
    );

    if (!mounted) return;
    setState(() => _isLoading = false);

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Sign-up successful! Please verify your email.')),
      );
      context.go('/home'); // Navigate to home screen
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Sign-up failed. Please try again.')),
      );
    }
  }

  Color _currentColor = const Color.fromARGB(255, 4, 255, 242);
  double _leftPosition = -5;
  double _bottomPosition = -10;

  final List<Color> _colors = [
    Colors.amber,
    const Color.fromARGB(255, 224, 169, 5),
    Colors.blue,
    Colors.green,
    const Color.fromARGB(255, 53, 39, 176),
  ];

  int _colorIndex = 0;
  bool _showPassword = false;
  bool _showConfirmPassword = false;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _changeAppearance();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    emailController.dispose();
    usernameController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    countryController.dispose();
    phoneController.dispose();
    phoneCodeController.dispose();
    super.dispose();
  }

  void _changeAppearance() {
    setState(() {
      _colorIndex = (_colorIndex + 1) % _colors.length;
      _currentColor = _colors[_colorIndex];
      _leftPosition += 10;
      _bottomPosition += 5;

      if (_leftPosition > MediaQuery.of(context).size.width) {
        _leftPosition = -5;
      }
      if (_bottomPosition > MediaQuery.of(context).size.height) {
        _bottomPosition = -10;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Animated circular background
          Positioned(
            left: _leftPosition,
            bottom: _bottomPosition,
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            right: 300,
            top: 300,
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            left: _leftPosition,
            top: _bottomPosition,
            child: AnimatedContainer(
              duration: const Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          // Blurred background
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 100.0, sigmaY: 100.0),
            child: Container(
              color: const Color.fromARGB(143, 255, 255, 255),
            ),
          ),
          // Main content
          Center(
            child: SizedBox(
              width: double.infinity,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20.0, vertical: 60),
                    child: SizedBox(
                      width: 400,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Logo
                          CircleAvatar(
                            radius: 40,
                            backgroundColor: Colors.amber,
                            child: const Text(
                              'GP',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ),
                          const SizedBox(height: 100),
                          // Sign Up text
                          Text(
                            'Create Your Account',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Cairo',
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 30),
                          // Social login button (Google Sign-In)
                          SizedBox(
                            height: 50,
                            width: 400,
                            child: ElevatedButton.icon(
                              onPressed: _isLoading
                                  ? null
                                  : () async {
                                      setState(() => _isLoading = true);
                                      final success = await widget.authService
                                          .signInWithGoogle();
                                      if (!mounted) return;
                                      setState(() => _isLoading = false);
                                      if (success) {
                                        context.go('/home');
                                      } else {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                              content: Text(
                                                  'Google Sign-In failed.')),
                                        );
                                      }
                                    },
                              icon: const Icon(
                                FontAwesomeIcons.google,
                                color: Color.fromARGB(255, 0, 0, 0),
                                size: 25,
                              ),
                              label: const Text(' Sign up with Google'),
                              style: ElevatedButton.styleFrom(
                                elevation: 0,
                                textStyle: const TextStyle(
                                    fontFamily: 'Cairo', fontSize: 20),
                                foregroundColor: Colors.black87,
                                backgroundColor: AppColors.componentBackColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 16),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          // "OR Continue With" text
                          SizedBox(
                            width: 400,
                            child: Row(
                              children: [
                                Expanded(
                                    child: Divider(color: AppColors.greyBack)),
                                Text(
                                  '  OR Continue With  ',
                                  style: TextStyle(
                                    color: Colors.amber.withAlpha(200),
                                    fontSize: 20,
                                    fontFamily: 'Cairo',
                                  ),
                                ),
                                Expanded(
                                    child: Divider(color: AppColors.greyBack)),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Email field
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: TextField(
                              controller: emailController,
                              decoration: InputDecoration(
                                hintText: 'Email',
                                filled: true,
                                fillColor: AppColors.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Username field
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: TextField(
                              controller: usernameController,
                              decoration: InputDecoration(
                                hintText: 'Username',
                                filled: true,
                                fillColor: AppColors.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Password field
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: TextField(
                              controller: passwordController,
                              obscureText: !_showPassword,
                              decoration: InputDecoration(
                                hintText: 'Password',
                                filled: true,
                                fillColor: AppColors.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                suffix: InkWell(
                                  onTap: () {
                                    setState(() {
                                      _showPassword = !_showPassword;
                                    });
                                  },
                                  child: Text(_showPassword ? 'Hide' : 'Show'),
                                ),
                                suffixStyle: const TextStyle(
                                    color: Colors.amber, fontSize: 18),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Confirm Password field
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: TextField(
                              controller: confirmPasswordController,
                              obscureText: !_showConfirmPassword,
                              decoration: InputDecoration(
                                hintText: 'Confirm Password',
                                filled: true,
                                fillColor: AppColors.componentBackColor,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(10),
                                  borderSide: BorderSide.none,
                                ),
                                suffix: InkWell(
                                  onTap: () {
                                    setState(() {
                                      _showConfirmPassword =
                                          !_showConfirmPassword;
                                    });
                                  },
                                  child: Text(
                                      _showConfirmPassword ? 'Hide' : 'Show'),
                                ),
                                suffixStyle: const TextStyle(
                                    color: Colors.amber, fontSize: 18),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Country field
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: GestureDetector(
                              onTap: () {
                                showCountryPicker(
                                  useSafeArea: true,
                                  context: context,
                                  showPhoneCode: false,
                                  onSelect: (Country country) {
                                    countryController.text = country.name;
                                    phoneCodeController.text =
                                        '+${country.phoneCode}';
                                  },
                                  countryListTheme: CountryListThemeData(
                                    borderRadius: BorderRadius.circular(10),
                                    inputDecoration: InputDecoration(
                                      hintText: 'Country',
                                      filled: true,
                                      fillColor: AppColors.componentBackColor,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: BorderSide.none,
                                      ),
                                    ),
                                  ),
                                );
                              },
                              child: AbsorbPointer(
                                child: TextField(
                                  controller: countryController,
                                  decoration: InputDecoration(
                                    hintText: 'Country',
                                    filled: true,
                                    fillColor: AppColors.componentBackColor,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide.none,
                                    ),
                                    suffixIcon:
                                        const Icon(Icons.arrow_drop_down),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          // Phone number with code
                          SizedBox(
                            width: 400,
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 100,
                                  height: 50,
                                  child: GestureDetector(
                                    onTap: () {
                                      showCountryPicker(
                                        context: context,
                                        showPhoneCode: true,
                                        onSelect: (Country country) {
                                          phoneCodeController.text =
                                              '+${country.phoneCode}';
                                        },
                                        countryListTheme: CountryListThemeData(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          inputDecoration: InputDecoration(
                                            hintText: '+XX',
                                            filled: true,
                                            fillColor:
                                                AppColors.componentBackColor,
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide.none,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                    child: AbsorbPointer(
                                      child: TextField(
                                        controller: phoneCodeController,
                                        decoration: InputDecoration(
                                          hintText: '+XX',
                                          filled: true,
                                          fillColor:
                                              AppColors.componentBackColor,
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            borderSide: BorderSide.none,
                                          ),
                                          suffixIcon:
                                              const Icon(Icons.arrow_drop_down),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: SizedBox(
                                    height: 50,
                                    child: TextField(
                                      controller: phoneController,
                                      decoration: InputDecoration(
                                        hintText: 'Phone Number',
                                        filled: true,
                                        fillColor: AppColors.componentBackColor,
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          borderSide: BorderSide.none,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Sign Up button
                          SizedBox(
                            width: 400,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _signUp,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.amber,
                                foregroundColor: Colors.white,
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 40, vertical: 15),
                              ),
                              child: _isLoading
                                  ? const CircularProgressIndicator(
                                      color: Colors.white)
                                  : const Text(
                                      'Sign Up',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'Cairo',
                                        fontSize: 18,
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          // Already have an account
                          InkWell(
                            onTap: () => context.pop(),
                            child: RichText(
                              text: const TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'Already have an account? ',
                                    style: TextStyle(
                                      color: Colors.black87,
                                      fontSize: 16,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  TextSpan(
                                    text: 'Log in now',
                                    style: TextStyle(
                                      color: Colors.amber,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Cookie settings button (bottom-left)
          Positioned(
            bottom: 20,
            left: 20,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.black87,
                backgroundColor: Colors.white,
                side: BorderSide(color: Colors.grey.shade300),
                elevation: 1,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text(
                'COOKIE SETTINGS',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
