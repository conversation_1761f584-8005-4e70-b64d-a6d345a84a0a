import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String name;
  final String email;
  final bool emailVerified;
  final String mobileNumber;
  final String profilePictureUrl;
  final bool isPublisher;
  final Timestamp? createdAt;
  final Timestamp? lastLogin;
  final Map<String, dynamic> additionalDetails;
  final bool isBlocked;
  final bool isAdmin;
  final List<String> assignedOrderIds; // Added for publisher orders
  final double mainBalance; // Added for publisher balance
  final double reservedBalance; // Added for publisher balance
    final double buyerFunds; // Added for publisher balance
  final double reservedFunds; // Added for publisher balance
  

Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'emailVerified': emailVerified,
      'mobileNumber': mobileNumber,
      'profilePictureUrl': profilePictureUrl,
      'isPublisher': isPublisher,
      'createdAt': createdAt,
      'lastLogin': lastLogin,
      'additionalDetails': additionalDetails,
      'isBlocked': isBlocked,
      'isAdmin': isAdmin,
      'assignedOrderIds': assignedOrderIds,
      'mainBalance': mainBalance,
      'reservedBalance': reservedBalance,
      'buyerFunds': buyerFunds,
      'reservedFunds': reservedFunds,
    };
  }

  UserModel({
    required this.uid,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.mobileNumber,
    required this.profilePictureUrl,
    required this.isPublisher,
    this.createdAt,
    this.lastLogin,
    required this.additionalDetails,
    this.isBlocked = false,
    this.isAdmin = false,
    this.assignedOrderIds = const [],
    this.mainBalance = 0.0,
    this.reservedBalance = 0.0,
    this.buyerFunds = 0.0,
    this.reservedFunds = 0.0,
  });

  factory UserModel.fromMap(Map<String, dynamic> data, String id) {
    return UserModel(
      uid: id,
      name: data['name'] ?? 'N/A',
      email: data['email'] ?? 'N/A',
      emailVerified: data['emailVerified'] ?? false,
      mobileNumber: data['mobileNumber'] ?? '',
      profilePictureUrl: data['profilePictureUrl'] ?? '',
      isPublisher: data['isPublisher'] ?? false,
      createdAt: data['createdAt'] as Timestamp?,
      lastLogin: data['lastLogin'] as Timestamp?,
      additionalDetails:
          Map<String, dynamic>.from(data['additionalDetails'] ?? {}),
      isBlocked: data['isBlocked'] ?? false,
      isAdmin: data['isAdmin'] ?? false,
      assignedOrderIds: List<String>.from(data['assignedOrderIds'] ?? []),
      mainBalance: (data['mainBalance'] ?? 0.0).toDouble(),
      reservedBalance: (data['reservedBalance'] ?? 0.0).toDouble(),
      buyerFunds: (data['buyerFunds'] ?? 0.0).toDouble(),
      reservedFunds: (data['reservedFunds'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'emailVerified': emailVerified,
      'mobileNumber': mobileNumber,
      'profilePictureUrl': profilePictureUrl,
      'isPublisher': isPublisher,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'lastLogin': lastLogin ?? FieldValue.serverTimestamp(),
      'additionalDetails': additionalDetails,
      'isBlocked': isBlocked,
      'isAdmin': isAdmin,
      'assignedOrderIds': assignedOrderIds,
      'mainBalance': mainBalance,
      'reservedBalance': reservedBalance,
      'buyerFunds': buyerFunds,
      'reservedFunds': reservedFunds,
    };
  }

  UserModel copyWith({
    String? uid,
    String? name,
    String? email,
    bool? emailVerified,
    String? mobileNumber,
    String? profilePictureUrl,
    bool? isPublisher,
    Timestamp? createdAt,
    Timestamp? lastLogin,
    Map<String, dynamic>? additionalDetails,
    bool? isBlocked,
    bool? isAdmin,
    List<String>? assignedOrderIds,
    double? mainBalance,
    double? reservedBalance,
    double? buyerFunds,
    double? reservedFunds,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      isPublisher: isPublisher ?? this.isPublisher,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      additionalDetails: additionalDetails ?? this.additionalDetails,
      isBlocked: isBlocked ?? this.isBlocked,
      isAdmin: isAdmin ?? this.isAdmin,
      assignedOrderIds: assignedOrderIds ?? this.assignedOrderIds,
      mainBalance: mainBalance ?? this.mainBalance,
      reservedBalance: reservedBalance ?? this.reservedBalance,
      buyerFunds: buyerFunds ?? this.buyerFunds,
      reservedFunds: reservedFunds ?? this.reservedFunds,
    );
  }

}
