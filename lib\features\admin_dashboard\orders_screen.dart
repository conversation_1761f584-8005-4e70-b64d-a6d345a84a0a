import 'dart:async';
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:universal_html/html.dart' as html;

import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

import 'package:guest_posts_buyer/features/admin_dashboard/user_profile_page.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderManagementScreen extends StatefulWidget {
  const OrderManagementScreen({super.key});

  @override
  State<OrderManagementScreen> createState() => _OrderManagementScreenState();
}

class _OrderManagementScreenState extends State<OrderManagementScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  String _selectedStatus = 'All';
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _priceMinController = TextEditingController();
  final TextEditingController _priceMaxController = TextEditingController();
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedBuyer;
  String? _selectedPublisher;
  List<OrderModel> _orders = [];
  Map<String, Map<String, String>> _buyerDetails = {};
  Map<String, WebsiteModel> _websiteDetails = {};
  Map<String, UserModel> _publisherDetails = {};
  bool _isLoading = false;
  bool _isSearchLoading = false;
  Timer? _debounce;
  String? _adminUid;
  bool _isPublisher = false; // New flag to track if user is a publisher
  List<String> _selectedOrderIds = [];
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  String _sortField = 'orderDate';
  bool _sortAscending = false;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<QuerySnapshot>? _usersSubscription;
  StreamSubscription<QuerySnapshot>? _websitesSubscription;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
    _searchController.addListener(_onSearchChanged);
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );
      setState(() {
        _adminUid = user.uid;
        _isPublisher = userData?['isPublisher'] == true;
        _isLoading = true;
      });
      _subscribeToData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking user access: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _isSearchLoading = true;
      });
      Future.delayed(const Duration(milliseconds: 300), () {
        setState(() => _isSearchLoading = false);
      });
    });
  }

  void _subscribeToData() {
    _ordersSubscription?.cancel();
    _ordersSubscription = FirebaseFirestore.instance
        .collection('orders')
        .orderBy(_sortField, descending: !_sortAscending)
        .snapshots()
        .listen((snapshot) {
      final orders = snapshot.docs.map((doc) {
        final data = doc.data();
        return OrderModel.fromMap(data..['orderId'] = doc.id);
      }).toList();

      final buyerIds = orders.map((o) => o.buyerId).toSet();
      final websiteIds = orders.map((o) => o.websiteId).toList();
      final publisherIds =
          orders.map((o) => o.publisherId).whereType<String>().toSet();

      setState(() {
        _orders = orders;
        _isLoading = false;
      });

      _usersSubscription?.cancel();
      _usersSubscription = FirebaseFirestore.instance
          .collection('users')
          .where(FieldPath.documentId,
              whereIn: [
                ...buyerIds.where((id) => id.isNotEmpty),
                ...publisherIds.where((id) => id.isNotEmpty),
              ].toList())
          .snapshots()
          .listen((snapshot) {
        final buyerDetails = <String, Map<String, String>>{};
        final publisherDetails = <String, UserModel>{};

        for (var doc in snapshot.docs) {
          final data = doc.data();
          if (buyerIds.contains(doc.id)) {
            buyerDetails[doc.id] = {
              'name': data['name'] ?? 'Unknown',
              'email': data['email'] ?? 'N/A',
            };
          }
          if (publisherIds.contains(doc.id)) {
            publisherDetails[doc.id] = UserModel.fromMap(data, doc.id);
          }
        }

        setState(() {
          _buyerDetails = buyerDetails;
          _publisherDetails = publisherDetails;
        });
      }, onError: (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading users: $e'),
            backgroundColor: Colors.red,
          ),
        );
      });

      _websitesSubscription?.cancel();
      final validWebsiteIds = websiteIds.where((id) => id.isNotEmpty).toList();
      _websitesSubscription = FirebaseFirestore.instance
          .collection('websites')
          .where(FieldPath.documentId, whereIn: validWebsiteIds)
          .snapshots()
          .listen((snapshot) {
        final websiteDetails = <String, WebsiteModel>{};

        for (var doc in snapshot.docs) {
          final data = doc.data();
          websiteDetails[doc.id] =
              WebsiteModel.fromMap(data..['websiteId'] = doc.id);
        }

        setState(() {
          _websiteDetails = websiteDetails;
        });
      }, onError: (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading websites: $e'),
            backgroundColor: Colors.red,
          ),
        );
      });
    }, onError: (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading orders: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _subscribeToData,
          ),
        ),
      );
    });
  }

  List<OrderModel> _filteredOrders(List<OrderModel> orders) {
    var filtered = orders;

    if (_priceMinController.text.isNotEmpty) {
      final minPrice = double.tryParse(_priceMinController.text);
      if (minPrice != null) {
        filtered =
            filtered.where((order) => order.totalPrice >= minPrice).toList();
      }
    }
    if (_priceMaxController.text.isNotEmpty) {
      final maxPrice = double.tryParse(_priceMaxController.text);
      if (maxPrice != null) {
        filtered =
            filtered.where((order) => order.totalPrice <= maxPrice).toList();
      }
    }

    if (_startDate != null) {
      filtered = filtered
          .where((order) =>
              order.orderDate.toDate().isAfter(_startDate!) ||
              order.orderDate.toDate().isAtSameMomentAs(_startDate!))
          .toList();
    }
    if (_endDate != null) {
      filtered = filtered
          .where((order) =>
              order.orderDate.toDate().isBefore(_endDate!) ||
              order.orderDate.toDate().isAtSameMomentAs(_endDate!))
          .toList();
    }

    if (_selectedBuyer != null && _selectedBuyer != 'All') {
      filtered =
          filtered.where((order) => order.buyerId == _selectedBuyer).toList();
    }

    if (_selectedPublisher != null && _selectedPublisher != 'All') {
      filtered = filtered
          .where((order) => order.publisherId == _selectedPublisher)
          .toList();
    }

    if (_selectedStatus == 'All') {
      return filtered;
    }
    if (_selectedStatus == 'Disputed') {
      return filtered.where((order) => order.isDisputed ?? false).toList();
    }
    return filtered.where((order) => order.status == _selectedStatus).toList();
  }

  List<OrderModel> _searchedOrders(List<OrderModel> orders) {
    if (_searchController.text.isEmpty) return orders;
    final searchTerm = _searchController.text.toLowerCase();
    return orders.where((order) {
      final website = _websiteDetails[order.websiteId];
      final publisher = _publisherDetails[order.publisherId];
      return (order.orderId?.toLowerCase().contains(searchTerm) ?? false) ||
          (website?.url.toLowerCase().contains(searchTerm) ?? false) ||
          (website?.domainName.toLowerCase().contains(searchTerm) ?? false) ||
          (_buyerDetails[order.buyerId]?['name']
                  ?.toLowerCase()
                  .contains(searchTerm) ??
              false) ||
          (_buyerDetails[order.buyerId]?['email']
                  ?.toLowerCase()
                  .contains(searchTerm) ??
              false) ||
          (publisher?.name.toLowerCase().contains(searchTerm) ?? false) ||
          (publisher?.email.toLowerCase().contains(searchTerm) ?? false) ||
          order.status.toLowerCase().contains(searchTerm) ||
          (order.disputeStatus?.toLowerCase().contains(searchTerm) ?? false) ||
          order.postTitle.toLowerCase().contains(searchTerm);
    }).toList();
  }

  Future<void> _logAuditAction(
      String orderId, String action, String? details) async {
    try {
      await _firestoreService.addDocument(
        collectionPath: 'orders/$orderId/audit_logs',
        data: {
          'adminId': _adminUid,
          'action': action,
          'details': details,
          'timestamp': Timestamp.now(),
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error logging audit action: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _changeStatus(OrderModel order, String newStatus) async {
    if (newStatus == order.status) return;

    try {
      final now = Timestamp.now();
      // Get actor type based on current user
      String actorType;
      if (_isPublisher) {
        actorType = "Publisher";
      } else {
        actorType = "Admin";
      }

      final statusDetail = '$newStatus by $actorType';

      // Prepare data to update
      final Map<String, dynamic> updateData = {
        'status': newStatus,
        'statusDetail': statusDetail,
        'lastUpdated': now,
        'actionBy': _adminUid,
        'actionTimestamp': now,
      };

      // Add specific timestamp based on status
      if (newStatus == 'In Progress') {
        updateData['inProgressDate'] = now;
      } else if (newStatus == 'Completed') {
        updateData['completionDate'] = now;
      } else if (newStatus == 'Approved') {
        updateData['approvalDate'] = now;
      }

      await _firestoreService.updateDocument(
        collectionPath: 'orders',
        documentId: order.orderId!,
        data: updateData,
      );

      setState(() {
        final index = _orders.indexWhere((o) => o.orderId == order.orderId);
        if (index != -1) {
          _orders[index] = order.copyWith(
            status: newStatus,
            statusDetail: statusDetail,
            lastUpdated: now,
            actionBy: _adminUid,
            actionTimestamp: now,
            inProgressDate:
                newStatus == 'In Progress' ? now : order.inProgressDate,
            completionDate:
                newStatus == 'Completed' ? now : order.completionDate,
            approvalDate: newStatus == 'Approved' ? now : order.approvalDate,
          );
        }
      });

      await _logAuditAction(
        order.orderId!,
        'Status Changed',
        'Changed to $newStatus ($statusDetail)',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Order status updated to $newStatus')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _bulkAction(String action,
      {String? status, bool? isDisputed}) async {
    if (_selectedOrderIds.isEmpty) return;

    try {
      for (var orderId in _selectedOrderIds) {
        final order = _orders.firstWhere((o) => o.orderId == orderId);
        if (action == 'status' && status != null) {
          final now = Timestamp.now();
          // Get actor type based on current user
          String actorType;
          if (_isPublisher) {
            actorType = "Publisher";
          } else {
            actorType = "Admin";
          }

          final statusDetail = '$status by $actorType';

          // Prepare data to update
          final Map<String, dynamic> updateData = {
            'status': status,
            'statusDetail': statusDetail,
            'lastUpdated': now,
            'actionBy': _adminUid,
            'actionTimestamp': now,
          };

          // Add specific timestamp based on status
          if (status == 'In Progress') {
            updateData['inProgressDate'] = now;
          } else if (status == 'Completed') {
            updateData['completionDate'] = now;
          } else if (status == 'Approved') {
            updateData['approvalDate'] = now;
          }

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: orderId,
            data: updateData,
          );

          await _logAuditAction(
            orderId,
            'Bulk Status Changed',
            'Changed to $status ($statusDetail)',
          );

          setState(() {
            final index = _orders.indexWhere((o) => o.orderId == orderId);
            if (index != -1) {
              _orders[index] = order.copyWith(
                status: status,
                statusDetail: statusDetail,
                lastUpdated: now,
                actionBy: _adminUid,
                actionTimestamp: now,
                inProgressDate:
                    status == 'In Progress' ? now : order.inProgressDate,
                completionDate:
                    status == 'Completed' ? now : order.completionDate,
                approvalDate: status == 'Approved' ? now : order.approvalDate,
              );
            }
          });
        } else if (action == 'dispute' && isDisputed != null) {
          final now = Timestamp.now();
          // Get actor type based on current user
          String actorType;
          if (_isPublisher) {
            actorType = "Publisher";
          } else {
            actorType = "Admin";
          }

          final disputeAction =
              isDisputed ? 'Dispute flagged' : 'Dispute resolved';
          final disputeStatusDetail = '$disputeAction by $actorType';

          await _firestoreService.updateDocument(
            collectionPath: 'orders',
            documentId: orderId,
            data: {
              'isDisputed': isDisputed,
              'disputeStatus': isDisputed ? 'Open' : null,
              'lastUpdated': now,
              'actionBy': _adminUid,
              'actionTimestamp': now,
              'statusDetail': disputeStatusDetail,
            },
          );

          await _logAuditAction(
            orderId,
            isDisputed ? 'Bulk Dispute Flagged' : 'Bulk Dispute Resolved',
            disputeStatusDetail,
          );

          setState(() {
            final index = _orders.indexWhere((o) => o.orderId == orderId);
            if (index != -1) {
              _orders[index] = order.copyWith(
                isDisputed: isDisputed,
                disputeStatus: isDisputed ? 'Open' : null,
                lastUpdated: now,
                actionBy: _adminUid,
                actionTimestamp: now,
                statusDetail: disputeStatusDetail,
              );
            }
          });
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Bulk $action completed')),
        );
        setState(() => _selectedOrderIds.clear());
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error performing bulk $action: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportToCsv(List<OrderModel> orders) {
    final List<List<dynamic>> rows = [
      [
        'Order ID',
        'Website URL',
        'Buyer Name',
        'Buyer Email',
        'Publisher Name',
        'Publisher Email',
        'Post Title',
        'Word Count',
        'Backlink Type',
        'Is Sponsored',
        'Base Price',
        'Total Price',
        'Status',
        'Status Detail',
        'Dispute Status',
        'Order Date',
        'In Progress Date',
        'Approval Date',
        'Completion Date',
        'Last Updated',
        'Last Action By',
        'Last Action Date',
        'Is Disputed',
        'Dispute Note',
      ],
      ...orders.map((order) {
        final website = _websiteDetails[order.websiteId];
        final publisher = _publisherDetails[order.publisherId];
        return [
          order.orderId ?? 'N/A',
          website?.url ?? 'N/A',
          _buyerDetails[order.buyerId]?['name'] ?? 'Unknown',
          _buyerDetails[order.buyerId]?['email'] ?? 'N/A',
          publisher?.name ?? 'N/A',
          publisher?.email ?? 'N/A',
          order.postTitle,
          order.wordCount,
          order.backlinkType,
          order.isSponsored,
          '\$${order.basePrice.toStringAsFixed(2)}',
          '\$${order.totalPrice.toStringAsFixed(2)}',
          order.status,
          order.statusDetail ?? 'N/A',
          order.disputeStatus ?? 'N/A',
          _formatDate(order.orderDate),
          _formatDate(order.inProgressDate),
          _formatDate(order.approvalDate),
          _formatDate(order.completionDate),
          _formatDate(order.lastUpdated),
          order.actionBy ?? 'N/A',
          _formatDate(order.actionTimestamp),
          order.isDisputed ?? false,
          order.disputeNote ?? 'N/A',
        ];
      }),
    ];

    String csv = const ListToCsvConverter().convert(rows);
    final bytes = utf8.encode(csv);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download = 'orders_export_${DateTime.now().toIso8601String()}.csv';
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  void _viewDetails(OrderModel order) {
    // Show the full OrderDetailsDialog with assignment functionality
    showDialog(
      context: context,
      builder: (context) => OrderDetailsDialog(
        order: order,
        website: _websiteDetails[order.websiteId],
        buyerName: _buyerDetails[order.buyerId]?['name'] ?? 'Unknown',
        buyerEmail: _buyerDetails[order.buyerId]?['email'] ?? 'N/A',
        publisher: _publisherDetails[order.publisherId],
        onChangeStatus: (status) => _changeStatus(order, status),
        onAssignPublisher: (publisherId) =>
            _assignPublisher(order, publisherId),
        onUpdateDispute: (isDisputed, note, disputeStatus) =>
            _updateDispute(order, isDisputed, note, disputeStatus),
        firestoreService: _firestoreService,
      ),
    );
  }

  Future<void> _updateDispute(OrderModel order, bool isDisputed, String? note,
      String? disputeStatus) async {
    try {
      final now = Timestamp.now();
      // Get actor type based on current user
      String actorType;
      if (_isPublisher) {
        actorType = "Publisher";
      } else {
        actorType = "Admin";
      }

      final disputeAction = isDisputed ? 'Dispute flagged' : 'Dispute resolved';
      final disputeStatusDetail = '$disputeAction by $actorType';

      await _firestoreService.updateDocument(
        collectionPath: 'orders',
        documentId: order.orderId!,
        data: {
          'isDisputed': isDisputed,
          'disputeNote': note,
          'disputeStatus': disputeStatus,
          'lastUpdated': now,
          'actionBy': _adminUid,
          'actionTimestamp': now,
          'statusDetail': disputeStatusDetail,
        },
      );

      setState(() {
        final index = _orders.indexWhere((o) => o.orderId == order.orderId);
        if (index != -1) {
          _orders[index] = order.copyWith(
            isDisputed: isDisputed,
            disputeNote: note,
            disputeStatus: disputeStatus,
            lastUpdated: now,
            actionBy: _adminUid,
            actionTimestamp: now,
            statusDetail: disputeStatusDetail,
          );
        }
      });

      await _logAuditAction(
        order.orderId!,
        isDisputed ? 'Dispute Updated' : 'Dispute Resolved',
        'Status: $disputeStatus, Note: ${note ?? 'None'}',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isDisputed ? 'Dispute updated' : 'Dispute resolved'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating dispute: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _assignPublisher(OrderModel order, String? publisherId) async {
    try {
      final now = Timestamp.now();
      // Get actor type based on current user
      String actorType;
      if (_isPublisher) {
        actorType = "Publisher";
      } else {
        actorType = "Admin";
      }

      final action =
          publisherId != null ? 'Publisher assigned' : 'Publisher unassigned';
      final actionDetail = '$action by $actorType';

      await _firestoreService.updateDocument(
        collectionPath: 'orders',
        documentId: order.orderId!,
        data: {
          'publisherId': publisherId,
          'lastUpdated': now,
          'actionBy': _adminUid,
          'actionTimestamp': now,
          'statusDetail': actionDetail,
        },
      );

      if (publisherId != null) {
        final publisherData = await _firestoreService.getDocument(
          collectionPath: 'users',
          documentId: publisherId,
        );
        if (publisherData != null) {
          final publisher = UserModel.fromMap(publisherData, publisherId);
          final updatedOrders = List<String>.from(publisher.assignedOrderIds)
            ..add(order.orderId!);
          await _firestoreService.updateDocument(
            collectionPath: 'users',
            documentId: publisherId,
            data: {'assignedOrderIds': updatedOrders},
          );
        }
      }

      setState(() {
        final index = _orders.indexWhere((o) => o.orderId == order.orderId);
        if (index != -1) {
          _orders[index] = order.copyWith(
            publisherId: publisherId,
            lastUpdated: now,
            actionBy: _adminUid,
            actionTimestamp: now,
            statusDetail: actionDetail,
          );
        }
      });

      await _logAuditAction(
        order.orderId!,
        'Publisher Assignment',
        publisherId != null ? 'Assigned to $publisherId' : 'Unassigned',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(publisherId != null
                ? 'Publisher assigned successfully'
                : 'Publisher unassigned successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error assigning publisher: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _priceMinController.dispose();
    _priceMaxController.dispose();
    _debounce?.cancel();
    _ordersSubscription?.cancel();
    _usersSubscription?.cancel();
    _websitesSubscription?.cancel();
    super.dispose();
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => setState(() {
        _selectedStatus = title;
        _currentPage = 1;
      }),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.blue : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(26, 115, 232, 0.08),
              blurRadius: 4,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
          border: Border.all(
            color: isSelected
                ? AppColors.blue
                : const Color.fromRGBO(232, 240, 254, 0.8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected)
              Container(
                padding: const EdgeInsets.all(4),
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  _getIconForStatus(title),
                  color: AppColors.blue,
                  size: 16,
                ),
              ),
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 15,
                color: isSelected ? Colors.white : AppColors.dark,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForStatus(String status) {
    switch (status) {
      case 'All':
        return Icons.list;
      case 'Pending':
        return Icons.hourglass_empty;
      case 'In Progress':
        return Icons.autorenew;
      case 'Approved':
        return Icons.thumb_up;
      case 'Completed':
        return Icons.check_circle;
      case 'Disputed':
        return Icons.warning;
      default:
        return Icons.circle;
    }
  }

  Widget _buildAdminSummary(List<OrderModel> orders, double screenWidth) {
    final totalOrders = orders.length;
    final pendingOrders = orders.where((o) => o.status == 'Pending').length;
    final inProgressOrders =
        orders.where((o) => o.status == 'In Progress').length;
    final approvedOrders = orders.where((o) => o.status == 'Approved').length;
    final completedOrders = orders.where((o) => o.status == 'Completed').length;
    final disputedOrders = orders.where((o) => o.isDisputed ?? false).length;

    final crossAxisCount = screenWidth < 600
        ? 1
        : screenWidth < 900
            ? 2
            : screenWidth < 1200
                ? 3
                : 5;
    final cardWidth = screenWidth < 600
        ? screenWidth * 0.9
        : screenWidth / crossAxisCount - 32;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          title: 'Total Orders',
          value: totalOrders.toString(),
          color: AppColors.blue,
          icon: Icons.shopping_cart,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Pending',
          value: pendingOrders.toString(),
          color: AppColors.yellow,
          icon: Icons.hourglass_empty,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'In Progress',
          value: inProgressOrders.toString(),
          color: AppColors.yellow,
          icon: Icons.autorenew,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Approved',
          value: approvedOrders.toString(),
          color: Colors.orange,
          icon: Icons.thumb_up,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Completed',
          value: completedOrders.toString(),
          color: AppColors.green,
          icon: Icons.check_circle,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Disputed',
          value: disputedOrders.toString(),
          color: AppColors.red,
          icon: Icons.warning,
          cardWidth: cardWidth,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
  }) {
    final isSmallScreen = cardWidth < 200;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: cardWidth,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(color),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: isSmallScreen ? 20 : 24),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isSmallScreen ? 24 : 32,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(width: 8),
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  'orders',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 12 : 14,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.4,
      ),
      itemCount: 4,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            elevation: 4,
            child: Container(
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  String _getUserNameById(String? userId) {
    if (userId == null || userId.isEmpty) return 'N/A';
    if (_buyerDetails.containsKey(userId)) {
      return _buyerDetails[userId]?['name'] ?? 'Unknown';
    } else if (_publisherDetails.containsKey(userId)) {
      return _publisherDetails[userId]?.name ?? 'Unknown';
    }
    return 'Unknown';
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 600;
        final isWideScreen = screenWidth > 800;

        if (_auth.currentUser == null || _adminUid == null) {
          return _buildShimmerGrid();
        }

        final filteredOrders = _filteredOrders(_orders);
        final searchedOrders = _searchedOrders(filteredOrders);
        final totalPages = (searchedOrders.length / _itemsPerPage).ceil();
        final startIndex = (_currentPage - 1) * _itemsPerPage;
        final endIndex =
            (startIndex + _itemsPerPage).clamp(0, searchedOrders.length);
        final pagedOrders = searchedOrders.sublist(startIndex, endIndex);

        // Filter pending requests for the current publisher
        final pendingRequests = _orders
            .where((order) =>
                order.publisherId == _adminUid && order.status == 'Pending')
            .toList();
        final searchedPendingRequests = _searchedOrders(pendingRequests);

        return Scaffold(
          backgroundColor: Colors.white,
          body: Container(
            color: Colors.white,
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Order Management',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: isSmallScreen ? 24 : 32,
                                fontWeight: FontWeight.bold,
                                color: AppColors.dark,
                              ),
                            ),
                            Row(
                              children: [
                                Container(
                                  width: isSmallScreen ? 180 : 320,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color.fromRGBO(
                                            26, 115, 232, 0.08),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                        spreadRadius: 0,
                                      ),
                                    ],
                                    border: Border.all(
                                        color: const Color.fromRGBO(
                                            232, 240, 254, 0.8)),
                                  ),
                                  child: Stack(
                                    alignment: Alignment.centerRight,
                                    children: [
                                      TextField(
                                        controller: _searchController,
                                        decoration: InputDecoration(
                                          hintText:
                                              'Search by order ID, website, buyer...',
                                          hintStyle: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: isSmallScreen ? 14 : 16,
                                            color: AppColors.grey,
                                          ),
                                          fillColor: Colors.white,
                                          filled: true,
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            borderSide: BorderSide.none,
                                          ),
                                          prefixIcon: Icon(Icons.search,
                                              color: AppColors.blue),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 12),
                                        ),
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 14 : 16,
                                        ),
                                      ),
                                      if (_isSearchLoading)
                                        const Padding(
                                          padding: EdgeInsets.only(right: 16),
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: AppColors.blue,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Container(
                                  decoration: BoxDecoration(
                                    color:
                                        _getLightColorForIcon(AppColors.blue),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Tooltip(
                                    message: 'Export to CSV',
                                    child: IconButton(
                                      icon: const Icon(Icons.download,
                                          color: AppColors.blue),
                                      onPressed: () =>
                                          _exportToCsv(searchedOrders),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color:
                                        _getLightColorForIcon(AppColors.grey),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Tooltip(
                                    message: 'Refresh data',
                                    child: IconButton(
                                      icon: const Icon(Icons.refresh,
                                          color: AppColors.grey),
                                      onPressed: _subscribeToData,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        _buildAdminSummary(_orders, screenWidth),
                        const SizedBox(height: 24),
                        // Pending Requests Table (for publishers only)
                        if (_isPublisher &&
                            searchedPendingRequests.isNotEmpty) ...[
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: _getLightColorForIcon(AppColors.green),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.pending_actions,
                                  color: AppColors.green,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'My Pending Requests',
                                style: TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.dark,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 18),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color.fromRGBO(26, 115, 232, 0.08),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                columnSpacing: isWideScreen
                                    ? 40
                                    : isSmallScreen
                                        ? 16
                                        : 24,
                                headingRowHeight: 56,
                                dataRowMinHeight: 48,
                                dataRowMaxHeight: 60,
                                border: TableBorder(
                                  horizontalInside: BorderSide(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8),
                                    width: 1,
                                  ),
                                ),
                                headingRowColor: WidgetStatePropertyAll(
                                    _getLightColorForIcon(AppColors.green)),
                                dataRowColor:
                                    const WidgetStatePropertyAll(Colors.white),
                                columns: [
                                  DataColumn(
                                    label: Text('Order ID',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Website',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Buyer',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Price',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Ordered',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Actions',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                ],
                                rows: searchedPendingRequests
                                    .map((order) => _buildPendingRequestRow(
                                        order, isSmallScreen))
                                    .toList(),
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                        ],
                        // Main Orders Table
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: _getLightColorForIcon(AppColors.blue),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.shopping_cart,
                                color: AppColors.blue,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Orders Found',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: AppColors.dark,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 18),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildTab('All', _selectedStatus == 'All'),
                              const SizedBox(width: 8),
                              _buildTab(
                                  'Pending', _selectedStatus == 'Pending'),
                              const SizedBox(width: 8),
                              _buildTab('In Progress',
                                  _selectedStatus == 'In Progress'),
                              const SizedBox(width: 8),
                              _buildTab(
                                  'Approved', _selectedStatus == 'Approved'),
                              const SizedBox(width: 8),
                              _buildTab(
                                  'Completed', _selectedStatus == 'Completed'),
                              const SizedBox(width: 8),
                              _buildTab(
                                  'Disputed', _selectedStatus == 'Disputed'),
                            ],
                          ),
                        ),
                        const SizedBox(height: 18),
                        if (_selectedOrderIds.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color.fromRGBO(26, 115, 232, 0.08),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: _getLightColorForIcon(
                                            AppColors.blue),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Icon(
                                        Icons.checklist,
                                        color: AppColors.blue,
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Bulk Actions',
                                      style: const TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.dark,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: _getLightColorForIcon(
                                            AppColors.blue),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(
                                            Icons.check_box,
                                            color: AppColors.blue,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            '${_selectedOrderIds.length} order(s) selected',
                                            style: const TextStyle(
                                              fontFamily: 'Space',
                                              fontWeight: FontWeight.w500,
                                              color: AppColors.blue,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Row(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                                color: const Color.fromRGBO(
                                                    232, 240, 254, 1.0)),
                                          ),
                                          child: DropdownButton<String>(
                                            hint: const Padding(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              child: Text('Change Status'),
                                            ),
                                            underline: const SizedBox(),
                                            items: [
                                              'Pending',
                                              'In Progress',
                                              'Approved',
                                              'Completed',
                                              'Cancelled'
                                            ]
                                                .map((status) =>
                                                    DropdownMenuItem(
                                                      value: status,
                                                      child: Text(status),
                                                    ))
                                                .toList(),
                                            onChanged: (value) => _bulkAction(
                                                'status',
                                                status: value),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                                color: const Color.fromRGBO(
                                                    232, 240, 254, 1.0)),
                                          ),
                                          child: DropdownButton<bool>(
                                            hint: const Padding(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 8),
                                              child: Text('Dispute Action'),
                                            ),
                                            underline: const SizedBox(),
                                            items: [
                                              DropdownMenuItem(
                                                value: true,
                                                child:
                                                    const Text('Flag Dispute'),
                                              ),
                                              DropdownMenuItem(
                                                value: false,
                                                child: const Text(
                                                    'Resolve Dispute'),
                                              ),
                                            ],
                                            onChanged: (value) => _bulkAction(
                                                'dispute',
                                                isDisputed: value),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        TextButton.icon(
                                          onPressed: () => setState(
                                              () => _selectedOrderIds.clear()),
                                          icon:
                                              const Icon(Icons.clear, size: 16),
                                          label: const Text('Clear'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: AppColors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        const SizedBox(height: 16),
                        if (searchedOrders.isEmpty)
                          Container(
                            padding: const EdgeInsets.all(32),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color.fromRGBO(26, 115, 232, 0.08),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color:
                                          _getLightColorForIcon(AppColors.blue),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.shopping_cart,
                                      size: isSmallScreen ? 48 : 64,
                                      color: AppColors.blue,
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  Text(
                                    'No Orders Found',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 20 : 24,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    'Try adjusting your search or filter criteria',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 14 : 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      _searchController.clear();
                                      setState(() {
                                        _selectedStatus = 'All';
                                        _currentPage = 1;
                                      });
                                    },
                                    icon: const Icon(Icons.refresh),
                                    label: const Text('Reset Filters'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.blue,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24, vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        else
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color.fromRGBO(26, 115, 232, 0.08),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                columnSpacing: isWideScreen
                                    ? 40
                                    : isSmallScreen
                                        ? 16
                                        : 24,
                                headingRowHeight: 56,
                                dataRowMinHeight: 48,
                                dataRowMaxHeight: 60,
                                border: TableBorder(
                                  horizontalInside: BorderSide(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8),
                                    width: 1,
                                  ),
                                ),
                                headingRowColor: WidgetStatePropertyAll(
                                    _getLightColorForIcon(AppColors.blue)),
                                dataRowColor:
                                    const WidgetStatePropertyAll(Colors.white),
                                columns: [
                                  DataColumn(
                                    label: Checkbox(
                                      value: _selectedOrderIds.length ==
                                          searchedOrders.length,
                                      onChanged: (value) {
                                        setState(() {
                                          if (value == true) {
                                            _selectedOrderIds = searchedOrders
                                                .map((o) => o.orderId!)
                                                .toList();
                                          } else {
                                            _selectedOrderIds.clear();
                                          }
                                        });
                                      },
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text('Order ID',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Website',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Buyer',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Publisher',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: InkWell(
                                      onTap: () => _toggleSort('totalPrice'),
                                      child: Row(
                                        children: [
                                          Text('Price',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                          if (_sortField == 'totalPrice')
                                            Icon(
                                              _sortAscending
                                                  ? Icons.arrow_upward
                                                  : Icons.arrow_downward,
                                              size: 16,
                                              color: AppColors.dark,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text('Status',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: Text('Dispute',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                  DataColumn(
                                    label: InkWell(
                                      onTap: () => _toggleSort('orderDate'),
                                      child: Row(
                                        children: [
                                          Text('Ordered',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                          if (_sortField == 'orderDate')
                                            Icon(
                                              _sortAscending
                                                  ? Icons.arrow_upward
                                                  : Icons.arrow_downward,
                                              size: 16,
                                              color: AppColors.dark,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  DataColumn(
                                    label: Text('Actions',
                                        style: _headerStyle(isSmallScreen)),
                                  ),
                                ],
                                rows: pagedOrders
                                    .asMap()
                                    .entries
                                    .expand((entry) => _buildDataRow(
                                          entry.value,
                                          isSmallScreen,
                                          entry.key,
                                        ))
                                    .toList(),
                              ),
                            ),
                          ),
                        const SizedBox(height: 16),
                        if (totalPages > 1)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 16, horizontal: 24),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      const Color.fromRGBO(26, 115, 232, 0.08),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                  color:
                                      const Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildPageButton(
                                  icon: Icons.keyboard_double_arrow_left,
                                  onPressed: _currentPage > 1
                                      ? () => setState(() => _currentPage = 1)
                                      : null,
                                  isActive: _currentPage > 1,
                                ),
                                const SizedBox(width: 8),
                                _buildPageButton(
                                  icon: Icons.chevron_left,
                                  onPressed: _currentPage > 1
                                      ? () => setState(() => _currentPage--)
                                      : null,
                                  isActive: _currentPage > 1,
                                ),
                                Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  decoration: BoxDecoration(
                                    color:
                                        _getLightColorForIcon(AppColors.blue),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'Page $_currentPage of $totalPages',
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.blue,
                                    ),
                                  ),
                                ),
                                _buildPageButton(
                                  icon: Icons.chevron_right,
                                  onPressed: _currentPage < totalPages
                                      ? () => setState(() => _currentPage++)
                                      : null,
                                  isActive: _currentPage < totalPages,
                                ),
                                const SizedBox(width: 8),
                                _buildPageButton(
                                  icon: Icons.keyboard_double_arrow_right,
                                  onPressed: _currentPage < totalPages
                                      ? () => setState(
                                          () => _currentPage = totalPages)
                                      : null,
                                  isActive: _currentPage < totalPages,
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
          ),
        );
      },
    );
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 14 : 16,
      fontWeight: FontWeight.bold,
      color: AppColors.dark,
    );
  }

  void _toggleSort(String field) {
    setState(() {
      if (_sortField == field) {
        _sortAscending = !_sortAscending;
      } else {
        _sortField = field;
        _sortAscending = true;
      }
    });
    _subscribeToData();
  }

  DataRow _buildPendingRequestRow(OrderModel order, bool isSmallScreen) {
    final website = _websiteDetails[order.websiteId];

    return DataRow(
      cells: [
        DataCell(
          Text(
            order.orderId ?? 'N/A',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: AppColors.blue,
              decoration: TextDecoration.underline,
            ),
          ),
          onTap: () => _viewDetails(order),
        ),
        DataCell(
          Tooltip(
            message: website?.url ?? 'N/A',
            child: Text(
              website != null
                  ? (website.url.length > 20
                      ? '${website.url.substring(0, 17)}...'
                      : website.url)
                  : 'N/A',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: _buyerDetails[order.buyerId]?['email'] ?? 'N/A',
            child: Text(
              _getUserNameById(order.buyerId),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${order.totalPrice.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(order.orderDate),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 6),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'View Details',
                  child: IconButton(
                    icon: const Icon(Icons.visibility,
                        color: AppColors.blue, size: 18),
                    onPressed: () => _viewDetails(order),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(6),
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(right: 6),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'View Buyer Profile',
                  child: IconButton(
                    icon: const Icon(Icons.person,
                        color: AppColors.green, size: 18),
                    onPressed: () => showUserProfile(context, order.buyerId),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(6),
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(right: 6),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'Accept Order',
                  child: IconButton(
                    icon: const Icon(Icons.check_circle,
                        color: AppColors.green, size: 18),
                    onPressed: () => _changeStatus(order, 'Approved'),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(6),
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'Reject Order',
                  child: IconButton(
                    icon: const Icon(Icons.cancel,
                        color: AppColors.red, size: 18),
                    onPressed: () => _changeStatus(order, 'Decline'),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(6),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<DataRow> _buildDataRow(OrderModel order, bool isSmallScreen, int index) {
    final website = _websiteDetails[order.websiteId];

    final mainRow = DataRow(
      selected: _selectedOrderIds.contains(order.orderId),
      onSelectChanged: (selected) {
        setState(() {
          if (selected == true) {
            _selectedOrderIds.add(order.orderId!);
          } else {
            _selectedOrderIds.remove(order.orderId);
          }
        });
      },
      cells: [
        DataCell(
          Checkbox(
            value: _selectedOrderIds.contains(order.orderId),
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _selectedOrderIds.add(order.orderId!);
                } else {
                  _selectedOrderIds.remove(order.orderId);
                }
              });
            },
          ),
        ),
        DataCell(
          Text(
            order.orderId ?? 'N/A',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              color: AppColors.blue,
              decoration: TextDecoration.underline,
            ),
          ),
          onTap: () => _viewDetails(order),
        ),
        DataCell(
          Tooltip(
            message: website?.url ?? 'N/A',
            child: Text(
              website != null
                  ? (website.url.length > 20
                      ? '${website.url.substring(0, 17)}...'
                      : website.url)
                  : 'N/A',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: _buyerDetails[order.buyerId]?['email'] ?? 'N/A',
            child: Text(
              _getUserNameById(order.buyerId),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            _getUserNameById(order.publisherId),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${order.totalPrice.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: order.statusDetail ?? 'Status: ${order.status}',
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: _getLightColorForStatus(order.status),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: _getStatusColor(order.status).withAlpha(76)),
              ),
              child: DropdownButton<String>(
                value: [
                  'Pending',
                  'In Progress',
                  'Approved',
                  'Completed',
                  'Cancelled',
                  'Rejected'
                ].contains(order.status)
                    ? order.status
                    : 'Pending',
                items: [
                  'Pending',
                  'In Progress',
                  'Approved',
                  'Completed',
                  'Cancelled',
                  'Rejected'
                ]
                    .map((status) => DropdownMenuItem(
                          value: status,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _getStatusColor(status),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                status,
                                style: TextStyle(
                                  color: _getStatusColor(status),
                                  fontFamily: 'Space',
                                  fontWeight: FontWeight.w500,
                                  fontSize: isSmallScreen ? 12 : 14,
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
                onChanged: (value) => _changeStatus(order, value!),
                underline: const SizedBox(),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: _getStatusColor(order.status),
                ),
              ),
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: order.statusDetail != null
                ? order.statusDetail!
                : (order.actionBy != null
                    ? 'Last action by: ${order.actionBy}'
                    : 'No action recorded'),
            child: Text(
              order.disputeStatus ?? 'None',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
                color:
                    order.isDisputed ?? false ? AppColors.red : AppColors.grey,
              ),
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: order.actionTimestamp != null
                ? 'Last updated: ${_formatDate(order.actionTimestamp!)}'
                : 'Order date: ${_formatDate(order.orderDate)}',
            child: Text(
              _formatDate(order.orderDate),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'View Details',
                  child: IconButton(
                    icon: const Icon(Icons.visibility,
                        color: AppColors.blue, size: 20),
                    onPressed: () => _viewDetails(order),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'View Buyer Profile',
                  child: IconButton(
                    icon: const Icon(Icons.person,
                        color: AppColors.green, size: 20),
                    onPressed: () => showUserProfile(context, order.buyerId),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );

    return [mainRow];
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }

  Color _getLightColorForIcon(Color color) {
    if (color == AppColors.blue) {
      return const Color.fromRGBO(232, 240, 254, 1.0);
    } else if (color == AppColors.green) {
      return const Color.fromRGBO(232, 245, 233, 1.0);
    } else if (color == AppColors.yellow) {
      return const Color.fromRGBO(255, 243, 224, 1.0);
    } else if (color == AppColors.red) {
      return const Color.fromRGBO(255, 235, 238, 1.0);
    } else if (color == Colors.orange) {
      return const Color.fromRGBO(255, 236, 217, 1.0);
    } else {
      return const Color.fromRGBO(240, 240, 240, 1.0);
    }
  }

  Widget _buildPageButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isActive,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive
            ? _getLightColorForIcon(AppColors.blue)
            : const Color.fromRGBO(245, 245, 245, 1.0),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        color: isActive ? AppColors.blue : AppColors.grey,
        style: IconButton.styleFrom(
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'Completed':
        return AppColors.green;
      case 'Approved':
        return Colors.orange;
      case 'In Progress':
        return AppColors.yellow;
      case 'Pending':
        return AppColors.blue;
      case 'Cancelled':
      case 'Rejected':
        return AppColors.red;
      default:
        return AppColors.grey;
    }
  }

  Color _getLightColorForStatus(String? status) {
    switch (status) {
      case 'Completed':
        return const Color.fromRGBO(232, 245, 233, 1.0); // Light green
      case 'Approved':
        return const Color.fromRGBO(255, 236, 217, 1.0); // Light orange
      case 'In Progress':
        return const Color.fromRGBO(255, 243, 224, 1.0); // Light yellow
      case 'Pending':
        return const Color.fromRGBO(232, 240, 254, 1.0); // Light blue
      case 'Cancelled':
      case 'Rejected':
        return const Color.fromRGBO(255, 235, 238, 1.0); // Light red
      default:
        return const Color.fromRGBO(240, 240, 240, 1.0); // Light grey
    }
  }
}

void showUserProfile(BuildContext context, String uid) {
  showDialog(
    context: context,
    builder: (context) => UserProfileDialog(
      uid: uid,
      firestoreService: FirestoreService(),
    ),
  );
}

class OrderDetailsDialog extends StatefulWidget {
  final OrderModel order;
  final WebsiteModel? website;
  final String buyerName;
  final String buyerEmail;
  final UserModel? publisher;
  final Function(String) onChangeStatus;
  final Function(String?) onAssignPublisher;
  final Function(bool, String?, String?) onUpdateDispute;
  final FirestoreService firestoreService;

  const OrderDetailsDialog({
    super.key,
    required this.order,
    this.website,
    required this.buyerName,
    required this.buyerEmail,
    this.publisher,
    required this.onChangeStatus,
    required this.onAssignPublisher,
    required this.onUpdateDispute,
    required this.firestoreService,
  });

  @override
  State<OrderDetailsDialog> createState() => _OrderDetailsDialogState();
}

class _OrderDetailsDialogState extends State<OrderDetailsDialog> {
  late TextEditingController _disputeNoteController;
  String? _selectedStatus;
  String? _selectedPublisher;
  bool _isDisputed = false;
  String? _disputeStatus;

  @override
  void initState() {
    super.initState();
    _disputeNoteController =
        TextEditingController(text: widget.order.disputeNote);
    _selectedStatus = widget.order.status;
    _isDisputed = widget.order.isDisputed ?? false;
    _disputeStatus = widget.order.disputeStatus;
    _selectedPublisher = widget.order.publisherId;
  }

  @override
  void dispose() {
    _disputeNoteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final maxWidth = isSmallScreen ? double.infinity : 800.0;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth, maxHeight: 600),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(26, 115, 232, 0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 0,
            ),
          ],
          border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              decoration: BoxDecoration(
                color: _getLightColorForIcon(AppColors.blue),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20)),
                border: const Border(
                  bottom: BorderSide(
                    color: Color.fromRGBO(232, 240, 254, 1.0),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.receipt_long,
                          color: AppColors.blue,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Order Details - ${widget.order.orderId ?? 'N/A'}',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: isSmallScreen ? 18 : 22,
                          fontWeight: FontWeight.bold,
                          color: AppColors.dark,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.close, color: AppColors.grey),
                      onPressed: () => Navigator.pop(context),
                      tooltip: 'Close',
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('Website Information', isSmallScreen),
                    _buildDetailRow(
                      'URL',
                      widget.website?.url ?? 'N/A',
                      isUrl: true,
                      url: widget.website?.url,
                    ),
                    _buildDetailRow(
                        'Domain Name', widget.website?.domainName ?? 'N/A'),
                    _buildDetailRow('Language', widget.order.websiteLanguage),
                    _buildDetailRow(
                      'Categories',
                      widget.order.websiteCategories.join(', '),
                    ),
                    const SizedBox(height: 16),
                    _buildSectionTitle('Order Information', isSmallScreen),
                    _buildDetailRow('Post Title', widget.order.postTitle),
                    _buildDetailRow(
                        'Word Count', widget.order.wordCount.toString()),
                    _buildDetailRow('Backlink Type', widget.order.backlinkType),
                    _buildDetailRow(
                      'Sponsored',
                      widget.order.isSponsored ? 'Yes' : 'No',
                    ),
                    _buildDetailRow(
                      'Base Price',
                      '\$${widget.order.basePrice.toStringAsFixed(2)}',
                    ),
                    _buildDetailRow(
                      'Special Topics Price',
                      '\$${widget.order.specialTopicsAdditionalPrice.toStringAsFixed(2)}',
                    ),
                    _buildDetailRow(
                      'Total Price',
                      '\$${widget.order.totalPrice.toStringAsFixed(2)}',
                    ),
                    _buildDetailRow(
                        'Order Date', _formatDate(widget.order.orderDate)),
                    _buildDetailRow(
                        'Last Updated', _formatDate(widget.order.lastUpdated)),
                    _buildDetailRow('Approval Date',
                        _formatDate(widget.order.approvalDate)),
                    _buildDetailRow(
                      'Completion Date',
                      _formatDate(widget.order.completionDate),
                    ),
                    if (widget.order.inProgressDate != null)
                      _buildDetailRow(
                        'In Progress Date',
                        _formatDate(widget.order.inProgressDate),
                      ),
                    if (widget.order.statusDetail != null)
                      _buildDetailRow(
                        'Status Detail',
                        widget.order.statusDetail!,
                      ),
                    if (widget.order.actionBy != null)
                      _buildDetailRow(
                        'Last Action By',
                        widget.order.actionBy!,
                      ),
                    if (widget.order.actionTimestamp != null)
                      _buildDetailRow(
                        'Last Action Date',
                        _formatDate(widget.order.actionTimestamp),
                      ),
                    _buildDetailRow(
                        'Payment Status', widget.order.paymentStatus),
                    _buildDetailRow(
                        'Payment ID', widget.order.paymentId ?? 'N/A'),
                    _buildDetailRow('Notes', widget.order.notes ?? 'N/A'),
                    _buildDetailRow(
                      'Rejection Reason',
                      widget.order.rejectionReason ?? 'N/A',
                    ),
                    _buildDetailRow(
                      'Buyer',
                      '${widget.buyerName} (${widget.buyerEmail})',
                    ),
                    _buildDetailRow(
                      'Publisher',
                      widget.publisher?.name ?? 'N/A',
                    ),
                    const SizedBox(height: 16),
                    _buildSectionTitle('Post Content', isSmallScreen),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color.fromRGBO(26, 115, 232, 0.08),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                        border: Border.all(
                            color: const Color.fromRGBO(232, 240, 254, 0.8)),
                      ),
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: SingleChildScrollView(
                        child: Text(
                          widget.order.postContent.isEmpty
                              ? 'No content provided'
                              : widget.order.postContent,
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: isSmallScreen ? 13 : 14,
                            color: AppColors.dark,
                            height: 1.5,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildSectionTitle('Links', isSmallScreen),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color.fromRGBO(26, 115, 232, 0.08),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                        border: Border.all(
                            color: const Color.fromRGBO(232, 240, 254, 0.8)),
                      ),
                      child: widget.order.links.isEmpty
                          ? Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color:
                                        _getLightColorForIcon(AppColors.grey),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.link_off,
                                    color: AppColors.grey,
                                    size: 16,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text(
                                  'No links provided',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 14,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: widget.order.links
                                  .map((link) => Container(
                                        margin:
                                            const EdgeInsets.only(bottom: 8),
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8,
                                          horizontal: 12,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _getLightColorForIcon(
                                              AppColors.blue),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          children: [
                                            const Icon(
                                              Icons.link,
                                              color: AppColors.blue,
                                              size: 16,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: InkWell(
                                                onTap: () => _launchURL(link),
                                                child: Text(
                                                  link,
                                                  style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    color: AppColors.blue,
                                                    decoration: TextDecoration
                                                        .underline,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: const Icon(
                                                Icons.open_in_new,
                                                color: AppColors.blue,
                                                size: 16,
                                              ),
                                              onPressed: () => _launchURL(link),
                                              constraints:
                                                  const BoxConstraints(),
                                              padding: const EdgeInsets.all(8),
                                            ),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                            ),
                    ),
                    const SizedBox(height: 16),
                    _buildSectionTitle('Actions', isSmallScreen),
                    _buildDropdown(
                      label: 'Status',
                      value: [
                        'Pending',
                        'In Progress',
                        'Approved',
                        'Completed',
                        'Cancelled',
                        'Rejected'
                      ].contains(_selectedStatus)
                          ? _selectedStatus
                          : 'Pending',
                      items: [
                        'Pending',
                        'In Progress',
                        'Approved',
                        'Completed',
                        'Cancelled',
                        'Rejected'
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedStatus = value);
                          widget.onChangeStatus(value);
                        }
                      },
                      isSmallScreen: isSmallScreen,
                    ),
                    const SizedBox(height: 16),
                    _buildPublisherDropdown(isSmallScreen),
                    const SizedBox(height: 16),
                    _buildSectionTitle('Dispute', isSmallScreen),
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color.fromRGBO(26, 115, 232, 0.08),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                            spreadRadius: 0,
                          ),
                        ],
                        border: Border.all(
                          color: _isDisputed
                              ? const Color.fromRGBO(255, 235, 238, 0.8)
                              : const Color.fromRGBO(232, 240, 254, 0.8),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _isDisputed
                                      ? _getLightColorForIcon(AppColors.red)
                                      : _getLightColorForIcon(AppColors.grey),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  _isDisputed
                                      ? Icons.warning
                                      : Icons.check_circle,
                                  color: _isDisputed
                                      ? AppColors.red
                                      : AppColors.grey,
                                  size: 18,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Dispute Status',
                                style: TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: isSmallScreen ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.dark,
                                ),
                              ),
                              const Spacer(),
                              Switch(
                                value: _isDisputed,
                                activeColor: AppColors.red,
                                activeTrackColor:
                                    _getLightColorForIcon(AppColors.red),
                                onChanged: (value) {
                                  setState(() {
                                    _isDisputed = value;
                                    if (!_isDisputed) {
                                      _disputeStatus = null;
                                      _disputeNoteController.clear();
                                    } else {
                                      _disputeStatus = 'Open';
                                    }
                                  });
                                  widget.onUpdateDispute(
                                    _isDisputed,
                                    _disputeNoteController.text,
                                    _disputeStatus,
                                  );
                                },
                              ),
                            ],
                          ),
                          if (_isDisputed) ...[
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: _getLightColorForIcon(AppColors.red),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: TextField(
                                controller: _disputeNoteController,
                                decoration: const InputDecoration(
                                  labelText: 'Dispute Note',
                                  labelStyle: TextStyle(
                                    color: AppColors.red,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                    borderSide: BorderSide.none,
                                  ),
                                  filled: true,
                                  fillColor: Colors.white,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 12,
                                  ),
                                ),
                                maxLines: 3,
                                style: const TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 14,
                                ),
                                onChanged: (value) {
                                  widget.onUpdateDispute(
                                    _isDisputed,
                                    value,
                                    _disputeStatus,
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 4),
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        255, 235, 238, 1.0)),
                                borderRadius: BorderRadius.circular(10),
                                color: Colors.white,
                              ),
                              child: DropdownButton<String>(
                                value: ['Open', 'Under Review', 'Resolved']
                                        .contains(_disputeStatus)
                                    ? _disputeStatus
                                    : 'Open',
                                isExpanded: true,
                                underline: const SizedBox(),
                                icon: const Icon(Icons.arrow_drop_down,
                                    color: AppColors.red),
                                items: [
                                  DropdownMenuItem(
                                    value: 'Open',
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 8,
                                          height: 8,
                                          decoration: const BoxDecoration(
                                            color: AppColors.red,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Open',
                                          style: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: 14,
                                            color: AppColors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  DropdownMenuItem(
                                    value: 'Under Review',
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 8,
                                          height: 8,
                                          decoration: const BoxDecoration(
                                            color: Colors.orange,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Under Review',
                                          style: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: 14,
                                            color: Colors.orange,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  DropdownMenuItem(
                                    value: 'Resolved',
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 8,
                                          height: 8,
                                          decoration: const BoxDecoration(
                                            color: AppColors.green,
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Resolved',
                                          style: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: 14,
                                            color: AppColors.green,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() => _disputeStatus = value);
                                    widget.onUpdateDispute(
                                      _isDisputed,
                                      _disputeNoteController.text,
                                      value,
                                    );
                                  }
                                },
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: const Border(
                  top: BorderSide(
                    color: Color.fromRGBO(232, 240, 254, 1.0),
                    width: 1,
                  ),
                ),
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(20),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, size: 18),
                    label: const Text('Cancel'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.grey,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.check, size: 18),
                    label: const Text('Done'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      elevation: 0,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getLightColorForIcon(AppColors.blue),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getIconForSection(title),
              color: AppColors.blue,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconForSection(String section) {
    switch (section) {
      case 'Website Information':
        return Icons.language;
      case 'Order Information':
        return Icons.shopping_cart;
      case 'Post Content':
        return Icons.article;
      case 'Links':
        return Icons.link;
      case 'Actions':
        return Icons.settings;
      case 'Dispute':
        return Icons.warning;
      default:
        return Icons.info;
    }
  }

  Widget _buildDetailRow(String label, String value,
      {bool isUrl = false, String? url}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 150,
            padding: const EdgeInsets.only(right: 12),
            decoration: const BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Color.fromRGBO(232, 240, 254, 1.0),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: isUrl && url != null
                ? InkWell(
                    onTap: () => _launchURL(url),
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontFamily: 'Space',
                        fontSize: 14,
                        color: AppColors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  )
                : Text(
                    value,
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      color: AppColors.dark,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    required bool isSmallScreen,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(26, 115, 232, 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.blue),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.tune,
                  color: AppColors.blue,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              border:
                  Border.all(color: const Color.fromRGBO(232, 240, 254, 1.0)),
              borderRadius: BorderRadius.circular(10),
              color: Colors.white,
            ),
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              underline: const SizedBox(),
              icon: const Icon(Icons.arrow_drop_down, color: AppColors.blue),
              items: items
                  .map((item) => DropdownMenuItem(
                        value: item,
                        child: Text(
                          item,
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            color: AppColors.dark,
                          ),
                        ),
                      ))
                  .toList(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPublisherDropdown(bool isSmallScreen) {
    return FutureBuilder<QuerySnapshot>(
      future: FirebaseFirestore.instance
          .collection('users')
          .where('isPublisher', isEqualTo: true)
          .get(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final publishers = snapshot.data!.docs
            .map((doc) =>
                UserModel.fromMap(doc.data() as Map<String, dynamic>, doc.id))
            .toList();

        // Create a map of publisher UIDs to names for easier lookup
        final publisherMap = {for (var p in publishers) p.uid: p.name};

        // Set initial selected publisher based on the order's current publisher
        if (_selectedPublisher == null && widget.order.publisherId != null) {
          _selectedPublisher = widget.order.publisherId;
        }

        // Create dropdown items list
        final dropdownItems = <String>['Unassign'];
        dropdownItems.addAll(publishers.map((user) => user.name));

        // Get the display name for the dropdown
        String displayValue;
        if (_selectedPublisher == null) {
          displayValue = 'Unassign';
        } else {
          // Get the name from the map, or use a placeholder if not found
          final publisherName = publisherMap[_selectedPublisher];

          // Check if the publisher name exists in our dropdown items
          if (publisherName != null && dropdownItems.contains(publisherName)) {
            displayValue = publisherName;
          } else {
            // If the publisher exists but name isn't in dropdown items, add it temporarily
            if (publisherName != null) {
              dropdownItems.add(publisherName);
              displayValue = publisherName;
            } else {
              // If we can't find the publisher name at all, use a placeholder
              displayValue = 'Unassign';
              // Reset the selected publisher to null since we can't find it
              _selectedPublisher = null;
            }
          }
        }

        return _buildDropdown(
          label: 'Assign Publisher',
          value: displayValue,
          items: dropdownItems,
          onChanged: (value) {
            if (value == 'Unassign') {
              setState(() => _selectedPublisher = null);
              widget.onAssignPublisher(null);
            } else {
              // Find the publisher by name
              final selectedUser = publishers.firstWhere(
                (user) => user.name == value,
                // If not found (shouldn't happen with our fixes), keep current selection or use first
                orElse: () => publishers.isNotEmpty
                    ? publishers.first
                    : UserModel(
                        uid: 'placeholder',
                        name: 'Unknown',
                        email: '',
                        emailVerified: false,
                        mobileNumber: '',
                        profilePictureUrl: '',
                        isPublisher: true,
                        additionalDetails: {},
                        assignedOrderIds: [],
                      ),
              );
              setState(() => _selectedPublisher = selectedUser.uid);
              widget.onAssignPublisher(selectedUser.uid);
            }
          },
          isSmallScreen: isSmallScreen,
        );
      },
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Could not launch $url'),
              backgroundColor: AppColors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  Color _getLightColorForIcon(Color color) {
    if (color == AppColors.blue) {
      return const Color.fromRGBO(232, 240, 254, 1.0);
    } else if (color == AppColors.green) {
      return const Color.fromRGBO(232, 245, 233, 1.0);
    } else if (color == AppColors.yellow) {
      return const Color.fromRGBO(255, 243, 224, 1.0);
    } else if (color == AppColors.red) {
      return const Color.fromRGBO(255, 235, 238, 1.0);
    } else if (color == Colors.orange) {
      return const Color.fromRGBO(255, 236, 217, 1.0);
    } else {
      return const Color.fromRGBO(240, 240, 240, 1.0);
    }
  }
}
