import 'package:flutter/material.dart';

class AppColors {
  // Background colors
  static Color backgroundColor = const Color(0xFFFFFFFF);
  static Color componentBackColor = const Color(0xFFF8F9FC);
  static Color componentBackColor2 = const Color(0xFFF0F3FA);


  static const Color primaryGreen = Color(0xFF34C759);

  static const Color greyDark = Color(0xFF636366);
  // static const Color backgroundColor = Color(0xFFF2F2F7); // Example background



  static const Color primaryColor = Color(0xFF2563EB); // Rich blue
  static const Color secondaryColor = Color(0xFF4F46E5); // Indigo
  static const Color accentColor = Color(0xFF2563EB); // Rich blue
  static const Color accentGradientStart = Color(0xFF2563EB); // Rich blue
  static const Color accentGradientEnd = Color(0xFF3B82F6); // Lighter blue

  // Text colors
  static const Color textPrimary = Color(0xFF0F172A); // Very dark blue/slate
  static const Color textSecondary = Color(0xFF475569); // Slate
  static const Color textLight = Color(0xFF94A3B8); // Light slate
  // Text colors
  static Color black = const Color(0xFF0D0D0D);
  static Color greyBack = const Color(0xFFE3E3E3);
  static Color greyText = const Color(0xFFB8B8B8);

  // Primary colors
  static Color primaryBlue = const Color(0xFF1A73E8);
  static Color secondryColor = const Color(0xFF1A73E8);
  static Color clickBlue = const Color(0xFF0B57D0);

  // Standard colors
  static const dark = Color(0xFF212121);
  static const light = Color(0xFFF5F5F5);
  static const blue = Color(0xFF1A73E8);
  static const green = Color(0xFF34A853);
  static const red = Color(0xFFEA4335);
  static const yellow = Color(0xFFFBBC05);
  static const grey = Color(0xFF9AA0A6);

  // Blue palette
  static const blueLightest = Color(0xFFE8F0FE);
  static const blueLight = Color(0xFFC2D7FF);
  static const blueMedium = Color(0xFF4285F4);
  static const blueDark = Color(0xFF1967D2);
  static const blueDarkest = Color(0xFF174EA6);

  // Card gradients
  static LinearGradient blueGradient = LinearGradient(
    colors: [blueLightest, Color.fromRGBO(194, 215, 255, 0.6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient greenGradient = LinearGradient(
    colors: [
      Color.fromRGBO(52, 168, 83, 0.1),
      Color.fromRGBO(52, 168, 83, 0.3)
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient yellowGradient = LinearGradient(
    colors: [
      Color.fromRGBO(251, 188, 5, 0.1),
      Color.fromRGBO(251, 188, 5, 0.3)
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient redGradient = LinearGradient(
    colors: [
      Color.fromRGBO(234, 67, 53, 0.1),
      Color.fromRGBO(234, 67, 53, 0.3)
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
