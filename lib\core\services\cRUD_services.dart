// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'dart:async';

// class FirestoreService {
//   static final FirestoreService _instance = FirestoreService._internal();
//   factory FirestoreService() => _instance;
//   FirestoreService._internal();

//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;

//   CollectionReference<Map<String, dynamic>> _getCollection(
//       String collectionPath) {
//     return _firestore.collection(collectionPath);
//   }

//   /// CREATE Operations
//   Future<String> addDocument(
//       {required String collectionPath,
//       required Map<String, dynamic> data}) async {
//     try {
//       DocumentReference docRef = await _getCollection(collectionPath).add({
//         ...data,
//         'createdAt': FieldValue.serverTimestamp(),
//         'updatedAt': FieldValue.serverTimestamp(),
//       });
//       return docRef.id;
//     } catch (e) {
//       throw Exception('Error adding document: $e');
//     }
//   }

//   Future<void> setDocument(
//       {required String collectionPath,
//       required String documentId,
//       required Map<String, dynamic> data,
//       bool merge = false}) async {
//     try {
//       await _getCollection(collectionPath).doc(documentId).set({
//         ...data,
//         'createdAt': FieldValue.serverTimestamp(),
//         'updatedAt': FieldValue.serverTimestamp(),
//       }, SetOptions(merge: merge));
//     } catch (e) {
//       throw Exception('Error setting document: $e');
//     }
//   }

//   /// READ Operations
//   Future<Map<String, dynamic>?> getDocument(
//       {required String collectionPath, required String documentId}) async {
//     try {
//       DocumentSnapshot<Map<String, dynamic>> snapshot =
//           await _getCollection(collectionPath).doc(documentId).get();
//       return snapshot.exists ? snapshot.data() : null;
//     } catch (e) {
//       throw Exception('Error getting document: $e');
//     }
//   }

//   Stream<DocumentSnapshot<Map<String, dynamic>>> streamDocument(
//       {required String collectionPath, required String documentId}) {
//     try {
//       return _getCollection(collectionPath).doc(documentId).snapshots();
//     } catch (e) {
//       throw Exception('Error streaming document: $e');
//     }
//   }

//   Stream<QuerySnapshot<Map<String, dynamic>>> streamCollection(
//       {required String collectionPath,
//       Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
//           queryBuilder}) {
//     try {
//       Query<Map<String, dynamic>> query = _getCollection(collectionPath);
//       if (queryBuilder != null) {
//         query = queryBuilder(query);
//       }
//       return query.snapshots();
//     } catch (e) {
//       throw Exception('Error streaming collection: $e');
//     }
//   }

//   Future<List<Map<String, dynamic>>> getCollection(
//       {required String collectionPath,
//       Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
//           queryBuilder}) async {
//     try {
//       Query<Map<String, dynamic>> query = _getCollection(collectionPath);
//       if (queryBuilder != null) {
//         query = queryBuilder(query);
//       }
//       QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
//       return snapshot.docs.map((doc) => doc.data()).toList();
//     } catch (e) {
//       throw Exception('Error getting collection: $e');
//     }
//   }

//   // New method for order/transaction history
//   Stream<QuerySnapshot<Map<String, dynamic>>> streamSubCollection(
//       {required String collectionPath,
//       required String documentId,
//       required String subCollectionPath}) {
//     try {
//       return _getCollection(collectionPath)
//           .doc(documentId)
//           .collection(subCollectionPath)
//           .snapshots();
//     } catch (e) {
//       throw Exception('Error streaming subcollection: $e');
//     }
//   }

//   /// UPDATE Operations
//   Future<void> updateDocument(
//       {required String collectionPath,
//       required String documentId,
//       required Map<String, dynamic> data}) async {
//     try {
//       await _getCollection(collectionPath).doc(documentId).update({
//         ...data,
//         'updatedAt': FieldValue.serverTimestamp(),
//       });
//     } catch (e) {
//       throw Exception('Error updating document: $e');
//     }
//   }

//   /// DELETE Operations
//   Future<void> deleteDocument(
//       {required String collectionPath, required String documentId}) async {
//     try {
//       await _getCollection(collectionPath).doc(documentId).delete();
//     } catch (e) {
//       throw Exception('Error deleting document: $e');
//     }
//   }

//   /// Batch Operations
//   Future<void> batchUpdate(
//       {required List<Map<String, dynamic>> operations}) async {
//     try {
//       WriteBatch batch = _firestore.batch();
//       for (var operation in operations) {
//         final docRef = _getCollection(operation['collectionPath'])
//             .doc(operation['documentId']);
//         switch (operation['type']) {
//           case 'set':
//             batch.set(docRef, operation['data'], operation['options']);
//             break;
//           case 'update':
//             batch.update(docRef, operation['data']);
//             break;
//           case 'delete':
//             batch.delete(docRef);
//             break;
//         }
//       }
//       await batch.commit();
//     } catch (e) {
//       throw Exception('Error in batch operation: $e');
//     }
//   }

//   /// Transaction Operations
//   Future<T> runTransaction<T>(
//       {required Future<T> Function(Transaction) transactionHandler}) async {
//     try {
//       return await _firestore.runTransaction(transactionHandler);
//     } catch (e) {
//       throw Exception('Error in transaction: $e');
//     }
//   }
// }


// class AdminService {
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;

// final String _collectionPath = 'admin-collection';
//   /// Stream admin fees from Firestore
//   Stream<Map<String, dynamic>> getFees() {
//     return _firestore.collection('admin_settings').doc('fees').snapshots().map(
//           (snapshot) => snapshot.exists
//               ? snapshot.data()!
//               : {
//                   'paypalFee': 0.04, // Default 4%
//                   'adsyFee': 0.039, // Default 3.9%
//                   'minimumPayout': 60.0, // Default $60
//                 },
//         );
//   }

//   /// Update admin fees (for admin dashboard use)
//   Future<void> updateFees({
//     required double paypalFee,
//     required double adsyFee,
//     required double minimumPayout,
//   }) async {
//     try {
//       await _firestore.collection('admin_settings').doc('fees').set({
//         'paypalFee': paypalFee,
//         'adsyFee': adsyFee,
//         'minimumPayout': minimumPayout,
//         'updatedAt': FieldValue.serverTimestamp(),
//       }, SetOptions(merge: true));
//     } catch (e) {
//       throw Exception('Error updating fees: $e');
//     }
//   }
// }



import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  CollectionReference<Map<String, dynamic>> _getCollection(
      String collectionPath) {
    return _firestore.collection(collectionPath);
  }

  /// CREATE Operations
  Future<String> addDocument(
      {required String collectionPath,
      required Map<String, dynamic> data}) async {
    try {
      DocumentReference docRef = await _getCollection(collectionPath).add({
        ...data,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      throw Exception('Error adding document: $e');
    }
  }

  Future<void> setDocument(
      {required String collectionPath,
      required String documentId,
      required Map<String, dynamic> data,
      bool merge = false}) async {
    try {
      await _getCollection(collectionPath).doc(documentId).set({
        ...data,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: merge));
    } catch (e) {
      throw Exception('Error setting document: $e');
    }
  }

  /// READ Operations
  Future<Map<String, dynamic>?> getDocument(
      {required String collectionPath, required String documentId}) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> snapshot =
          await _getCollection(collectionPath).doc(documentId).get();
      return snapshot.exists ? snapshot.data() : null;
    } catch (e) {
      throw Exception('Error getting document: $e');
    }
  }

  Stream<DocumentSnapshot<Map<String, dynamic>>> streamDocument(
      {required String collectionPath, required String documentId}) {
    try {
      return _getCollection(collectionPath).doc(documentId).snapshots();
    } catch (e) {
      throw Exception('Error streaming document: $e');
    }
  }

  Stream<QuerySnapshot<Map<String, dynamic>>> streamCollection(
      {required String collectionPath,
      Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
          queryBuilder}) {
    try {
      Query<Map<String, dynamic>> query = _getCollection(collectionPath);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      return query.snapshots();
    } catch (e) {
      throw Exception('Error streaming collection: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getCollection(
      {required String collectionPath,
      Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
          queryBuilder}) async {
    try {
      Query<Map<String, dynamic>> query = _getCollection(collectionPath);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
      return snapshot.docs.map((doc) => {...doc.data(), 'id': doc.id}).toList();
    } catch (e) {
      throw Exception('Error getting collection: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getSubCollection({
    required String collectionPath,
    required String documentId,
    required String subCollectionPath,
    Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
        queryBuilder,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _getCollection(collectionPath)
          .doc(documentId)
          .collection(subCollectionPath);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      QuerySnapshot<Map<String, dynamic>> snapshot = await query.get();
      return snapshot.docs.map((doc) => {...doc.data(), 'id': doc.id}).toList();
    } catch (e) {
      throw Exception('Error getting subcollection: $e');
    }
  }

  Stream<QuerySnapshot<Map<String, dynamic>>> streamSubCollection({
    required String collectionPath,
    required String documentId,
    required String subCollectionPath,
    Query<Map<String, dynamic>> Function(Query<Map<String, dynamic>>)?
        queryBuilder,
  }) {
    try {
      Query<Map<String, dynamic>> query = _getCollection(collectionPath)
          .doc(documentId)
          .collection(subCollectionPath);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      return query.snapshots();
    } catch (e) {
      throw Exception('Error streaming subcollection: $e');
    }
  }

  /// UPDATE Operations
  Future<void> updateDocument(
      {required String collectionPath,
      required String documentId,
      required Map<String, dynamic> data}) async {
    try {
      await _getCollection(collectionPath).doc(documentId).update({
        ...data,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Error updating document: $e');
    }
  }

  /// DELETE Operations
  Future<void> deleteDocument(
      {required String collectionPath, required String documentId}) async {
    try {
      await _getCollection(collectionPath).doc(documentId).delete();
    } catch (e) {
      throw Exception('Error deleting document: $e');
    }
  }

  /// Batch Operations
  Future<void> batchUpdate(
      {required List<Map<String, dynamic>> operations}) async {
    try {
      WriteBatch batch = _firestore.batch();
      for (var operation in operations) {
        final docRef = _getCollection(operation['collectionPath'])
            .doc(operation['documentId']);
        switch (operation['type']) {
          case 'set':
            batch.set(docRef, operation['data'], operation['options']);
            break;
          case 'update':
            batch.update(docRef, operation['data']);
            break;
          case 'delete':
            batch.delete(docRef);
            break;
        }
      }
      await batch.commit();
    } catch (e) {
      throw Exception('Error in batch operation: $e');
    }
  }

  /// Transaction Operations
  Future<T> runTransaction<T>(
      {required Future<T> Function(Transaction) transactionHandler}) async {
    try {
      return await _firestore.runTransaction(transactionHandler);
    } catch (e) {
      throw Exception('Error in transaction: $e');
    }
  }
}
