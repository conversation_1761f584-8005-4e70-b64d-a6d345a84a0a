import 'package:cloud_firestore/cloud_firestore.dart';

class OfferModel {
  final String id;
  final String title;
  final String description;
  final double percentage;
  final double minAmount;
  final bool isActive;
  final Timestamp? validUntil;
  final Timestamp createdAt;

  OfferModel({
    required this.id,
    required this.title,
    required this.description,
    required this.percentage,
    required this.minAmount,
    required this.isActive,
    this.validUntil,
    required this.createdAt,
  });

  factory OfferModel.fromMap(Map<String, dynamic> map, String id) {
    return OfferModel(
      id: id,
      title: map['title'] ?? 'Special Offer',
      description: map['description'] ?? 'Add funds and get bonus',
      percentage: (map['percentage'] as num?)?.toDouble() ?? 0.0,
      minAmount: (map['minAmount'] as num?)?.toDouble() ?? 0.0,
      isActive: map['isActive'] ?? false,
      validUntil: map['validUntil'] as Timestamp?,
      createdAt: map['createdAt'] as Timestamp? ?? Timestamp.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'percentage': percentage,
      'minAmount': minAmount,
      'isActive': isActive,
      'validUntil': validUntil,
      'createdAt': createdAt,
    };
  }

  // Calculate bonus amount based on deposit amount
  double calculateBonus(double amount) {
    if (amount >= minAmount) {
      return amount * (percentage / 100);
    }
    return 0.0;
  }

  // Check if offer is valid based on validUntil date
  bool isValid() {
    if (!isActive) return false;
    if (validUntil == null) return true;
    return validUntil!.toDate().isAfter(DateTime.now());
  }
}
