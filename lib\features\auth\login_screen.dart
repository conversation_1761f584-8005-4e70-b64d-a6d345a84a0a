// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:guest_posts_buyer/core/services/auth_service.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  void _login() async {
    bool success = await AuthService().loginWithEmail(
      emailController.text,
      passwordController.text,
    );
    if (success) context.go('/home');
  }

  Color _currentColor = const Color.fromARGB(255, 150, 0, 0);
  double _leftPosition = -5;
  double _bottomPosition = -10;

  // Lists of shapes and colors to cycle through
  final List<Color> _colors = [
    Colors.amber,
    const Color.fromARGB(255, 224, 169, 5),
    Colors.blue,
    Colors.green,
    Colors.purple,
  ];

  int _colorIndex = 0;

  bool _rememberMe = false;
  bool _showPass = false;

  // Timer for automatic changes
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    // Start the timer to change every 2 seconds
    _timer = Timer.periodic(Duration(seconds: 3), (timer) {
      _changeAppearance();
    });
  }

  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer when the widget is disposed
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  // Function to update shape, color, and position
  void _changeAppearance() {
    setState(() {
      _colorIndex = (_colorIndex + 1) % _colors.length;
      _currentColor = _colors[_colorIndex];
      _leftPosition += 10; // Move horizontally
      _bottomPosition += 5; // Move vertically

      // Optional: Reset position if it goes off-screen
      if (_leftPosition > MediaQuery.of(context).size.width) {
        _leftPosition = -5;
      }
      if (_bottomPosition > MediaQuery.of(context).size.height) {
        _bottomPosition = -10;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Gradient background with blur

          Positioned(
            left: _leftPosition,
            bottom: _bottomPosition,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            right: 300,
            top: 300,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          Positioned(
            left: _leftPosition,
            top: _bottomPosition,
            child: AnimatedContainer(
              duration: Duration(seconds: 2),
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentColor,
              ),
            ),
          ),
          // Partial blurred background (e.g., top section)
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 100.0, sigmaY: 100.0),
            child: Container(
              color: const Color.fromARGB(143, 255, 255, 255),
            ),
          ),
          // Main content
          Center(
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: SizedBox(
                  width: 400,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: const Color.fromARGB(115, 51, 51, 51),
                        child: Text(
                          'GP',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'cairo'),
                        ),
                      ),
                      SizedBox(height: 100),
                      // Log In text
                      Text(
                        'Login To Admin Dashboard',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Alatsi',
                          color: const Color.fromARGB(255, 0, 0, 0),
                        ),
                      ),
                      SizedBox(height: 30),
                      // Social login buttons

                      // Email field
                      SizedBox(
                        width: 400,
                        height: 50,
                        child: TextField(
                          controller: emailController,
                          decoration: InputDecoration(
                            hintText: 'Email',
                            filled: true,
                            fillColor: AppColors.componentBackColor,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      // Password field
                      SizedBox(
                        width: 400,
                        height: 50,
                        child: TextField(
                          controller: passwordController,
                          obscureText: _showPass ? false : true,
                          decoration: InputDecoration(
                            hintText: 'Password',
                            filled: true,
                            fillColor: AppColors.componentBackColor,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide.none,
                            ),
                            suffix: InkWell(
                                onTap: () {
                                  setState(() {
                                    _showPass = !_showPass;
                                  });
                                },
                                child: Text(_showPass ? 'Hide' : 'Show')),
                            suffixStyle:
                                TextStyle(color: Colors.amber, fontSize: 18),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      // Remember me and Lost password
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: (value) {
                                  setState(() {
                                    _rememberMe = value!;
                                  });
                                },
                                checkColor: Colors.amber,
                                shape: RoundedRectangleBorder(
                                    side: BorderSide(),
                                    borderRadius: BorderRadius.circular(3)),
                                activeColor: Colors.amber,
                              ),
                              Text('Remember me'),
                            ],
                          ),
                          TextButton(
                            onPressed: () {},
                            child: Text(
                              'Lost password?',
                              style: TextStyle(
                                color: Colors.amber,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      // Log In button
                      SizedBox(
                        width: 400,
                        height: 50,
                        child: ElevatedButton(
                          onPressed: () {
                            _login();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black87,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                            padding: EdgeInsets.symmetric(
                                horizontal: 40, vertical: 15),
                          ),
                          child: Text(
                            'Log In',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      // Create new account
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Cookie settings button (bottom-left)
          Positioned(
            bottom: 20,
            left: 20,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.black,
                backgroundColor: Colors.white,
                side: BorderSide(color: Colors.grey.shade300),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text('COOKIE SETTINGS'),
            ),
          ),
        ],
      ),
    );
  }
}
