import 'package:cloud_firestore/cloud_firestore.dart';

enum InvoiceType {
  deposit,
  order,
  withdrawal,
}

class InvoiceModel {
  final String? invoiceId;
  final String userId;
  final String userEmail;
  final String userName;
  final InvoiceType invoiceType;
  final String transactionId;
  final double amount;
  final double? fees;
  final double totalAmount;
  final String paymentMethod;
  final Timestamp createdAt;
  final String status;
  final String? orderId;
  final Map<String, dynamic>? orderDetails;
  final String? withdrawalId;
  final Map<String, dynamic>? withdrawalDetails;
  final String? depositId;
  final Map<String, dynamic>? depositDetails;
  final String? pdfUrl;
  final String? companyName;
  final String? companyAddress;
  final String? companyLogo;
  final String? companyEmail;
  final String? companyPhone;
  final String? companyWebsite;
  final String? companyVatNumber;
  final String? companyRegistrationNumber;

  InvoiceModel({
    this.invoiceId,
    required this.userId,
    required this.userEmail,
    required this.userName,
    required this.invoiceType,
    required this.transactionId,
    required this.amount,
    this.fees,
    required this.totalAmount,
    required this.paymentMethod,
    required this.createdAt,
    required this.status,
    this.orderId,
    this.orderDetails,
    this.withdrawalId,
    this.withdrawalDetails,
    this.depositId,
    this.depositDetails,
    this.pdfUrl,
    this.companyName,
    this.companyAddress,
    this.companyLogo,
    this.companyEmail,
    this.companyPhone,
    this.companyWebsite,
    this.companyVatNumber,
    this.companyRegistrationNumber,
  });

  factory InvoiceModel.fromMap(Map<String, dynamic> map, {String? id}) {
    return InvoiceModel(
      invoiceId: id ?? map['invoiceId'],
      userId: map['userId'] ?? '',
      userEmail: map['userEmail'] ?? '',
      userName: map['userName'] ?? '',
      invoiceType: _stringToInvoiceType(map['invoiceType'] ?? 'deposit'),
      transactionId: map['transactionId'] ?? '',
      amount: (map['amount'] as num?)?.toDouble() ?? 0.0,
      fees: (map['fees'] as num?)?.toDouble(),
      totalAmount: (map['totalAmount'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: map['paymentMethod'] ?? '',
      createdAt: map['createdAt'] is Timestamp
          ? map['createdAt']
          : Timestamp.fromDate(DateTime.now()),
      status: map['status'] ?? 'Pending',
      orderId: map['orderId'],
      orderDetails: map['orderDetails'],
      withdrawalId: map['withdrawalId'],
      withdrawalDetails: map['withdrawalDetails'],
      depositId: map['depositId'],
      depositDetails: map['depositDetails'],
      pdfUrl: map['pdfUrl'],
      companyName: map['companyName'],
      companyAddress: map['companyAddress'],
      companyLogo: map['companyLogo'],
      companyEmail: map['companyEmail'],
      companyPhone: map['companyPhone'],
      companyWebsite: map['companyWebsite'],
      companyVatNumber: map['companyVatNumber'],
      companyRegistrationNumber: map['companyRegistrationNumber'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'invoiceId': invoiceId,
      'userId': userId,
      'userEmail': userEmail,
      'userName': userName,
      'invoiceType': _invoiceTypeToString(invoiceType),
      'transactionId': transactionId,
      'amount': amount,
      'fees': fees,
      'totalAmount': totalAmount,
      'paymentMethod': paymentMethod,
      'createdAt': createdAt,
      'status': status,
      'orderId': orderId,
      'orderDetails': orderDetails,
      'withdrawalId': withdrawalId,
      'withdrawalDetails': withdrawalDetails,
      'depositId': depositId,
      'depositDetails': depositDetails,
      'pdfUrl': pdfUrl,
      'companyName': companyName,
      'companyAddress': companyAddress,
      'companyLogo': companyLogo,
      'companyEmail': companyEmail,
      'companyPhone': companyPhone,
      'companyWebsite': companyWebsite,
      'companyVatNumber': companyVatNumber,
      'companyRegistrationNumber': companyRegistrationNumber,
    };
  }

  static InvoiceType _stringToInvoiceType(String type) {
    switch (type.toLowerCase()) {
      case 'order':
        return InvoiceType.order;
      case 'withdrawal':
        return InvoiceType.withdrawal;
      case 'deposit':
      default:
        return InvoiceType.deposit;
    }
  }

  static String _invoiceTypeToString(InvoiceType type) {
    switch (type) {
      case InvoiceType.order:
        return 'order';
      case InvoiceType.withdrawal:
        return 'withdrawal';
      case InvoiceType.deposit:
        return 'deposit';
    }
  }
}
