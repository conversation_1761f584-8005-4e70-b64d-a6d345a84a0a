import 'package:cloud_firestore/cloud_firestore.dart';

class OrderModel {
  final String? orderId;
  final String websiteId;
  final String buyerId;
  final String websiteUrl;
  final String websiteDomainName;
  final String websiteLanguage;
  final List<String> websiteCategories;
  final String postTitle;
  final String postContent;
  final int wordCount;
  final List<String> links;
  final String backlinkType;
  final bool isSponsored;
  final double basePrice;
  final double specialTopicsAdditionalPrice;
  final double totalPrice;
  final String status;
  final String? statusDetail; // Added: Detailed status description
  final Timestamp orderDate;
  final Timestamp? lastUpdated;
  final Timestamp? approvalDate;
  final Timestamp? completionDate;
  final Timestamp?
      inProgressDate; // Added: Date when order was marked as "In Progress"
  final String paymentStatus;
  final String? paymentId;
  final String? notes;
  final String? publisherId; // Changed to nullable
  final String? rejectionReason;
  final bool? isDisputed;
  final String? disputeNote;
  final String? disputeStatus;
  final String? actionBy; // Added: Who performed the last status change
  final Timestamp?
      actionTimestamp; // Added: When the last status change occurred

  OrderModel({
    this.orderId,
    required this.websiteId,
    required this.buyerId,
    required this.websiteUrl,
    required this.websiteDomainName,
    required this.websiteLanguage,
    required this.websiteCategories,
    required this.postTitle,
    required this.postContent,
    required this.wordCount,
    required this.links,
    required this.backlinkType,
    required this.isSponsored,
    required this.basePrice,
    required this.specialTopicsAdditionalPrice,
    required this.totalPrice,
    required this.status,
    this.statusDetail,
    required this.orderDate,
    this.lastUpdated,
    this.approvalDate,
    this.completionDate,
    this.inProgressDate,
    required this.paymentStatus,
    this.paymentId,
    this.notes,
    this.publisherId,
    this.rejectionReason,
    this.isDisputed,
    this.disputeNote,
    this.disputeStatus,
    this.actionBy,
    this.actionTimestamp,
  });

  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      orderId: map['orderId'] as String?,
      websiteId: map['websiteId'] as String? ?? '',
      buyerId: map['buyerId'] as String? ?? '',
      websiteUrl: map['websiteUrl'] as String? ?? '',
      websiteDomainName: map['websiteDomainName'] as String? ?? '',
      websiteLanguage: map['websiteLanguage'] as String? ?? '',
      websiteCategories: List<String>.from(map['websiteCategories'] ?? []),
      postTitle: map['postTitle'] as String? ?? '',
      postContent: map['postContent'] as String? ?? '',
      wordCount: map['wordCount'] as int? ?? 0,
      links: List<String>.from(map['links'] ?? []),
      backlinkType: map['backlinkType'] as String? ?? '',
      isSponsored: map['isSponsored'] as bool? ?? false,
      basePrice: (map['basePrice'] as num?)?.toDouble() ?? 0.0,
      specialTopicsAdditionalPrice:
          (map['specialTopicsAdditionalPrice'] as num?)?.toDouble() ?? 0.0,
      totalPrice: (map['totalPrice'] as num?)?.toDouble() ?? 0.0,
      status: map['status'] as String? ?? 'Pending',
      statusDetail: map['statusDetail'] as String?,
      orderDate: map['orderDate'] as Timestamp? ?? Timestamp.now(),
      lastUpdated: map['lastUpdated'] as Timestamp?,
      approvalDate: map['approvalDate'] as Timestamp?,
      completionDate: map['completionDate'] as Timestamp?,
      inProgressDate: map['inProgressDate'] as Timestamp?,
      paymentStatus: map['paymentStatus'] as String? ?? '',
      paymentId: map['paymentId'] as String?,
      notes: map['notes'] as String?,
      publisherId: map['publisherId'] as String?,
      rejectionReason: map['rejectionReason'] as String?,
      isDisputed: map['isDisputed'] as bool?,
      disputeNote: map['disputeNote'] as String?,
      disputeStatus: map['disputeStatus'] as String?,
      actionBy: map['actionBy'] as String?,
      actionTimestamp: map['actionTimestamp'] as Timestamp?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'orderId': orderId,
      'websiteId': websiteId,
      'buyerId': buyerId,
      'websiteUrl': websiteUrl,
      'websiteDomainName': websiteDomainName,
      'websiteLanguage': websiteLanguage,
      'websiteCategories': websiteCategories,
      'postTitle': postTitle,
      'postContent': postContent,
      'wordCount': wordCount,
      'links': links,
      'backlinkType': backlinkType,
      'isSponsored': isSponsored,
      'basePrice': basePrice,
      'specialTopicsAdditionalPrice': specialTopicsAdditionalPrice,
      'totalPrice': totalPrice,
      'status': status,
      'statusDetail': statusDetail,
      'orderDate': orderDate,
      'lastUpdated': lastUpdated,
      'approvalDate': approvalDate,
      'completionDate': completionDate,
      'inProgressDate': inProgressDate,
      'paymentStatus': paymentStatus,
      'paymentId': paymentId,
      'notes': notes,
      'publisherId': publisherId,
      'rejectionReason': rejectionReason,
      'isDisputed': isDisputed,
      'disputeNote': disputeNote,
      'disputeStatus': disputeStatus,
      'actionBy': actionBy,
      'actionTimestamp': actionTimestamp,
    };
  }

  OrderModel copyWith({
    String? orderId,
    String? websiteId,
    String? buyerId,
    String? websiteUrl,
    String? websiteDomainName,
    String? websiteLanguage,
    List<String>? websiteCategories,
    String? postTitle,
    String? postContent,
    int? wordCount,
    List<String>? links,
    String? backlinkType,
    bool? isSponsored,
    double? basePrice,
    double? specialTopicsAdditionalPrice,
    double? totalPrice,
    String? status,
    String? statusDetail,
    Timestamp? orderDate,
    Timestamp? lastUpdated,
    Timestamp? approvalDate,
    Timestamp? completionDate,
    Timestamp? inProgressDate,
    String? paymentStatus,
    String? paymentId,
    String? notes,
    String? publisherId,
    String? rejectionReason,
    bool? isDisputed,
    String? disputeNote,
    String? disputeStatus,
    String? actionBy,
    Timestamp? actionTimestamp,
  }) {
    return OrderModel(
      orderId: orderId ?? this.orderId,
      websiteId: websiteId ?? this.websiteId,
      buyerId: buyerId ?? this.buyerId,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      websiteDomainName: websiteDomainName ?? this.websiteDomainName,
      websiteLanguage: websiteLanguage ?? this.websiteLanguage,
      websiteCategories: websiteCategories ?? this.websiteCategories,
      postTitle: postTitle ?? this.postTitle,
      postContent: postContent ?? this.postContent,
      wordCount: wordCount ?? this.wordCount,
      links: links ?? this.links,
      backlinkType: backlinkType ?? this.backlinkType,
      isSponsored: isSponsored ?? this.isSponsored,
      basePrice: basePrice ?? this.basePrice,
      specialTopicsAdditionalPrice:
          specialTopicsAdditionalPrice ?? this.specialTopicsAdditionalPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      statusDetail: statusDetail ?? this.statusDetail,
      orderDate: orderDate ?? this.orderDate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      approvalDate: approvalDate ?? this.approvalDate,
      completionDate: completionDate ?? this.completionDate,
      inProgressDate: inProgressDate ?? this.inProgressDate,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentId: paymentId ?? this.paymentId,
      notes: notes ?? this.notes,
      publisherId: publisherId ?? this.publisherId,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      isDisputed: isDisputed ?? this.isDisputed,
      disputeNote: disputeNote ?? this.disputeNote,
      disputeStatus: disputeStatus ?? this.disputeStatus,
      actionBy: actionBy ?? this.actionBy,
      actionTimestamp: actionTimestamp ?? this.actionTimestamp,
    );
  }
}
