import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class AdminSettingsPage extends StatefulWidget {
  const AdminSettingsPage({Key? key}) : super(key: key);

  @override
  _AdminSettingsPageState createState() => _AdminSettingsPageState();
}

class _AdminSettingsPageState extends State<AdminSettingsPage> {
  // Default values
  final String _defaultBacklinkType = 'dofollow';
  final double _defaultDofollowSurchargePercentage = 20.0;
  final double _defaultSpecialTopicsSurchargePercentage = 15.0;
  final int _defaultMaxCategories = 3;
  final Language _defaultLanguage = Language(name: 'English', code: 'en');
  final bool _defaultIsSponsored = false;
  final double _defaultPricing = 100.0;
  final String _defaultTraffic = '1000';
  final int _defaultMinWordCount = 500;
  final int _defaultMaxWordCount = 2000;
  final int _defaultMaxLinks = 3;
  final double _defaultPlatformFee = 5.0;
  final double _defaultPaypalFeePercentage = 2.9;
  final double _defaultPaypalFixedFee = 0.49;
  final double _defaultRazorpayFeePercentage = 2.0;
  final double _defaultRazorpayFixedFee = 0.0;

  final double _defaultMinimumPayout = 60.0;

  // Controllers and state
  final TextEditingController _pricingController = TextEditingController();
  final TextEditingController _trafficController = TextEditingController();
  final TextEditingController _wordCountMinController = TextEditingController();
  final TextEditingController _wordCountMaxController = TextEditingController();
  final TextEditingController _maxLinksController = TextEditingController();
  final TextEditingController _dofollowSurchargeController =
      TextEditingController();
  final TextEditingController _specialTopicsSurchargeController =
      TextEditingController();
  final TextEditingController _platformFeeController = TextEditingController();
  final TextEditingController _paypalFeePercentageController =
      TextEditingController();
  final TextEditingController _paypalFixedFeeController =
      TextEditingController();
  final TextEditingController _stripeFeePercentageController =
      TextEditingController();

  final TextEditingController _minimumPayoutController =
      TextEditingController();

  String _backlinkType = '';
  int _maxCategories = 0;
  Language? _selectedLanguage;
  bool _isSponsored = false;
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  @override
  void initState() {
    super.initState();
    _fetchDefaultsFromFirestore();
  }

  @override
  void dispose() {
    _pricingController.dispose();
    _trafficController.dispose();
    _wordCountMinController.dispose();
    _wordCountMaxController.dispose();
    _maxLinksController.dispose();
    _dofollowSurchargeController.dispose();
    _specialTopicsSurchargeController.dispose();
    _platformFeeController.dispose();
    _paypalFeePercentageController.dispose();
    _paypalFixedFeeController.dispose();
    _stripeFeePercentageController.dispose();
    super.dispose();
  }

  Future<void> _fetchDefaultsFromFirestore() async {
    setState(() => _isLoading = true);
    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('settings')
          .doc('defaults')
          .get();
      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        if (mounted) {
          setState(() {
            _backlinkType = data['defaultBacklinkType'] ?? _defaultBacklinkType;
            _dofollowSurchargeController.text =
                (data['dofollowSurchargePercentage'] ??
                        _defaultDofollowSurchargePercentage)
                    .toString();
            _specialTopicsSurchargeController.text =
                (data['specialTopicsSurchargePercentage'] ??
                        _defaultSpecialTopicsSurchargePercentage)
                    .toString();
            _maxCategories = data['maxCategories'] ?? _defaultMaxCategories;
            _selectedLanguage = Languages.defaultLanguages.firstWhere(
              (lang) =>
                  lang.name ==
                  (data['defaultLanguage'] ?? _defaultLanguage.name),
              orElse: () => _defaultLanguage,
            );
            _isSponsored = data['defaultIsSponsored'] ?? _defaultIsSponsored;
            _pricingController.text =
                (data['defaultPricing'] ?? _defaultPricing).toString();
            _trafficController.text =
                (data['defaultTraffic'] ?? _defaultTraffic).toString();
            _wordCountMinController.text =
                (data['defaultMinWordCount'] ?? _defaultMinWordCount)
                    .toString();
            _wordCountMaxController.text =
                (data['defaultMaxWordCount'] ?? _defaultMaxWordCount)
                    .toString();
            _maxLinksController.text =
                (data['defaultMaxLinks'] ?? _defaultMaxLinks).toString();
            _platformFeeController.text =
                (data['platformFee'] ?? _defaultPlatformFee).toString();
            _paypalFeePercentageController.text =
                (data['paypalFeePercentage'] ?? _defaultPaypalFeePercentage)
                    .toString();
            _paypalFixedFeeController.text =
                (data['paypalFixedFee'] ?? _defaultPaypalFixedFee).toString();
            _stripeFeePercentageController.text =
                (data['razorpayFeePercentage'] ?? _defaultRazorpayFeePercentage)
                    .toString();
            _minimumPayoutController.text =
                (data['minimumPayout'] ?? _defaultMinimumPayout).toString();
          });
        }
      } else {
        _setDefaultValues();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching defaults: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _setDefaultValues() {
    setState(() {
      _backlinkType = _defaultBacklinkType;
      _dofollowSurchargeController.text =
          _defaultDofollowSurchargePercentage.toString();
      _specialTopicsSurchargeController.text =
          _defaultSpecialTopicsSurchargePercentage.toString();
      _maxCategories = _defaultMaxCategories;
      _selectedLanguage = _defaultLanguage;
      _isSponsored = _defaultIsSponsored;
      _pricingController.text = _defaultPricing.toString();
      _trafficController.text = _defaultTraffic;
      _wordCountMinController.text = _defaultMinWordCount.toString();
      _wordCountMaxController.text = _defaultMaxWordCount.toString();
      _maxLinksController.text = _defaultMaxLinks.toString();
      _platformFeeController.text = _defaultPlatformFee.toString();
      _paypalFeePercentageController.text =
          _defaultPaypalFeePercentage.toString();
      _paypalFixedFeeController.text = _defaultPaypalFixedFee.toString();
      _stripeFeePercentageController.text =
          _defaultRazorpayFeePercentage.toString();
      _minimumPayoutController.text = _defaultMinimumPayout.toString();

      _errors.clear();
    });
  }

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'pricing': {
        'value': _pricingController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative number'
                : null,
      },
      'dofollowSurcharge': {
        'value': _dofollowSurchargeController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative percentage'
                : null,
      },
      'specialTopicsSurcharge': {
        'value': _specialTopicsSurchargeController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative percentage'
                : null,
      },
      'wordCountMin': {
        'value': _wordCountMinController.text,
        'validator': (String value) =>
            int.tryParse(value) == null || int.parse(value) < 0
                ? 'Enter a valid non-negative number'
                : null,
      },
      'wordCountMax': {
        'value': _wordCountMaxController.text,
        'validator': (String value) =>
            int.tryParse(value) == null || int.parse(value) < 0
                ? 'Enter a valid non-negative number'
                : null,
      },
      'maxLinks': {
        'value': _maxLinksController.text,
        'validator': (String value) =>
            int.tryParse(value) == null || int.parse(value) < 0
                ? 'Enter a valid non-negative number'
                : null,
      },
      'platformFee': {
        'value': _platformFeeController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative number'
                : null,
      },
      'paypalFeePercentage': {
        'value': _paypalFeePercentageController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative percentage'
                : null,
      },
      'razorpayFeePercentage': {
        'value': _stripeFeePercentageController.text,
        'validator': (String value) =>
            double.tryParse(value) == null || double.parse(value) < 0
                ? 'Enter a valid non-negative percentage'
                : null,
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _saveToFirestore() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.save, color: AppColors.blue),
            const SizedBox(width: 8),
            const Text('Confirm Save'),
          ],
        ),
        content: const Text('Are you sure you want to save these settings?'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.dark,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() => _isLoading = true);
    try {
      final data = {
        'defaultBacklinkType': _backlinkType,
        'dofollowSurchargePercentage':
            double.tryParse(_dofollowSurchargeController.text) ?? 0.0,
        'specialTopicsSurchargePercentage':
            double.tryParse(_specialTopicsSurchargeController.text) ?? 0.0,
        'maxCategories': _maxCategories,
        'defaultLanguage': _selectedLanguage?.name ?? _defaultLanguage.name,
        'defaultIsSponsored': _isSponsored,
        'defaultPricing': double.tryParse(_pricingController.text) ?? 0.0,
        'defaultTraffic': _trafficController.text,
        'defaultMinWordCount': int.tryParse(_wordCountMinController.text) ?? 0,
        'defaultMaxWordCount': int.tryParse(_wordCountMaxController.text) ?? 0,
        'defaultMaxLinks': int.tryParse(_maxLinksController.text) ?? 0,
        'platformFee': double.tryParse(_platformFeeController.text) ?? 0.0,
        'paypalFeePercentage':
            double.tryParse(_paypalFeePercentageController.text) ?? 0.0,
        'razorpayFeePercentage':
            double.tryParse(_stripeFeePercentageController.text) ?? 0.0,
      };
      data['minimumPayout'] =
          double.tryParse(_minimumPayoutController.text) ?? 0.0;

      await FirebaseFirestore.instance
          .collection('settings')
          .doc('defaults')
          .set(data, SetOptions(merge: true));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Settings saved successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final isMediumScreen = MediaQuery.of(context).size.width < 900;

    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.blue,
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading settings...',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 16,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(
                        248, 249, 252, 1.0), // AppColors.componentBackColor
                    Colors.white,
                  ],
                  stops: [0.0, 0.8],
                ),
              ),
              child: SingleChildScrollView(
                child: Center(
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    margin: const EdgeInsets.symmetric(
                        vertical: 24, horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(26, 115, 232, 0.04),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(20)),
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(26, 115, 232, 0.08),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                                spreadRadius: 2,
                              ),
                            ],
                            gradient: LinearGradient(
                              colors: [
                                Colors.white,
                                Color.fromRGBO(232, 240, 254,
                                    0.3), // AppColors.blueLightest
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.settings,
                                      color: AppColors.blue,
                                      size: isSmallScreen ? 24 : 28,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Text(
                                    'Admin Settings - Defaults',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 22 : 28,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Tooltip(
                                      message: 'Refresh settings',
                                      child: IconButton(
                                        icon: Icon(Icons.refresh,
                                            color: AppColors.blue),
                                        onPressed: _fetchDefaultsFromFirestore,
                                      ),
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Tooltip(
                                      message: 'Reset to default values',
                                      child: IconButton(
                                        icon: Icon(Icons.restore,
                                            color: AppColors.blue),
                                        onPressed: _setDefaultValues,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Form Content
                        Padding(
                          padding: const EdgeInsets.all(24),
                          child: isMediumScreen
                              ? _buildSingleColumnForm(isSmallScreen)
                              : _buildTwoColumnForm(isSmallScreen),
                        ),
                        // Sticky Footer
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border(
                              top: BorderSide(
                                  color: Color.fromRGBO(232, 240, 254, 0.8)),
                            ),
                            borderRadius: const BorderRadius.vertical(
                                bottom: Radius.circular(20)),
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(26, 115, 232, 0.04),
                                blurRadius: 10,
                                offset: const Offset(0, -5),
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                child: ElevatedButton.icon(
                                  onPressed: () => Navigator.pop(context),
                                  icon: const Icon(Icons.close),
                                  label: const Text(
                                    'Cancel',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Color.fromRGBO(245, 245, 245, 1.0),
                                    foregroundColor: AppColors.dark,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 16,
                                    ),
                                    elevation: 0,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                child: ElevatedButton.icon(
                                  onPressed:
                                      _isLoading ? null : _saveToFirestore,
                                  icon: _isLoading
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2,
                                          ),
                                        )
                                      : const Icon(Icons.save),
                                  label: const Text(
                                    'Save Settings',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.blue,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 16,
                                    ),
                                    elevation: 0,
                                    disabledBackgroundColor:
                                        Color.fromRGBO(232, 240, 254, 0.5),
                                    disabledForegroundColor: Colors.grey,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildSingleColumnForm(bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSection('General Settings', isSmallScreen, [
          _buildDropdown(
            label: 'Default Backlink Type',
            value: _backlinkType,
            items: ['dofollow', 'nofollow'],
            onChanged: (value) {
              if (value != null) setState(() => _backlinkType = value);
            },
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: _dofollowSurchargeController,
            label: 'Dofollow Surcharge (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['dofollowSurcharge'],
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: _specialTopicsSurchargeController,
            label: 'Special Topics Surcharge (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['specialTopicsSurcharge'],
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: TextEditingController(text: _maxCategories.toString()),
            label: 'Max Categories',
            keyboardType: TextInputType.number,
            onChanged: (value) {
              setState(() {
                _maxCategories = int.tryParse(value) ?? _defaultMaxCategories;
              });
            },
            isSmallScreen: isSmallScreen,
          ),
          _buildDropdown(
            label: 'Default Language',
            value: _selectedLanguage?.name,
            items: Languages.defaultLanguages.map((lang) => lang.name).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedLanguage = Languages.defaultLanguages
                      .firstWhere((lang) => lang.name == value);
                });
              }
            },
            isSmallScreen: isSmallScreen,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Color.fromRGBO(232, 240, 254, 0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Color.fromRGBO(232, 240, 254, 0.8),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.3),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.verified,
                    color: AppColors.blue,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Default Is Sponsored',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: _isSponsored ? AppColors.blue : AppColors.dark,
                  ),
                ),
                const Spacer(),
                SizedBox(
                  height: 24,
                  width: 24,
                  child: Checkbox(
                    value: _isSponsored,
                    activeColor: AppColors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    onChanged: (value) {
                      setState(() => _isSponsored = value ?? false);
                    },
                  ),
                ),
              ],
            ),
          ),
        ]),
        _buildSection('Pricing and Traffic', isSmallScreen, [
          _buildTextField(
            controller: _pricingController,
            label: 'Default Pricing (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['pricing'],
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: _trafficController,
            label: 'Default Traffic',
            isSmallScreen: isSmallScreen,
          ),
        ]),
        _buildSection('Content Settings', isSmallScreen, [
          _buildTextField(
            controller: _wordCountMinController,
            label: 'Default Min Word Count',
            keyboardType: TextInputType.number,
            errorText: _errors['wordCountMin'],
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: _wordCountMaxController,
            label: 'Default Max Word Count',
            keyboardType: TextInputType.number,
            errorText: _errors['wordCountMax'],
            isSmallScreen: isSmallScreen,
          ),
          _buildTextField(
            controller: _maxLinksController,
            label: 'Default Max Links',
            keyboardType: TextInputType.number,
            errorText: _errors['maxLinks'],
            isSmallScreen: isSmallScreen,
          ),
        ]),
        _buildSection('Platform Fees', isSmallScreen, [
          _buildTextField(
            controller: _platformFeeController,
            label: 'Platform Fee (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['platformFee'],
            isSmallScreen: isSmallScreen,
          ),
        ]),
        _buildSection('PayPal Fees', isSmallScreen, [
          _buildTextField(
            controller: _paypalFeePercentageController,
            label: 'PayPal Fee (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['paypalFeePercentage'],
            isSmallScreen: isSmallScreen,
          ),
        ]),
        _buildSection('Stripe Fees', isSmallScreen, [
          _buildTextField(
            controller: _stripeFeePercentageController,
            label: 'Stripe Fee (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['razorpayFeePercentage'],
            isSmallScreen: isSmallScreen,
          ),
        ]),
        _buildSection('Minimum Payout', isSmallScreen, [
          _buildTextField(
            controller: _minimumPayoutController,
            label: 'Minimum Payout Amount (\$)',
            keyboardType: TextInputType.number,
            errorText: _errors['minimumPayout'],
            isSmallScreen: isSmallScreen,
          ),
        ]),
      ],
    );
  }

  Widget _buildTwoColumnForm(bool isSmallScreen) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSection('General Settings', isSmallScreen, [
                _buildDropdown(
                  label: 'Default Backlink Type',
                  value: _backlinkType,
                  items: ['dofollow', 'nofollow'],
                  onChanged: (value) {
                    if (value != null) setState(() => _backlinkType = value);
                  },
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller: _dofollowSurchargeController,
                  label: 'Dofollow Surcharge (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['dofollowSurcharge'],
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller: _specialTopicsSurchargeController,
                  label: 'Special Topics Surcharge (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['specialTopicsSurcharge'],
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller:
                      TextEditingController(text: _maxCategories.toString()),
                  label: 'Max Categories',
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _maxCategories =
                          int.tryParse(value) ?? _defaultMaxCategories;
                    });
                  },
                  isSmallScreen: isSmallScreen,
                ),
                _buildDropdown(
                  label: 'Default Language',
                  value: _selectedLanguage?.name,
                  items: Languages.defaultLanguages
                      .map((lang) => lang.name)
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedLanguage = Languages.defaultLanguages
                            .firstWhere((lang) => lang.name == value);
                      });
                    }
                  },
                  isSmallScreen: isSmallScreen,
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Color.fromRGBO(232, 240, 254, 0.8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(232, 240, 254, 0.3),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.verified,
                          color: AppColors.blue,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Default Is Sponsored',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: _isSponsored ? AppColors.blue : AppColors.dark,
                        ),
                      ),
                      const Spacer(),
                      SizedBox(
                        height: 24,
                        width: 24,
                        child: Checkbox(
                          value: _isSponsored,
                          activeColor: AppColors.blue,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          onChanged: (value) {
                            setState(() => _isSponsored = value ?? false);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ]),
              _buildSection('Pricing and Traffic', isSmallScreen, [
                _buildTextField(
                  controller: _pricingController,
                  label: 'Default Pricing (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['pricing'],
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller: _trafficController,
                  label: 'Default Traffic',
                  isSmallScreen: isSmallScreen,
                ),
              ]),
            ],
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSection('Content Settings', isSmallScreen, [
                _buildTextField(
                  controller: _wordCountMinController,
                  label: 'Default Min Word Count',
                  keyboardType: TextInputType.number,
                  errorText: _errors['wordCountMin'],
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller: _wordCountMaxController,
                  label: 'Default Max Word Count',
                  keyboardType: TextInputType.number,
                  errorText: _errors['wordCountMax'],
                  isSmallScreen: isSmallScreen,
                ),
                _buildTextField(
                  controller: _maxLinksController,
                  label: 'Default Max Links',
                  keyboardType: TextInputType.number,
                  errorText: _errors['maxLinks'],
                  isSmallScreen: isSmallScreen,
                ),
              ]),
              _buildSection('Platform Fees', isSmallScreen, [
                _buildTextField(
                  controller: _platformFeeController,
                  label: 'Platform Fee (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['platformFee'],
                  isSmallScreen: isSmallScreen,
                ),
              ]),
              _buildSection('PayPal Fees', isSmallScreen, [
                _buildTextField(
                  controller: _paypalFeePercentageController,
                  label: 'PayPal Fee (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['paypalFeePercentage'],
                  isSmallScreen: isSmallScreen,
                ),
                // _buildTextField(
                //   controller: _paypalFixedFeeController,
                //   label: 'PayPal Fixed Fee (\$)',
                //   keyboardType: TextInputType.number,
                //   errorText: _errors['paypalFixedFee'],
                //   isSmallScreen: isSmallScreen,
                // ),
              ]),
              _buildSection('Stripe Fees', isSmallScreen, [
                _buildTextField(
                  controller: _stripeFeePercentageController,
                  label: 'Stripe Fee (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['stripeFeePercentage'],
                  isSmallScreen: isSmallScreen,
                ),
              ]),
              _buildSection('Minimum Payout', isSmallScreen, [
                _buildTextField(
                  controller: _minimumPayoutController,
                  label: 'Minimum Payout Amount (\$)',
                  keyboardType: TextInputType.number,
                  errorText: _errors['minimumPayout'],
                  isSmallScreen: isSmallScreen,
                ),
              ]),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSection(
      String title, bool isSmallScreen, List<Widget> children) {
    IconData iconData;

    // Assign appropriate icons based on section title
    if (title.contains('General')) {
      iconData = Icons.settings;
    } else if (title.contains('Pricing')) {
      iconData = Icons.attach_money;
    } else if (title.contains('Content')) {
      iconData = Icons.article;
    } else if (title.contains('Platform')) {
      iconData = Icons.business;
    } else if (title.contains('PayPal')) {
      iconData = Icons.payment;
    } else if (title.contains('Stripe')) {
      iconData = Icons.account_balance;
    } else if (title.contains('Minimum')) {
      iconData = Icons.money;
    } else {
      iconData = Icons.settings;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(232, 240, 254, 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  iconData,
                  color: AppColors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isSmallScreen ? 18 : 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(26, 115, 232, 0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: children
                  .map((child) => Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: child,
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType? keyboardType,
    String? errorText,
    bool isSmallScreen = false,
    Function(String)? onChanged,
  }) {
    // Determine icon based on label
    IconData iconData;
    if (label.contains('Pricing') ||
        label.contains('Fee') ||
        label.contains('\$')) {
      iconData = Icons.attach_money;
    } else if (label.contains('Word')) {
      iconData = Icons.text_fields;
    } else if (label.contains('Links')) {
      iconData = Icons.link;
    } else if (label.contains('Traffic')) {
      iconData = Icons.trending_up;
    } else if (label.contains('Surcharge') || label.contains('%')) {
      iconData = Icons.percent;
    } else if (label.contains('Categories')) {
      iconData = Icons.category;
    } else {
      iconData = Icons.edit;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Color.fromRGBO(232, 240, 254, 0.3),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                iconData,
                color: AppColors.blue,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(26, 115, 232, 0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
          ),
          child: TextField(
            controller: controller,
            keyboardType: keyboardType,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.blue, width: 2),
              ),
              filled: true,
              fillColor: Color.fromRGBO(248, 249, 252, 0.5),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              errorText: errorText,
              errorStyle: const TextStyle(
                fontFamily: 'Space',
                color: AppColors.red,
                fontSize: 12,
              ),
              prefixIcon: Icon(iconData, color: AppColors.blue),
            ),
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 15,
              color: AppColors.dark,
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    required bool isSmallScreen,
  }) {
    // Determine icon based on label
    IconData iconData;
    if (label.contains('Language')) {
      iconData = Icons.language;
    } else if (label.contains('Backlink')) {
      iconData = Icons.link;
    } else {
      iconData = Icons.arrow_drop_down_circle;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Color.fromRGBO(232, 240, 254, 0.3),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                iconData,
                color: AppColors.blue,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(26, 115, 232, 0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Color.fromRGBO(248, 249, 252, 0.5),
            ),
            child: Row(
              children: [
                Icon(iconData, color: AppColors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButton<String>(
                    value: value,
                    isExpanded: true,
                    underline: const SizedBox(),
                    icon: Icon(Icons.arrow_drop_down, color: AppColors.blue),
                    items: items
                        .map((item) => DropdownMenuItem(
                              value: item,
                              child: Text(
                                item,
                                style: const TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 15,
                                  color: AppColors.dark,
                                ),
                              ),
                            ))
                        .toList(),
                    onChanged: onChanged,
                    dropdownColor: Colors.white,
                    focusColor: AppColors.blue,
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 15,
                      color: AppColors.dark,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Mock Language class (replace with your actual implementation)
class Language {
  final String name;
  final String code;

  const Language({required this.name, required this.code});
}

class Languages {
  static const List<Language> defaultLanguages = [
    Language(name: 'English', code: 'en'),
    Language(name: 'Spanish', code: 'es'),
    Language(name: 'French', code: 'fr'),
    // Add more as needed
  ];
}
