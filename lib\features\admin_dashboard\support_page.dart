import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class AdminSupportPage extends StatefulWidget {
  const AdminSupportPage({super.key});

  @override
  State<AdminSupportPage> createState() => _AdminSupportPageState();
}

class _AdminSupportPageState extends State<AdminSupportPage> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TextEditingController _searchController = TextEditingController();
  String _filterStatus = 'All';
  final TextEditingController replyController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Color.fromRGBO(232, 240, 254, 0.5),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.support_agent,
                color: AppColors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Support Dashboard',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: Color.fromRGBO(232, 240, 254, 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: Icon(Icons.refresh, color: AppColors.blue),
              onPressed: () => setState(() {}),
              tooltip: 'Refresh',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(26, 115, 232, 0.03),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                          spreadRadius: 0,
                        ),
                      ],
                      border:
                          Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search requests...',
                        hintStyle: GoogleFonts.poppins(color: Colors.grey[600]),
                        prefixIcon: Icon(Icons.search, color: AppColors.blue),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: Color.fromRGBO(232, 240, 254, 1.0)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                              color: Color.fromRGBO(232, 240, 254, 1.0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide:
                              BorderSide(color: AppColors.blue, width: 2),
                        ),
                        filled: true,
                        fillColor: Color.fromRGBO(248, 249, 252, 0.5),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 15,
                        color: AppColors.dark,
                      ),
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(26, 115, 232, 0.03),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                    border:
                        Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
                  ),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Color.fromRGBO(248, 249, 252, 0.5),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.filter_list,
                            color: AppColors.blue, size: 20),
                        const SizedBox(width: 8),
                        DropdownButton<String>(
                          value: _filterStatus,
                          items: ['All', 'Pending', 'Replied', 'Resolved']
                              .map((status) => DropdownMenuItem(
                                    value: status,
                                    child: Text(
                                      status,
                                      style: GoogleFonts.poppins(
                                        fontSize: 15,
                                        color: AppColors.dark,
                                      ),
                                    ),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _filterStatus = value!;
                            });
                          },
                          style: GoogleFonts.poppins(
                            fontSize: 15,
                            color: AppColors.dark,
                          ),
                          underline: const SizedBox(),
                          icon: Icon(Icons.arrow_drop_down,
                              color: AppColors.blue),
                          borderRadius: BorderRadius.circular(12),
                          dropdownColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: _firestore
                  .collection('support_requests')
                  .orderBy('timestamp', descending: true)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return _buildErrorState('Error: ${snapshot.error}');
                }
                if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                  return _buildEmptyState();
                }

                final requests = snapshot.data!.docs.where((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  final reply = data['reply'];
                  final status = data['status'] ?? 'pending';
                  final searchQuery = _searchController.text.toLowerCase();
                  final name = (data['name'] ?? '').toLowerCase();
                  final email = (data['email'] ?? '').toLowerCase();
                  final requestText = (data['request'] ?? '').toLowerCase();

                  final matchesSearch = name.contains(searchQuery) ||
                      email.contains(searchQuery) ||
                      requestText.contains(searchQuery);

                  final matchesStatus = _filterStatus == 'All' ||
                      (_filterStatus == 'Pending' &&
                          reply == null &&
                          status != 'resolved') ||
                      (_filterStatus == 'Replied' &&
                          reply != null &&
                          status != 'resolved') ||
                      (_filterStatus == 'Resolved' && status == 'resolved');

                  return matchesSearch && matchesStatus;
                }).toList();

                if (requests.isEmpty) {
                  return _buildEmptyState(
                      message: 'No matching requests found.');
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: requests.length,
                  itemBuilder: (context, index) {
                    return _buildRequestCard(context, requests[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestCard(
      BuildContext context, QueryDocumentSnapshot request) {
    final data = request.data() as Map<String, dynamic>;
    final requestId = request.id;
    final name = data['name'] ?? 'Unknown';
    final email = data['email'] ?? 'Unknown';
    final requestText = data['request'] ?? '';
    final reply = data['reply'];
    final timestamp = (data['timestamp'] as Timestamp?)?.toDate();
    final userId = data['uid'] ?? '';
    final status = data['status'] ?? 'pending';
    final isResolved = status == 'resolved';
    final displayStatus = isResolved
        ? 'Resolved'
        : reply == null
            ? 'Pending'
            : 'Replied';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        childrenPadding: const EdgeInsets.all(20),
        iconColor: AppColors.blue,
        collapsedIconColor: AppColors.blue,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isResolved
                        ? Color.fromRGBO(52, 168, 83, 0.1)
                        : reply == null
                            ? Color.fromRGBO(251, 188, 5, 0.1)
                            : Color.fromRGBO(26, 115, 232, 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    isResolved
                        ? Icons.check_circle
                        : reply == null
                            ? Icons.hourglass_empty
                            : Icons.question_answer,
                    color: isResolved
                        ? AppColors.green
                        : reply == null
                            ? AppColors.yellow
                            : AppColors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  name,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.dark,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isResolved
                    ? Color.fromRGBO(52, 168, 83, 0.1)
                    : reply == null
                        ? Color.fromRGBO(251, 188, 5, 0.1)
                        : Color.fromRGBO(26, 115, 232, 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isResolved
                      ? Color.fromRGBO(52, 168, 83, 0.3)
                      : reply == null
                          ? Color.fromRGBO(251, 188, 5, 0.3)
                          : Color.fromRGBO(26, 115, 232, 0.3),
                ),
              ),
              child: Text(
                displayStatus,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: isResolved
                      ? AppColors.green
                      : reply == null
                          ? AppColors.yellow
                          : AppColors.blue,
                ),
              ),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(left: 40, top: 4),
          child: Text(
            email,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ),
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color.fromRGBO(248, 249, 252, 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.message, color: AppColors.blue, size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'Request Message',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.dark,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  requestText,
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    color: AppColors.dark,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.calendar_today, color: AppColors.blue, size: 16),
              const SizedBox(width: 8),
              Text(
                'Submitted: ${timestamp != null ? DateFormat('MMM dd, yyyy HH:mm').format(timestamp) : 'Unknown'}',
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          if (reply != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color.fromRGBO(26, 115, 232, 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Color.fromRGBO(26, 115, 232, 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.question_answer,
                          color: AppColors.blue, size: 18),
                      const SizedBox(width: 8),
                      Text(
                        'Admin Reply',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    reply,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      color: AppColors.dark,
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (!isResolved) ...[
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              ),
              child: TextField(
                controller: replyController,
                decoration: InputDecoration(
                  hintText: 'Enter your reply...',
                  hintStyle: GoogleFonts.poppins(color: Colors.grey[600]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppColors.blue, width: 2),
                  ),
                  filled: true,
                  fillColor: Color.fromRGBO(248, 249, 252, 0.5),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  prefixIcon: Icon(Icons.reply, color: AppColors.blue),
                ),
                maxLines: 3,
                enabled: !isResolved,
                style: GoogleFonts.poppins(
                  fontSize: 15,
                  color: AppColors.dark,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (!isResolved)
                ElevatedButton.icon(
                  onPressed: () async {
                    final replyText = replyController.text.trim();
                    if (replyText.isNotEmpty) {
                      try {
                        await _firestore
                            .collection('support_requests')
                            .doc(requestId)
                            .update({
                          'reply': replyText,
                          'replied_at': FieldValue.serverTimestamp(),
                          'status': 'replied', // Update status to replied
                        });
                        replyController.clear();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Reply sent successfully',
                                style: GoogleFonts.poppins(),
                              ),
                              backgroundColor: AppColors.green,
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Error sending reply: $e',
                                style: GoogleFonts.poppins(),
                              ),
                              backgroundColor: AppColors.red,
                            ),
                          );
                        }
                      }
                    }
                  },
                  icon: Icon(Icons.send),
                  label: Text(
                    'Send Reply',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              Row(
                children: [
                  if (userId.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(232, 240, 254, 0.5),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextButton.icon(
                        onPressed: () => _showProfileDialog(context, userId),
                        icon:
                            Icon(Icons.person, color: AppColors.blue, size: 18),
                        label: Text(
                          'View Profile',
                          style: GoogleFonts.poppins(
                            color: AppColors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  if (!isResolved && reply != null)
                    Container(
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(52, 168, 83, 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextButton.icon(
                        onPressed: () => _showResolveDialog(context, requestId),
                        icon: Icon(Icons.check_circle,
                            color: AppColors.green, size: 18),
                        label: Text(
                          'Mark Resolved',
                          style: GoogleFonts.poppins(
                            color: AppColors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Other methods (_buildEmptyState, _buildErrorState, _showProfileDialog, etc.) remain unchanged

  Widget _buildErrorState(String message) {
    return Center(
      child: Text(
        message,
        style: GoogleFonts.poppins(fontSize: 18, color: Colors.red),
      ),
    );
  }

  // Including _showResolveDialog for reference, as it already updates the status correctly
  void _showResolveDialog(BuildContext context, String requestId) {
    // Get the scaffold messenger before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.green),
            const SizedBox(width: 8),
            Text(
              'Mark as Resolved',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to mark this request as resolved?',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.dark,
            ),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              // Close the dialog first
              Navigator.pop(dialogContext);

              // Then perform the async operation without using BuildContext
              FirebaseFirestore.instance
                  .collection('support_requests')
                  .doc(requestId)
                  .update({'status': 'resolved'}).then((_) {
                // Show success message using the stored scaffoldMessenger
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(
                      'Request marked as resolved',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: AppColors.green,
                  ),
                );
              }).catchError((e) {
                // Show error message using the stored scaffoldMessenger
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(
                      'Error: $e',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: AppColors.red,
                  ),
                );
              });
            },
            icon: Icon(Icons.check_circle),
            label: Text(
              'Resolve',
              style: GoogleFonts.poppins(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ],
      ),
    );
  }
}

void _showProfileDialog(BuildContext context, String uid) {
  showDialog(
    context: context,
    builder: (dialogContext) => Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(dialogContext).size.width * 0.4,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(26, 115, 232, 0.08),
              blurRadius: 15,
              offset: const Offset(0, 5),
              spreadRadius: 1,
            ),
          ],
        ),
        child: FutureBuilder<DocumentSnapshot>(
          future: FirebaseFirestore.instance.collection('users').doc(uid).get(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: CircularProgressIndicator(
                  color: AppColors.blue,
                  strokeWidth: 3,
                ),
              );
            }
            if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.error_outline, color: AppColors.red, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      'Error: ${snapshot.error}',
                      style: GoogleFonts.poppins(color: AppColors.red),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }
            if (!snapshot.hasData || !snapshot.data!.exists) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.person_off, color: Colors.grey, size: 48),
                    const SizedBox(height: 16),
                    Text(
                      'User not found',
                      style:
                          GoogleFonts.poppins(fontSize: 18, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            final userData = snapshot.data!.data() as Map<String, dynamic>;
            final name = userData['name'] ?? 'Unknown';
            final email = userData['email'] ?? 'Unknown';
            final emailVerified = userData['emailVerified'] ?? false;
            final mobileNumber = userData['mobileNumber'] ?? 'Not provided';
            final profilePictureUrl = userData['profilePictureUrl'] ?? '';
            final isPublisher = userData['isPublisher'] ?? false;
            final createdAt = (userData['createdAt'] as Timestamp?)?.toDate();
            final lastLogin = (userData['lastLogin'] as Timestamp?)?.toDate();
            final additionalDetails =
                userData['additionalDetails'] as Map<String, dynamic>? ?? {};
            final isBlocked = userData['isBlocked'] ?? false;
            final isAdmin = userData['isAdmin'] ?? false;
            final assignedOrderIds =
                List<String>.from(userData['assignedOrderIds'] ?? []);

            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(26, 115, 232, 0.15),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 40,
                          backgroundColor: Color.fromRGBO(232, 240, 254, 0.8),
                          backgroundImage: profilePictureUrl.isNotEmpty
                              ? NetworkImage(profilePictureUrl)
                              : null,
                          child: profilePictureUrl.isEmpty
                              ? Text(
                                  name.isNotEmpty ? name[0].toUpperCase() : 'U',
                                  style: GoogleFonts.poppins(
                                    fontSize: 32,
                                    color: AppColors.blue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: GoogleFonts.poppins(
                                fontSize: 22,
                                fontWeight: FontWeight.w600,
                                color: AppColors.dark,
                              ),
                            ),
                            Row(
                              children: [
                                Icon(Icons.email,
                                    color: AppColors.blue, size: 16),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    email,
                                    style: GoogleFonts.poppins(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildBadge(
                        'Email ${emailVerified ? 'Verified' : 'Unverified'}',
                        emailVerified
                            ? Color.fromRGBO(52, 168, 83, 0.1)
                            : Color.fromRGBO(234, 67, 53, 0.1),
                        emailVerified ? AppColors.green : AppColors.red,
                        emailVerified ? Icons.verified_user : Icons.error,
                      ),
                      _buildBadge(
                        isAdmin ? 'Admin' : 'User',
                        isAdmin
                            ? Color.fromRGBO(26, 115, 232, 0.1)
                            : Color.fromRGBO(154, 160, 166, 0.1),
                        isAdmin ? AppColors.blue : Colors.grey[700]!,
                        isAdmin ? Icons.admin_panel_settings : Icons.person,
                      ),
                      _buildBadge(
                        isPublisher ? 'Publisher' : 'Not Publisher',
                        isPublisher
                            ? Color.fromRGBO(142, 36, 170, 0.1)
                            : Color.fromRGBO(154, 160, 166, 0.1),
                        isPublisher
                            ? Color.fromRGBO(142, 36, 170, 1.0)
                            : Colors.grey[700]!,
                        isPublisher ? Icons.verified : Icons.person_outline,
                      ),
                      _buildBadge(
                        isBlocked ? 'Blocked' : 'Active',
                        isBlocked
                            ? Color.fromRGBO(234, 67, 53, 0.1)
                            : Color.fromRGBO(52, 168, 83, 0.1),
                        isBlocked ? AppColors.red : AppColors.green,
                        isBlocked ? Icons.block : Icons.check_circle,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildProfileItem(
                    'UID',
                    uid,
                    Icons.fingerprint,
                  ),
                  _buildProfileItem(
                    'Mobile Number',
                    mobileNumber,
                    Icons.phone,
                  ),
                  _buildProfileItem(
                    'Created At',
                    createdAt != null
                        ? DateFormat('MMM dd, yyyy').format(createdAt)
                        : 'Unknown',
                    Icons.calendar_today,
                  ),
                  _buildProfileItem(
                    'Last Login',
                    lastLogin != null
                        ? DateFormat('MMM dd, yyyy').format(lastLogin)
                        : 'Unknown',
                    Icons.login,
                  ),
                  if (additionalDetails.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Additional Details',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...additionalDetails.entries.map(
                      (entry) => _buildProfileItem(
                        entry.key,
                        entry.value.toString(),
                        Icons.info,
                      ),
                    ),
                  ],
                  if (assignedOrderIds.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Assigned Orders',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: assignedOrderIds
                          .map(
                            (orderId) => Chip(
                              label: Text(
                                orderId,
                                style: GoogleFonts.poppins(fontSize: 14),
                              ),
                              backgroundColor: Colors.grey[100],
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ],
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton.icon(
                      onPressed: () => Navigator.pop(dialogContext),
                      icon: Icon(Icons.close),
                      label: Text(
                        'Close',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    ),
  );
}

Widget _buildBadge(
    String label, Color bgColor, Color textColor, IconData icon) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: const Color.fromARGB(77, 0, 0, 0),
      ),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: textColor, size: 14),
        const SizedBox(width: 6),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: textColor,
          ),
        ),
      ],
    ),
  );
}

Widget _buildProfileItem(String label, String value, IconData icon) {
  return Container(
    margin: const EdgeInsets.symmetric(vertical: 8),
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Color.fromRGBO(248, 249, 252, 0.5),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
    ),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color.fromRGBO(232, 240, 254, 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppColors.blue, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _buildEmptyState({String message = 'No requests found.'}) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.inbox,
          size: 64,
          color: Color.fromRGBO(26, 115, 232, 0.3),
        ),
        const SizedBox(height: 16),
        Text(
          message,
          style: GoogleFonts.poppins(
            fontSize: 18,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Check back later for new support requests',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.grey[500],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}
