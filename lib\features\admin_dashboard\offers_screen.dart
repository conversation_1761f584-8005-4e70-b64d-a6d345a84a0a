import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/offer_model.dart';

import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

class OffersManagementScreen extends StatefulWidget {
  const OffersManagementScreen({super.key});

  @override
  State<OffersManagementScreen> createState() => _OffersManagementScreenState();
}

class _OffersManagementScreenState extends State<OffersManagementScreen> {
  List<OfferModel> _offers = [];
  bool _isLoading = true;
  String _sortField = 'createdAt';
  bool _sortAscending = false;
  int _currentPage = 1;
  final int _itemsPerPage = 10;

  // Form controllers for adding/editing offers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _percentageController = TextEditingController();
  final TextEditingController _minAmountController = TextEditingController();
  DateTime? _validUntil;
  bool _isActive = true;
  final Map<String, String?> _errors = {};

  @override
  void initState() {
    super.initState();
    _fetchOffers();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _percentageController.dispose();
    _minAmountController.dispose();
    super.dispose();
  }

  Future<void> _fetchOffers() async {
    setState(() => _isLoading = true);
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('offers')
          .orderBy(_sortField, descending: !_sortAscending)
          .get();

      final offers = snapshot.docs.map((doc) {
        return OfferModel.fromMap(doc.data(), doc.id);
      }).toList();

      setState(() {
        _offers = offers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading offers: $e'),
          backgroundColor: AppColors.red,
        ),
      );
    }
  }

  void _showAddOfferDialog() {
    _resetForm();
    showDialog(
      context: context,
      builder: (context) => _buildOfferDialog(isEdit: false),
    );
  }

  void _showEditOfferDialog(OfferModel offer) {
    _resetForm();
    _titleController.text = offer.title;
    _descriptionController.text = offer.description;
    _percentageController.text = offer.percentage.toString();
    _minAmountController.text = offer.minAmount.toString();
    _validUntil = offer.validUntil?.toDate();
    _isActive = offer.isActive;

    showDialog(
      context: context,
      builder: (context) => _buildOfferDialog(isEdit: true, offer: offer),
    );
  }

  void _resetForm() {
    _titleController.clear();
    _descriptionController.clear();
    _percentageController.clear();
    _minAmountController.clear();
    _validUntil = null;
    _isActive = true;
    _errors.clear();
  }

  bool _validateForm() {
    _errors.clear();
    bool isValid = true;

    if (_titleController.text.isEmpty) {
      _errors['title'] = 'Title is required';
      isValid = false;
    }

    if (_descriptionController.text.isEmpty) {
      _errors['description'] = 'Description is required';
      isValid = false;
    }

    if (_percentageController.text.isEmpty) {
      _errors['percentage'] = 'Percentage is required';
      isValid = false;
    } else if (double.tryParse(_percentageController.text) == null) {
      _errors['percentage'] = 'Please enter a valid number';
      isValid = false;
    } else if (double.parse(_percentageController.text) <= 0) {
      _errors['percentage'] = 'Percentage must be greater than 0';
      isValid = false;
    }

    if (_minAmountController.text.isEmpty) {
      _errors['minAmount'] = 'Minimum amount is required';
      isValid = false;
    } else if (double.tryParse(_minAmountController.text) == null) {
      _errors['minAmount'] = 'Please enter a valid number';
      isValid = false;
    } else if (double.parse(_minAmountController.text) < 0) {
      _errors['minAmount'] = 'Minimum amount cannot be negative';
      isValid = false;
    }

    return isValid;
  }

  Future<void> _saveOffer({String? offerId}) async {
    if (!_validateForm()) {
      return;
    }

    setState(() => _isLoading = true);
    try {
      final data = {
        'title': _titleController.text,
        'description': _descriptionController.text,
        'percentage': double.parse(_percentageController.text),
        'minAmount': double.parse(_minAmountController.text),
        'isActive': _isActive,
        'validUntil':
            _validUntil != null ? Timestamp.fromDate(_validUntil!) : null,
      };

      if (offerId == null) {
        // Add new offer
        data['createdAt'] = Timestamp.now();
        await FirebaseFirestore.instance.collection('offers').add(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Offer added successfully'),
              backgroundColor: AppColors.green,
            ),
          );
        }
      } else {
        // Update existing offer
        await FirebaseFirestore.instance
            .collection('offers')
            .doc(offerId)
            .update(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Offer updated successfully'),
              backgroundColor: AppColors.green,
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
        _fetchOffers();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving offer: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteOffer(String offerId) async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this offer?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('offers')
          .doc(offerId)
          .delete();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Offer deleted successfully'),
            backgroundColor: AppColors.green,
          ),
        );
        _fetchOffers();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting offer: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleOfferStatus(OfferModel offer) async {
    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('offers')
          .doc(offer.id)
          .update({
        'isActive': !offer.isActive,
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Offer ${offer.isActive ? 'deactivated' : 'activated'} successfully'),
            backgroundColor: AppColors.green,
          ),
        );
        _fetchOffers();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating offer status: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  Widget _buildHeader(bool isSmallScreen) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.blue.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
        gradient: LinearGradient(
          colors: [
            Colors.white,
            AppColors.blueLightest.withOpacity(0.3),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Offers Management',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 24 : 32,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
          ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              iconColor: Colors.white,
              textStyle: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            label: const Text('Add Offer'),
            icon: const Icon(Icons.add),
            onPressed: _showAddOfferDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildOffersTable(bool isSmallScreen, bool isWideScreen) {
    final paginatedOffers = _getPaginatedOffers();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.blue.withOpacity(0.04),
                blurRadius: 15,
                offset: const Offset(0, 5),
                spreadRadius: 1,
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 16),
                child: Text(
                  'All Offers',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 18 : 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.dark,
                  ),
                ),
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  columnSpacing: isSmallScreen ? 16 : 24,
                  headingRowHeight: 60,
                  dataRowMinHeight: 70,
                  dataRowMaxHeight: 85,
                  headingRowColor: WidgetStateProperty.all(
                    AppColors.blueLightest.withOpacity(0.2),
                  ),
                  border: TableBorder(
                    horizontalInside: BorderSide(
                      color: Colors.grey.withOpacity(0.15),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  columns: [
                    DataColumn(
                      label: Text('Title', style: _headerStyle(isSmallScreen)),
                      onSort: (columnIndex, ascending) {
                        _sortField = 'title';
                        _sortAscending = ascending;
                        _fetchOffers();
                      },
                    ),
                    DataColumn(
                      label: Text('Description',
                          style: _headerStyle(isSmallScreen)),
                    ),
                    DataColumn(
                      label: Text('Percentage',
                          style: _headerStyle(isSmallScreen)),
                      onSort: (columnIndex, ascending) {
                        _sortField = 'percentage';
                        _sortAscending = ascending;
                        _fetchOffers();
                      },
                    ),
                    DataColumn(
                      label: Text('Min Amount',
                          style: _headerStyle(isSmallScreen)),
                      onSort: (columnIndex, ascending) {
                        _sortField = 'minAmount';
                        _sortAscending = ascending;
                        _fetchOffers();
                      },
                    ),
                    DataColumn(
                      label: Text('Valid Until',
                          style: _headerStyle(isSmallScreen)),
                    ),
                    DataColumn(
                      label: Text('Status', style: _headerStyle(isSmallScreen)),
                      onSort: (columnIndex, ascending) {
                        _sortField = 'isActive';
                        _sortAscending = ascending;
                        _fetchOffers();
                      },
                    ),
                    DataColumn(
                      label: Text('Created At',
                          style: _headerStyle(isSmallScreen)),
                      onSort: (columnIndex, ascending) {
                        _sortField = 'createdAt';
                        _sortAscending = ascending;
                        _fetchOffers();
                      },
                    ),
                    DataColumn(
                      label:
                          Text('Actions', style: _headerStyle(isSmallScreen)),
                    ),
                  ],
                  rows: paginatedOffers.isEmpty
                      ? [
                          DataRow(
                            cells: List.generate(
                              8,
                              (index) => DataCell(
                                index == 0
                                    ? Text(
                                        'No offers found',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: 14,
                                          color: Colors.grey,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      )
                                    : const Text(''),
                              ),
                            ),
                          ),
                        ]
                      : paginatedOffers
                          .map((offer) =>
                              _buildOfferDataRow(offer, isSmallScreen))
                          .toList(),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        _buildPaginationControls(isSmallScreen),
      ],
    );
  }

  DataRow _buildOfferDataRow(OfferModel offer, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            offer.title,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 15,
              fontWeight: FontWeight.w600,
              color: AppColors.dark,
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: offer.description,
            child: Text(
              offer.description.length > 30
                  ? '${offer.description.substring(0, 27)}...'
                  : offer.description,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 15,
                color: AppColors.dark.withAlpha(200),
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.blue.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${offer.percentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 15,
                fontWeight: FontWeight.w600,
                color: AppColors.blue,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.yellow.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '\$${offer.minAmount.toStringAsFixed(2)}',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 15,
                fontWeight: FontWeight.w600,
                color: AppColors.yellow.withAlpha(220),
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            offer.validUntil != null
                ? DateFormat('MMM dd, yyyy').format(offer.validUntil!.toDate())
                : 'No expiration',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 15,
              color: offer.validUntil != null
                  ? AppColors.dark.withAlpha(200)
                  : Colors.grey,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: offer.isActive
                  ? AppColors.green.withAlpha(15)
                  : AppColors.red.withAlpha(15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: offer.isActive
                    ? AppColors.green.withAlpha(50)
                    : AppColors.red.withAlpha(50),
                width: 1,
              ),
            ),
            child: Text(
              offer.isActive ? 'Active' : 'Inactive',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 13 : 15,
                color: offer.isActive ? AppColors.green : AppColors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            DateFormat('MMM dd, yyyy').format(offer.createdAt.toDate()),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 13 : 15,
              color: AppColors.dark.withAlpha(180),
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppColors.blue.withAlpha(15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'Edit',
                  child: IconButton(
                    icon: Icon(Icons.edit,
                        color: AppColors.blue, size: isSmallScreen ? 20 : 22),
                    onPressed: () => _showEditOfferDialog(offer),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: offer.isActive
                      ? AppColors.green.withAlpha(15)
                      : AppColors.grey.withAlpha(15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: offer.isActive ? 'Deactivate' : 'Activate',
                  child: IconButton(
                    icon: Icon(
                      offer.isActive ? Icons.toggle_on : Icons.toggle_off,
                      color: offer.isActive ? AppColors.green : AppColors.grey,
                      size: isSmallScreen ? 20 : 22,
                    ),
                    onPressed: () => _toggleOfferStatus(offer),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.red.withAlpha(15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Tooltip(
                  message: 'Delete',
                  child: IconButton(
                    icon: Icon(Icons.delete,
                        color: AppColors.red, size: isSmallScreen ? 20 : 22),
                    onPressed: () => _deleteOffer(offer.id),
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaginationControls(bool isSmallScreen) {
    final totalPages = (_offers.length / _itemsPerPage).ceil();

    if (totalPages <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.03),
            blurRadius: 10,
            offset: const Offset(0, 2),
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildPageButton(
            icon: Icons.keyboard_double_arrow_left,
            onPressed: _currentPage > 1
                ? () => setState(() => _currentPage = 1)
                : null,
            isActive: _currentPage > 1,
          ),
          const SizedBox(width: 8),
          _buildPageButton(
            icon: Icons.chevron_left,
            onPressed:
                _currentPage > 1 ? () => setState(() => _currentPage--) : null,
            isActive: _currentPage > 1,
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.blue,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '$_currentPage of $totalPages',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
          _buildPageButton(
            icon: Icons.chevron_right,
            onPressed: _currentPage < totalPages
                ? () => setState(() => _currentPage++)
                : null,
            isActive: _currentPage < totalPages,
          ),
          const SizedBox(width: 8),
          _buildPageButton(
            icon: Icons.keyboard_double_arrow_right,
            onPressed: _currentPage < totalPages
                ? () => setState(() => _currentPage = totalPages)
                : null,
            isActive: _currentPage < totalPages,
          ),
        ],
      ),
    );
  }

  Widget _buildPageButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required bool isActive,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive ? AppColors.blueLightest : Colors.grey.withAlpha(30),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        icon: Icon(icon, size: 20),
        onPressed: onPressed,
        color: isActive ? AppColors.blue : AppColors.grey,
        style: IconButton.styleFrom(
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  List<OfferModel> _getPaginatedOffers() {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    if (startIndex >= _offers.length) {
      return [];
    }

    return _offers.sublist(
        startIndex, endIndex > _offers.length ? _offers.length : endIndex);
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 14 : 16,
      fontWeight: FontWeight.bold,
      color: AppColors.blue,
      letterSpacing: 0.5,
    );
  }

  Widget _buildOfferDialog({required bool isEdit, OfferModel? offer}) {
    return AlertDialog(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.blue.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isEdit ? Icons.edit_note : Icons.add_circle_outline,
              color: AppColors.blue,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            isEdit ? 'Edit Offer' : 'Add New Offer',
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
        ],
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      backgroundColor: Colors.white,
      elevation: 5,
      content: Container(
        width: 500,
        constraints: const BoxConstraints(maxWidth: 500),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              _buildTextField(
                controller: _titleController,
                label: 'Title',
                errorText: _errors['title'],
                icon: Icons.title,
              ),
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                errorText: _errors['description'],
                maxLines: 3,
                icon: Icons.description,
              ),
              _buildTextField(
                controller: _percentageController,
                label: 'Percentage',
                errorText: _errors['percentage'],
                keyboardType: TextInputType.number,
                suffix: '%',
                icon: Icons.percent,
              ),
              _buildTextField(
                controller: _minAmountController,
                label: 'Minimum Amount',
                errorText: _errors['minAmount'],
                keyboardType: TextInputType.number,
                prefix: '\$',
                icon: Icons.attach_money,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.event,
                    color: AppColors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Valid Until (Optional)',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.dark,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _validUntil ??
                        DateTime.now().add(const Duration(days: 30)),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: ColorScheme.light(
                            primary: AppColors.blue,
                            onPrimary: Colors.white,
                            surface: Colors.white,
                            onSurface: AppColors.dark,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  );
                  if (date != null) {
                    setState(() => _validUntil = date);
                  }
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _validUntil != null
                          ? AppColors.blue.withAlpha(100)
                          : Colors.grey[300]!,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: _validUntil != null
                        ? AppColors.blueLightest.withAlpha(50)
                        : Colors.transparent,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _validUntil != null
                            ? DateFormat('MMM dd, yyyy').format(_validUntil!)
                            : 'No expiration',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 14,
                          color: _validUntil != null
                              ? AppColors.dark
                              : Colors.grey,
                        ),
                      ),
                      Icon(
                        Icons.calendar_today,
                        color: _validUntil != null
                            ? AppColors.blue
                            : Colors.grey[600],
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.blueLightest.withAlpha(50),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.blue.withAlpha(50),
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      height: 24,
                      width: 24,
                      child: Checkbox(
                        value: _isActive,
                        onChanged: (value) {
                          setState(() => _isActive = value ?? false);
                        },
                        activeColor: AppColors.blue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Active',
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: _isActive ? AppColors.blue : AppColors.dark,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      _isActive ? Icons.toggle_on : Icons.toggle_off,
                      color: _isActive ? AppColors.blue : Colors.grey,
                      size: 28,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actionsPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          style: TextButton.styleFrom(
            foregroundColor: AppColors.dark,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            'Cancel',
            style: TextStyle(
              fontFamily: 'Space',
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () => _saveOffer(offerId: offer?.id),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: Text(
            isEdit ? 'Update Offer' : 'Add Offer',
            style: const TextStyle(
              fontFamily: 'Space',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? prefix,
    String? suffix,
    IconData? icon,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: AppColors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
              ],
              Text(
                label,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            keyboardType: keyboardType,
            maxLines: maxLines,
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 15,
            ),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.blue, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.red),
              ),
              errorText: errorText,
              prefixText: prefix,
              suffixText: suffix,
              filled: true,
              fillColor: Colors.grey[50],
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              errorStyle: const TextStyle(
                fontFamily: 'Space',
                fontSize: 12,
                color: AppColors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isWideScreen = screenWidth >= 1200;

    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.blue,
                strokeWidth: 3,
              ),
            )
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(
                        248, 249, 252, 1.0), // AppColors.componentBackColor
                    Colors.white,
                  ],
                  stops: const [0.0, 0.8],
                ),
              ),
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(isSmallScreen),
                    const SizedBox(height: 24),
                    _buildOffersTable(isSmallScreen, isWideScreen),
                  ],
                ),
              ),
            ),
    );
  }
}
