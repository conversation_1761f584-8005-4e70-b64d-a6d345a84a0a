import 'package:cloud_firestore/cloud_firestore.dart';

class WebsiteModel {
  final String? websiteId;
  final String url;
  final String domainName;
  final String? language;
  final String? publisherId;
  final bool isSponsored;
  final List<String> categories;
  final int da;
  final int dr;
  final int traffic;
  final double pricing;
  final double basePricing;
  final double specialTopicsAdditionalPrice;
  final bool hasSpecialTopicsPricing;
  final String backlinkType;
  final double dofollowCost; // Cost for dofollow backlinks
  final List<String> allowedTopics;
  final List<String> disallowedTopics;
  final int wordCountMin;
  final int wordCountMax;
  final int maxLinks;
  final String status;
  final Timestamp submissionDate;
  final Timestamp createdAt;
  final Timestamp lastUpdated;
  final bool isActive;
  final List<String> contentType;
  final String contactEmail;
  final String notes;
  final int viewsCount;
  final int clicksCount;
  final String country;
  final bool adminWebsite;
  final String? rejectionReason; // Added rejectionReason as a nullable string
  final double? spamScore; // Spam score as a percentage (0-100)

  WebsiteModel({
    this.websiteId,
    required this.url,
    required this.domainName,
    this.language,
    this.publisherId,
    required this.isSponsored,
    required this.categories,
    required this.da,
    required this.dr,
    required this.traffic,
    required this.pricing,
    required this.basePricing,
    required this.specialTopicsAdditionalPrice,
    required this.hasSpecialTopicsPricing,
    required this.backlinkType,
    required this.dofollowCost,
    required this.allowedTopics,
    required this.disallowedTopics,
    required this.wordCountMin,
    required this.wordCountMax,
    required this.maxLinks,
    required this.status,
    required this.submissionDate,
    required this.createdAt,
    required this.lastUpdated,
    required this.isActive,
    required this.contentType,
    required this.contactEmail,
    required this.notes,
    required this.viewsCount,
    required this.clicksCount,
    required this.country,
    required this.adminWebsite,
    this.rejectionReason, // Added rejectionReason to the constructor
    this.spamScore,
  });

  Map<String, dynamic> toMap() {
    return {
      'websiteId': websiteId ?? '',
      'url': url,
      'domainName': domainName,
      'language': language,
      'publisherId': publisherId,
      'isSponsored': isSponsored,
      'categories': categories,
      'da': da,
      'dr': dr,
      'traffic': traffic,
      'pricing': pricing,
      'basePricing': basePricing,
      'specialTopicsAdditionalPrice': specialTopicsAdditionalPrice,
      'hasSpecialTopicsPricing': hasSpecialTopicsPricing,
      'backlinkType': backlinkType,
      'dofollowCost': dofollowCost,
      'allowedTopics': allowedTopics,
      'disallowedTopics': disallowedTopics,
      'wordCountMin': wordCountMin,
      'wordCountMax': wordCountMax,
      'maxLinks': maxLinks,
      'status': status,
      'submissionDate': submissionDate,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
      'isActive': isActive,
      'contentType': contentType,
      'contactEmail': contactEmail,
      'notes': notes,
      'viewsCount': viewsCount,
      'clicksCount': clicksCount,
      'country': country,
      'adminWebsite': adminWebsite,
      'rejectionReason': rejectionReason, // Added rejectionReason to the map

      'spamScore': spamScore,
    };
  }

  factory WebsiteModel.fromMap(Map<String, dynamic> map) {
    return WebsiteModel(
      websiteId: map['websiteId'],
      url: map['url'] as String,
      domainName: map['domainName'] as String,
      language: map['language'],
      publisherId: map['publisherId'],
      isSponsored: map['isSponsored'] as bool,
      categories: List<String>.from(map['categories']),
      da: map['da'] as int,
      dr: map['dr'] as int,
      traffic: map['traffic'] as int,
      pricing: (map['pricing'] as num).toDouble(),
      basePricing: (map['basePricing'] as num).toDouble(),
      specialTopicsAdditionalPrice:
          (map['specialTopicsAdditionalPrice'] as num).toDouble(),
      hasSpecialTopicsPricing: map['hasSpecialTopicsPricing'] as bool,
      backlinkType: map['backlinkType'] as String,
      dofollowCost: map['dofollowCost'] != null
          ? (map['dofollowCost'] as num).toDouble()
          : 0.0,
      allowedTopics: List<String>.from(map['allowedTopics']),
      disallowedTopics: List<String>.from(map['disallowedTopics']),
      wordCountMin: map['wordCountMin'] as int,
      wordCountMax: map['wordCountMax'] as int,
      maxLinks: map['maxLinks'] as int,
      status: map['status'] as String,
      submissionDate: map['submissionDate'] as Timestamp,
      createdAt: map['createdAt'] as Timestamp,
      lastUpdated: map['lastUpdated'] as Timestamp,
      isActive: map['isActive'] as bool,
      contentType: List<String>.from(map['contentType']),
      contactEmail: map['contactEmail'] as String,
      notes: map['notes'] as String,
      viewsCount: map['viewsCount'] as int,
      clicksCount: map['clicksCount'] as int,
      country: map['country'] as String,
      adminWebsite: map['adminWebsite'] as bool,
      rejectionReason:
          map['rejectionReason'], // Added rejectionReason to the factory
      spamScore: map['spamScore'] != null
          ? (map['spamScore'] as num).toDouble()
          : null,
    );
  }

  /// Creates a copy of this WebsiteModel with the given fields replaced with the new values.
  WebsiteModel copyWith({
    String? websiteId,
    String? url,
    String? domainName,
    String? language,
    String? publisherId,
    bool? isSponsored,
    List<String>? categories,
    int? da,
    int? dr,
    int? traffic,
    double? pricing,
    double? basePricing,
    double? specialTopicsAdditionalPrice,
    bool? hasSpecialTopicsPricing,
    String? backlinkType,
    double? dofollowCost,
    List<String>? allowedTopics,
    List<String>? disallowedTopics,
    int? wordCountMin,
    int? wordCountMax,
    int? maxLinks,
    String? status,
    Timestamp? submissionDate,
    Timestamp? createdAt,
    Timestamp? lastUpdated,
    bool? isActive,
    List<String>? contentType,
    String? contactEmail,
    String? notes,
    int? viewsCount,
    int? clicksCount,
    String? country,
    bool? adminWebsite,
    String? rejectionReason,
    double? spamScore,
  }) {
    return WebsiteModel(
      websiteId: websiteId ?? this.websiteId,
      url: url ?? this.url,
      domainName: domainName ?? this.domainName,
      language: language ?? this.language,
      publisherId: publisherId ?? this.publisherId,
      isSponsored: isSponsored ?? this.isSponsored,
      categories: categories ?? this.categories,
      da: da ?? this.da,
      dr: dr ?? this.dr,
      traffic: traffic ?? this.traffic,
      pricing: pricing ?? this.pricing,
      basePricing: basePricing ?? this.basePricing,
      specialTopicsAdditionalPrice:
          specialTopicsAdditionalPrice ?? this.specialTopicsAdditionalPrice,
      hasSpecialTopicsPricing:
          hasSpecialTopicsPricing ?? this.hasSpecialTopicsPricing,
      backlinkType: backlinkType ?? this.backlinkType,
      dofollowCost: dofollowCost ?? this.dofollowCost,
      allowedTopics: allowedTopics ?? this.allowedTopics,
      disallowedTopics: disallowedTopics ?? this.disallowedTopics,
      wordCountMin: wordCountMin ?? this.wordCountMin,
      wordCountMax: wordCountMax ?? this.wordCountMax,
      maxLinks: maxLinks ?? this.maxLinks,
      status: status ?? this.status,
      submissionDate: submissionDate ?? this.submissionDate,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isActive: isActive ?? this.isActive,
      contentType: contentType ?? this.contentType,
      contactEmail: contactEmail ?? this.contactEmail,
      notes: notes ?? this.notes,
      viewsCount: viewsCount ?? this.viewsCount,
      clicksCount: clicksCount ?? this.clicksCount,
      country: country ?? this.country,
      adminWebsite: adminWebsite ?? this.adminWebsite,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      spamScore: spamScore ?? this.spamScore,
    );
  }
}
