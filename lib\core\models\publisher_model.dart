// // TODO Implement this library.
// class PublisherModel {
//   final String publisherId;
//   final String name;
//   final String email;
//   final String status; // e.g., Active, Inactive
//   final List<String> assignedOrderIds;

//   PublisherModel({
//     required this.publisherId,
//     required this.name,
//     required this.email,
//     required this.status,
//     required this.assignedOrderIds,
//   });

//   factory PublisherModel.fromMap(Map<String, dynamic> map) {
//     return PublisherModel(
//       publisherId: map['publisherId'] ?? '',
//       name: map['name'] ?? 'Unknown',
//       email: map['email'] ?? 'N/A',
//       status: map['status'] ?? 'Active',
//       assignedOrderIds: List<String>.from(map['assignedOrderIds'] ?? []),
//     );
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'publisherId': publisherId,
//       'name': name,
//       'email': email,
//       'status': status,
//       'assignedOrderIds': assignedOrderIds,
//     };
//   }
// }
