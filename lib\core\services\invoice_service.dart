import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:guest_posts_buyer/core/models/invoice_model.dart';

class InvoiceService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  // Create a new invoice
  Future<String> createInvoice(InvoiceModel invoice) async {
    try {
      // Add invoice to Firestore
      final docRef =
          await _firestore.collection('invoices').add(invoice.toMap());

      // Also add to user's invoices subcollection
      await _firestore
          .collection('users')
          .doc(invoice.userId)
          .collection('invoices')
          .doc(docRef.id)
          .set({
        ...invoice.toMap(),
        'invoiceId': docRef.id,
      });

      // Generate PDF and send email via Cloud Function
      await _generateInvoicePdf(docRef.id);

      return docRef.id;
    } catch (e) {
      print('Error creating invoice: $e');
      throw Exception('Failed to create invoice: $e');
    }
  }

  // Get invoice by ID
  Future<InvoiceModel?> getInvoice(String invoiceId) async {
    try {
      final doc = await _firestore.collection('invoices').doc(invoiceId).get();
      if (doc.exists) {
        return InvoiceModel.fromMap(doc.data()!, id: doc.id);
      }
      return null;
    } catch (e) {
      print('Error getting invoice: $e');
      throw Exception('Failed to get invoice: $e');
    }
  }

  // Get all invoices for a user
  Stream<List<InvoiceModel>> getUserInvoices(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('invoices')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => InvoiceModel.fromMap(doc.data(), id: doc.id))
            .toList());
  }

  // Get all invoices for current user
  Stream<List<InvoiceModel>> getCurrentUserInvoices() {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value([]);
    }
    return getUserInvoices(user.uid);
  }

  // Get invoices by type for current user
  Stream<List<InvoiceModel>> getCurrentUserInvoicesByType(InvoiceType type) {
    final user = _auth.currentUser;
    if (user == null) {
      return Stream.value([]);
    }

    String invoiceTypeString;
    switch (type) {
      case InvoiceType.order:
        invoiceTypeString = 'order';
        break;
      case InvoiceType.withdrawal:
        invoiceTypeString = 'withdrawal';
        break;
      case InvoiceType.deposit:
      default:
        invoiceTypeString = 'deposit';
        break;
    }

    return _firestore
        .collection('users')
        .doc(user.uid)
        .collection('invoices')
        .where('invoiceType', isEqualTo: invoiceTypeString)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => InvoiceModel.fromMap(doc.data(), id: doc.id))
            .toList());
  }

  // Get all invoices (admin only)
  Stream<List<InvoiceModel>> getAllInvoices() {
    return _firestore
        .collection('invoices')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => InvoiceModel.fromMap(doc.data(), id: doc.id))
            .toList());
  }

  // Generate PDF for invoice
  Future<void> _generateInvoicePdf(String invoiceId) async {
    try {
      final callable = _functions.httpsCallable('generateInvoicePdf');
      await callable.call({
        'invoiceId': invoiceId,
      });
    } catch (e) {
      print('Error generating invoice PDF: $e');
      // Don't throw here, as this is a background task
    }
  }

  // Regenerate invoice PDF (admin only)
  Future<void> regenerateInvoicePdf(String invoiceId) async {
    try {
      final callable = _functions.httpsCallable('generateInvoicePdf');
      await callable.call({
        'invoiceId': invoiceId,
      });
    } catch (e) {
      print('Error regenerating invoice PDF: $e');
      throw Exception('Failed to regenerate invoice PDF: $e');
    }
  }

  // Download invoice PDF
  Future<String> getInvoicePdfUrl(String invoiceId) async {
    try {
      final doc = await _firestore.collection('invoices').doc(invoiceId).get();
      if (doc.exists && doc.data()?['pdfUrl'] != null) {
        return doc.data()!['pdfUrl'];
      }
      throw Exception('Invoice PDF not found');
    } catch (e) {
      print('Error getting invoice PDF URL: $e');
      throw Exception('Failed to get invoice PDF URL: $e');
    }
  }
}
