class Stat {
  final String id;
  final String number;
  final String label;

  Stat({required this.id, required this.number, required this.label});

  factory Stat.fromFirestore(Map<String, dynamic> data, String id) {
    return Stat(
      id: id,
      number: data['number'] ?? '',
      label: data['label'] ?? '',
    );
  }
}

class Service {
  final String id;
  final String icon;
  final String title;
  final String description;
  final List<String> features;

  Service({
    required this.id,
    required this.icon,
    required this.title,
    required this.description,
    required this.features,
  });

  factory Service.fromFirestore(Map<String, dynamic> data, String id) {
    return Service(
      id: id,
      icon: data['icon'] ?? 'article',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      features: List<String>.from(data['features'] ?? []),
    );
  }
}

class Testimonial {
  final String id;
  final String name;
  final String position;
  final String comment;
  final int rating;

  Testimonial({
    required this.id,
    required this.name,
    required this.position,
    required this.comment,
    required this.rating,
  });

  factory Testimonial.fromFirestore(Map<String, dynamic> data, String id) {
    return Testimonial(
      id: id,
      name: data['name'] ?? '',
      position: data['position'] ?? '',
      comment: data['comment'] ?? '',
      rating: data['rating'] ?? 0,
    );
  }
}

class FooterSectionData {
  final String id;
  final String title;
  final List<String> items;

  FooterSectionData(
      {required this.id, required this.title, required this.items});

  factory FooterSectionData.fromFirestore(
      Map<String, dynamic> data, String id) {
    return FooterSectionData(
      id: id,
      title: data['title'] ?? '',
      items: List<String>.from(data['items'] ?? []),
    );
  }
}

class HeroData {
  final String title;
  final String subtitle;
  final String imageUrl;

  HeroData(
      {required this.title, required this.subtitle, required this.imageUrl});

  factory HeroData.fromFirestore(Map<String, dynamic> data) {
    return HeroData(
      title: data['title'] ?? 'Premium Guest\nPosting Platform',
      subtitle: data['subtitle'] ??
          "Boost your website's authority with high-quality\nguest posts on premium websites.",
      imageUrl: data['imageUrl'] ?? 'https://your-image-url.com/hero.png',
    );
  }
}
