// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:csv/csv.dart';
import 'package:universal_html/html.dart' as html;
import 'package:fl_chart/fl_chart.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class PaymentsWithdrawalsScreen extends StatefulWidget {
  const PaymentsWithdrawalsScreen({super.key});

  @override
  State<PaymentsWithdrawalsScreen> createState() =>
      _PaymentsWithdrawalsScreenState();
}

class _PaymentsWithdrawalsScreenState extends State<PaymentsWithdrawalsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFunctions _functions = FirebaseFunctions.instance;
  String? _adminUid;
  bool _isLoading = true;
  double _mainBalance = 0;
  List<OrderModel> _completedOrders = [];
  List<Map<String, dynamic>> _withdrawalRequests = [];
  List<Map<String, dynamic>> _paymentHistory = [];
  List<Map<String, dynamic>> _notifications = [];
  int _unreadNotifications = 0;
  int _currentPage = 1; // For Payment History pagination
  int _currentWithdrawalsPage = 1; // For Withdrawal Requests pagination
  int _currentEarningsPage = 1; // For Publisher Earnings pagination
  final int _itemsPerPage = 5;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<QuerySnapshot>? _withdrawalsSubscription;
  StreamSubscription<QuerySnapshot>? _historySubscription;
  StreamSubscription<QuerySnapshot>? _notificationsSubscription;

  // Analytics state variables
  String _selectedTimePeriod = 'Monthly'; // Default time period
  List<String> _timePeriods = ['Daily', 'Weekly', 'Monthly', 'Yearly'];
  Map<String, double> _revenueByCategory = {};
  Map<String, double> _revenueByRegion = {};
  double _averageOrderValue = 0;
  double _currentMonthRevenue = 0;
  double _previousMonthRevenue = 0;
  double _currentQuarterRevenue = 0;
  double _previousQuarterRevenue = 0;
  double _currentYearRevenue = 0;
  double _previousYearRevenue = 0;
  double _currentMonthProfit = 0;
  double _previousMonthProfit = 0;
  double _currentQuarterProfit = 0;
  double _previousQuarterProfit = 0;
  double _currentYearProfit = 0;
  double _previousYearProfit = 0;
  List<FlSpot> _revenueChartData = [];
  List<FlSpot> _orderCountChartData = [];

  // Date filtering state variables
  String _selectedDatePeriod = 'Last 28 Days';
  List<String> _datePeriods = [
    'Today',
    'Last 28 Days',
    'Last 3 Months',
    'Last Year',
    'Lifetime'
  ];
  DateTime? _customStartDate;
  DateTime? _customEndDate;
  bool _isCustomDateRange = false;
  bool _isFilteringData = false;

  // Commission state variables
  double _commissionBalance = 0;
  double _confirmedCommissions = 0;
  double _totalCommissions = 0;
  double _totalBalance = 0; // Sum of main balance and commission
  DateTime? _commissionCreatedAt;
  DateTime? _commissionLastUpdated;
  List<Map<String, dynamic>> _commissionHistory = [];
  int _currentCommissionPage = 1; // For Commission History pagination
  StreamSubscription<DocumentSnapshot>? _financeSubscription;
  StreamSubscription<QuerySnapshot>? _commissionHistorySubscription;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
  }

  // Get date range based on selected period
  Map<String, DateTime?> _getDateRange() {
    final now = DateTime.now();
    DateTime? startDate;
    DateTime? endDate = now;

    if (_isCustomDateRange) {
      return {
        'start': _customStartDate,
        'end': _customEndDate,
      };
    }

    switch (_selectedDatePeriod) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case 'Last 28 Days':
        startDate = now.subtract(const Duration(days: 28));
        break;
      case 'Last 3 Months':
        startDate = DateTime(now.year, now.month - 3, now.day);
        break;
      case 'Last Year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      case 'Lifetime':
        startDate = null;
        endDate = null;
        break;
    }

    return {
      'start': startDate,
      'end': endDate,
    };
  }

  // Filter orders based on date range
  List<OrderModel> _getFilteredOrders() {
    final dateRange = _getDateRange();
    final startDate = dateRange['start'];
    final endDate = dateRange['end'];

    if (startDate == null && endDate == null) {
      return _completedOrders; // Return all orders for 'Lifetime'
    }

    return _completedOrders.where((order) {
      if (order.completionDate == null) return false;

      final orderDate = order.completionDate!.toDate();

      if (startDate != null && orderDate.isBefore(startDate)) {
        return false;
      }

      if (endDate != null && orderDate.isAfter(endDate)) {
        return false;
      }

      return true;
    }).toList();
  }

  // Filter data based on date range
  List<Map<String, dynamic>> _getFilteredData(
      List<Map<String, dynamic>> data, String dateField) {
    final dateRange = _getDateRange();
    final startDate = dateRange['start'];
    final endDate = dateRange['end'];

    if (startDate == null && endDate == null) {
      return data; // Return all data for 'Lifetime'
    }

    return data.where((item) {
      final timestamp = item[dateField] as Timestamp?;
      if (timestamp == null) return false;

      final itemDate = timestamp.toDate();

      if (startDate != null && itemDate.isBefore(startDate)) {
        return false;
      }

      if (endDate != null && itemDate.isAfter(endDate)) {
        return false;
      }

      return true;
    }).toList();
  }

  // Apply date filters to all data
  void _applyDateFilters() {
    setState(() {
      _isFilteringData = true;

      // Reset all pagination states to avoid RangeError
      _currentPage = 1;
      _currentEarningsPage = 1;
      _currentWithdrawalsPage = 1;
      _currentCommissionPage = 1;
    });

    // Recalculate analytics with filtered data
    _calculateAnalyticsData();

    setState(() {
      _isFilteringData = false;
    });
  }

  // Calculate analytics data based on completed orders
  void _calculateAnalyticsData() {
    if (_completedOrders.isEmpty) return;

    // Get filtered orders based on selected date range
    final filteredOrders = _getFilteredOrders();

    if (filteredOrders.isEmpty) {
      setState(() {
        _averageOrderValue = 0;
        _revenueByCategory = {};
        _revenueByRegion = {};
      });
      return;
    }

    // Calculate Average Order Value (AOV)
    final totalRevenue = filteredOrders.fold<double>(
        0, (total, order) => total + order.totalPrice);
    setState(() {
      _averageOrderValue = totalRevenue / filteredOrders.length;
    });

    // Generate revenue by category (using website categories)
    final categoryMap = <String, double>{};
    for (var order in filteredOrders) {
      if (order.websiteCategories.isEmpty) {
        categoryMap['Uncategorized'] =
            (categoryMap['Uncategorized'] ?? 0) + order.totalPrice;
      } else {
        // Use the first category for simplicity
        final category = order.websiteCategories.first;
        categoryMap[category] = (categoryMap[category] ?? 0) + order.totalPrice;
      }
    }
    setState(() {
      _revenueByCategory = Map.from(categoryMap);
    });

    // Generate revenue by region (using website language as proxy for region)
    final regionMap = <String, double>{};
    for (var order in filteredOrders) {
      final region =
          order.websiteLanguage.isNotEmpty ? order.websiteLanguage : 'Unknown';
      regionMap[region] = (regionMap[region] ?? 0) + order.totalPrice;
    }
    setState(() {
      _revenueByRegion = Map.from(regionMap);
    });

    // Calculate time-based metrics
    _calculateTimeBasedMetrics();

    // Generate chart data
    _generateChartData();
  }

  // Calculate time-based metrics (MTD, QTD, YTD)
  void _calculateTimeBasedMetrics() {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    final previousMonth = DateTime(now.year, now.month - 1);

    final currentQuarter = DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1);
    final previousQuarter = DateTime(
        currentQuarter.month > 3
            ? currentQuarter.year
            : currentQuarter.year - 1,
        currentQuarter.month > 3
            ? currentQuarter.month - 3
            : currentQuarter.month + 9);

    final currentYear = DateTime(now.year);
    final previousYear = DateTime(now.year - 1);

    // Calculate current month revenue
    double currentMonthRev = 0;
    double previousMonthRev = 0;
    double currentQuarterRev = 0;
    double previousQuarterRev = 0;
    double currentYearRev = 0;
    double previousYearRev = 0;

    // Profit is calculated as 30% of revenue for demonstration purposes
    const profitMargin = 0.3;

    // Use filtered orders for time-based calculations
    final filteredOrders = _getFilteredOrders();

    for (var order in filteredOrders) {
      if (order.completionDate == null) continue;

      final orderDate = order.completionDate!.toDate();

      // Month calculations
      if (orderDate.year == currentMonth.year &&
          orderDate.month == currentMonth.month) {
        currentMonthRev += order.totalPrice;
      } else if (orderDate.year == previousMonth.year &&
          orderDate.month == previousMonth.month) {
        previousMonthRev += order.totalPrice;
      }

      // Quarter calculations
      if (orderDate.isAfter(currentQuarter) ||
          (orderDate.year == currentQuarter.year &&
              orderDate.month == currentQuarter.month &&
              orderDate.day >= currentQuarter.day)) {
        currentQuarterRev += order.totalPrice;
      } else if (orderDate.isAfter(previousQuarter) ||
          (orderDate.year == previousQuarter.year &&
              orderDate.month == previousQuarter.month &&
              orderDate.day >= previousQuarter.day)) {
        previousQuarterRev += order.totalPrice;
      }

      // Year calculations
      if (orderDate.year == currentYear.year) {
        currentYearRev += order.totalPrice;
      } else if (orderDate.year == previousYear.year) {
        previousYearRev += order.totalPrice;
      }
    }

    setState(() {
      _currentMonthRevenue = currentMonthRev;
      _previousMonthRevenue = previousMonthRev;
      _currentQuarterRevenue = currentQuarterRev;
      _previousQuarterRevenue = previousQuarterRev;
      _currentYearRevenue = currentYearRev;
      _previousYearRevenue = previousYearRev;

      // Calculate profits
      _currentMonthProfit = currentMonthRev * profitMargin;
      _previousMonthProfit = previousMonthRev * profitMargin;
      _currentQuarterProfit = currentQuarterRev * profitMargin;
      _previousQuarterProfit = previousQuarterRev * profitMargin;
      _currentYearProfit = currentYearRev * profitMargin;
      _previousYearProfit = previousYearRev * profitMargin;
    });
  }

  // Generate chart data for revenue visualization
  void _generateChartData() {
    // Use filtered orders for chart data
    final filteredOrders = _getFilteredOrders();

    // Sort orders by date
    final sortedOrders = List<OrderModel>.from(filteredOrders);
    sortedOrders.sort((a, b) {
      if (a.completionDate == null || b.completionDate == null) return 0;
      return a.completionDate!.compareTo(b.completionDate!);
    });

    // Group by month for chart data
    final monthlyData = <DateTime, double>{};
    final monthlyCount = <DateTime, int>{};

    for (var order in sortedOrders) {
      if (order.completionDate == null) continue;

      final date = order.completionDate!.toDate();
      final monthKey = DateTime(date.year, date.month);

      monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + order.totalPrice;
      monthlyCount[monthKey] = (monthlyCount[monthKey] ?? 0) + 1;
    }

    // Convert to chart data points
    final revenuePoints = <FlSpot>[];
    final countPoints = <FlSpot>[];

    var index = 0;
    monthlyData.forEach((date, revenue) {
      revenuePoints.add(FlSpot(index.toDouble(), revenue));
      countPoints.add(FlSpot(index.toDouble(), monthlyCount[date]!.toDouble()));
      index++;
    });

    setState(() {
      _revenueChartData = revenuePoints;
      _orderCountChartData = countPoints;
    });
  }

  // Format percentage change with + or - sign
  String _formatPercentageChange(double current, double previous) {
    if (previous == 0) return '+100%';

    final percentChange = ((current - previous) / previous) * 100;
    final sign = percentChange >= 0 ? '+' : '';
    return '$sign${percentChange.toStringAsFixed(1)}%';
  }

  // Get color based on percentage change
  Color _getChangeColor(double current, double previous) {
    if (previous == 0) return AppColors.green;
    return current >= previous ? AppColors.green : AppColors.red;
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    try {
      if (user == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      setState(() {
        _adminUid = user.uid;
      });

      await _loadData();
      _subscribeToData();
      await _loadCommissionData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking admin access: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadData() async {
    try {
      if (!mounted) return;

      final userDoc = await _firestore.collection('users').doc(_adminUid).get();
      if (!userDoc.exists) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User data not found'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      if (mounted) {
        setState(() {
          _mainBalance = (userDoc.data()?['mainBalance'] ?? 0).toDouble();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _subscribeToData() {
    _ordersSubscription?.cancel();
    _ordersSubscription = _firestore
        .collection('orders')
        .where('status', isEqualTo: 'Completed')
        .snapshots()
        .listen((snapshot) {
      final orders = snapshot.docs
          .map((doc) {
            try {
              return OrderModel.fromMap(doc.data()..['orderId'] = doc.id);
            } catch (e) {
              return null;
            }
          })
          .where((order) => order != null)
          .cast<OrderModel>()
          .toList();
      setState(() {
        _completedOrders = orders;
        // Calculate analytics data after orders are loaded
        _calculateAnalyticsData();
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading orders: $e'),
              backgroundColor: Colors.red),
        );
      }
    });

    _withdrawalsSubscription?.cancel();
    _withdrawalsSubscription = _firestore
        .collection('withdrawals')
        // .where('status', isEqualTo: 'Pending')
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .listen((snapshot) {
      final requests = snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
      setState(() {
        _withdrawalRequests = requests;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading withdrawals: $e'),
              backgroundColor: Colors.red),
        );
      }
    });

    _historySubscription?.cancel();
    _historySubscription = _firestore
        .collectionGroup('history')
        .orderBy('date', descending: true)
        .snapshots()
        .listen((snapshot) {
      final history = snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
      setState(() {
        _paymentHistory = history;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading history: $e'),
              backgroundColor: Colors.red),
        );
      }
    });

    _notificationsSubscription?.cancel();
    _notificationsSubscription = _firestore
        .collection('notifications')
        .where('userId', isEqualTo: _adminUid)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .listen((snapshot) {
      final notifications = snapshot.docs
          .map((doc) => {
                'id': doc.id,
                ...doc.data(),
              })
          .toList();
      setState(() {
        _notifications = notifications;
        _unreadNotifications = notifications.where((n) => !n['read']).length;
      });
    }, onError: (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading notifications: $e'),
              backgroundColor: Colors.red),
        );
      }
    });
  }

  Future<void> _processWithdrawal(String withdrawalId, bool action,
      {String? reason}) async {
    try {
      final callable = _functions.httpsCallable('approveWithdrawal');
      await callable({
        'withdrawalId': withdrawalId,
        'approve': action,
        'reason': reason,
        'adminUid': _adminUid, // Pass the admin UID to verify admin access
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Withdrawal $action successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error processing withdrawal: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _markNotificationRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'read': true,
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error marking notification as read: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }

  // Build Sales & Revenue Metrics Dashboard
  Widget _buildSalesRevenueMetricsDashboard(
      bool isSmallScreen, bool isWideScreen) {
    return Card(
      color: AppColors.componentBackColor,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sales & Revenue Metrics',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w800,
                    color: AppColors.dark,
                  ),
                ),
                // Time period selector
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.blue.withOpacity(0.2)),
                  ),
                  child: DropdownButton<String>(
                    value: _selectedTimePeriod,
                    underline: const SizedBox(),
                    icon: const Icon(Icons.arrow_drop_down,
                        color: AppColors.blue),
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w600,
                    ),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedTimePeriod = newValue;
                        });
                      }
                    },
                    items: _timePeriods
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Average Order Value (AOV) card
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blue.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Average Order Value (AOV)',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.w700,
                          color: AppColors.dark,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.trending_up,
                          color: AppColors.blue,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '\$${_averageOrderValue.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 24 : 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Based on ${_completedOrders.length} completed orders',
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      color: AppColors.grey,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Revenue breakdown by category
            Text(
              'Revenue by Category',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.w700,
                color: AppColors.dark,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blue.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: _revenueByCategory.isEmpty
                  ? const Center(
                      child: Text(
                        'No category data available',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 14,
                          color: AppColors.grey,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _revenueByCategory.length,
                      itemBuilder: (context, index) {
                        final category =
                            _revenueByCategory.keys.elementAt(index);
                        final revenue = _revenueByCategory[category]!;
                        final totalRevenue = _revenueByCategory.values
                            .fold<double>(0, (total, value) => total + value);
                        final percentage = (revenue / totalRevenue) * 100;

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    category,
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                  Text(
                                    '\$${revenue.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%)',
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 6),
                              LinearProgressIndicator(
                                value: percentage / 100,
                                backgroundColor:
                                    AppColors.grey.withOpacity(0.2),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  _getCategoryColor(index),
                                ),
                                borderRadius: BorderRadius.circular(4),
                                minHeight: 8,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 24),

            // Geographic revenue analysis
            Text(
              'Revenue by Region',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.w700,
                color: AppColors.dark,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.blue.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: _revenueByRegion.isEmpty
                  ? const Center(
                      child: Text(
                        'No region data available',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 14,
                          color: AppColors.grey,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _revenueByRegion.length,
                      itemBuilder: (context, index) {
                        final region = _revenueByRegion.keys.elementAt(index);
                        final revenue = _revenueByRegion[region]!;
                        final totalRevenue = _revenueByRegion.values
                            .fold<double>(0, (total, value) => total + value);
                        final percentage = (revenue / totalRevenue) * 100;

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    region,
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                  Text(
                                    '\$${revenue.toStringAsFixed(2)} (${percentage.toStringAsFixed(1)}%)',
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 6),
                              LinearProgressIndicator(
                                value: percentage / 100,
                                backgroundColor:
                                    AppColors.grey.withOpacity(0.2),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  _getRegionColor(index),
                                ),
                                borderRadius: BorderRadius.circular(4),
                                minHeight: 8,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),

            // Export button
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton.icon(
                onPressed: () => _exportToCsv('analytics'),
                icon: const Icon(Icons.download, color: Colors.white),
                label: const Text(
                  'Export Report',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.blue,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build Financial Analytics Summary
  Widget _buildFinancialAnalyticsSummary(
      bool isSmallScreen, bool isWideScreen) {
    return Card(
      color: AppColors.componentBackColor,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Analytics Summary',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 20 : 24,
                fontWeight: FontWeight.w800,
                color: AppColors.dark,
              ),
            ),
            const SizedBox(height: 24),

            // Revenue metrics grid
            GridView.count(
              crossAxisCount: isSmallScreen ? 1 : 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: isSmallScreen ? 2.5 : 1.5,
              children: [
                // Month-to-Date Revenue
                _buildMetricComparisonCard(
                  title: 'Month-to-Date Revenue',
                  currentValue: _currentMonthRevenue,
                  previousValue: _previousMonthRevenue,
                  icon: Icons.calendar_today,
                  color: AppColors.blue,
                  isSmallScreen: isSmallScreen,
                ),

                // Quarter-to-Date Revenue
                _buildMetricComparisonCard(
                  title: 'Quarter-to-Date Revenue',
                  currentValue: _currentQuarterRevenue,
                  previousValue: _previousQuarterRevenue,
                  icon: Icons.calendar_view_month,
                  color: AppColors.green,
                  isSmallScreen: isSmallScreen,
                ),

                // Year-to-Date Revenue
                _buildMetricComparisonCard(
                  title: 'Year-to-Date Revenue',
                  currentValue: _currentYearRevenue,
                  previousValue: _previousYearRevenue,
                  icon: Icons.calendar_today_outlined,
                  color: AppColors.yellow,
                  isSmallScreen: isSmallScreen,
                ),

                // Month-to-Date Profit
                _buildMetricComparisonCard(
                  title: 'Month-to-Date Profit',
                  currentValue: _currentMonthProfit,
                  previousValue: _previousMonthProfit,
                  icon: Icons.attach_money,
                  color: AppColors.blue,
                  isSmallScreen: isSmallScreen,
                ),

                // Quarter-to-Date Profit
                _buildMetricComparisonCard(
                  title: 'Quarter-to-Date Profit',
                  currentValue: _currentQuarterProfit,
                  previousValue: _previousQuarterProfit,
                  icon: Icons.attach_money,
                  color: AppColors.green,
                  isSmallScreen: isSmallScreen,
                ),

                // Year-to-Date Profit
                _buildMetricComparisonCard(
                  title: 'Year-to-Date Profit',
                  currentValue: _currentYearProfit,
                  previousValue: _previousYearProfit,
                  icon: Icons.attach_money,
                  color: AppColors.yellow,
                  isSmallScreen: isSmallScreen,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Export button
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton.icon(
                onPressed: () => _exportToCsv('financial'),
                icon: const Icon(Icons.download, color: Colors.white),
                label: const Text(
                  'Export Financial Report',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.blue,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper widget for metric comparison cards
  Widget _buildMetricComparisonCard({
    required String title,
    required double currentValue,
    required double previousValue,
    required IconData icon,
    required Color color,
    required bool isSmallScreen,
  }) {
    final percentChange = previousValue > 0
        ? ((currentValue - previousValue) / previousValue) * 100
        : 100.0;
    final isPositive = percentChange >= 0;
    final changeText =
        '${isPositive ? '+' : ''}${percentChange.toStringAsFixed(1)}%';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.dark,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '\$${currentValue.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 18 : 22,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                color: isPositive ? AppColors.green : AppColors.red,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                changeText,
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isPositive ? AppColors.green : AppColors.red,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'vs previous',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 12,
                  color: AppColors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to get color for category
  Color _getCategoryColor(int index) {
    final colors = [
      AppColors.blue,
      AppColors.green,
      AppColors.yellow,
      AppColors.red,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }

  // Helper method to get color for region
  Color _getRegionColor(int index) {
    final colors = [
      AppColors.blue,
      AppColors.green,
      AppColors.yellow,
      AppColors.red,
      Colors.purple,
      Colors.orange,
      Colors.teal,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }

  void _exportToCsv(String type) {
    List<List<dynamic>> rows;
    String filename;

    // Generate date range suffix for filename
    String dateRangeStr = '';
    if (_selectedDatePeriod != 'Lifetime') {
      dateRangeStr = '_${_selectedDatePeriod.replaceAll(' ', '_')}';
    }
    if (_isCustomDateRange &&
        _customStartDate != null &&
        _customEndDate != null) {
      dateRangeStr =
          '_${DateFormat('ddMMyyyy').format(_customStartDate!)}_to_${DateFormat('ddMMyyyy').format(_customEndDate!)}';
    }

    switch (type) {
      case 'earnings':
        final filteredOrders = _getFilteredOrders();
        rows = [
          ['Order ID', 'Website', 'Amount', 'Date'],
          ...filteredOrders.map((order) => [
                order.orderId,
                order.websiteUrl,
                order.totalPrice,
                _formatDate(order.completionDate),
              ]),
        ];
        filename =
            'earnings${dateRangeStr}_${DateTime.now().toIso8601String()}.csv';
        break;
      case 'withdrawals':
        final filteredWithdrawals =
            _getFilteredData(_withdrawalRequests, 'requestedAt');
        rows = [
          ['Withdrawal ID', 'User', 'Amount', 'PayPal Email', 'Status', 'Date'],
          ...filteredWithdrawals.map((req) => [
                req['withdrawalId'],
                req['userId'],
                req['amount'],
                req['paypalEmail'],
                req['status'],
                _formatDate(req['requestedAt']),
              ]),
        ];
        filename =
            'withdrawals${dateRangeStr}_${DateTime.now().toIso8601String()}.csv';
        break;
      case 'history':
        final filteredHistory = _getFilteredData(_paymentHistory, 'date');
        rows = [
          ['ID', 'Description', 'Amount', 'Status', 'Type', 'Date'],
          ...filteredHistory.map((item) => [
                item['id'],
                item['description'],
                item['amount'],
                item['status'],
                item['type'],
                _formatDate(item['date']),
              ]),
        ];
        filename =
            'history${dateRangeStr}_${DateTime.now().toIso8601String()}.csv';
        break;
      case 'commission':
        final filteredCommissions =
            _getFilteredData(_commissionHistory, 'date');
        rows = [
          [
            'Commission ID',
            'Order ID',
            'Description',
            'Amount',
            'Status',
            'Date'
          ],
          ...filteredCommissions.map((item) => [
                item['id'],
                item['orderId'],
                item['description'],
                item['amount'],
                item['status'],
                _formatDate(item['date']),
              ]),
        ];
        filename =
            'commission_history${dateRangeStr}_${DateTime.now().toIso8601String()}.csv';
        break;
      case 'analytics':
        // Export analytics data
        rows = [
          ['Category', 'Revenue', 'Percentage'],
          ..._revenueByCategory.entries.map((entry) {
            final totalRevenue = _revenueByCategory.values
                .fold<double>(0, (total, value) => total + value);
            final percentage = (entry.value / totalRevenue) * 100;
            return [
              entry.key,
              entry.value,
              '${percentage.toStringAsFixed(1)}%',
            ];
          }),
          [''], // Empty row as separator
          ['Region', 'Revenue', 'Percentage'],
          ..._revenueByRegion.entries.map((entry) {
            final totalRevenue = _revenueByRegion.values
                .fold<double>(0, (total, value) => total + value);
            final percentage = (entry.value / totalRevenue) * 100;
            return [
              entry.key,
              entry.value,
              '${percentage.toStringAsFixed(1)}%',
            ];
          }),
        ];
        filename = 'sales_analytics_${DateTime.now().toIso8601String()}.csv';
        break;
      case 'financial':
        // Export financial metrics
        rows = [
          ['Metric', 'Current Value', 'Previous Value', 'Change (%)'],
          [
            'Month-to-Date Revenue',
            _currentMonthRevenue,
            _previousMonthRevenue,
            _formatPercentageChange(
                _currentMonthRevenue, _previousMonthRevenue),
          ],
          [
            'Quarter-to-Date Revenue',
            _currentQuarterRevenue,
            _previousQuarterRevenue,
            _formatPercentageChange(
                _currentQuarterRevenue, _previousQuarterRevenue),
          ],
          [
            'Year-to-Date Revenue',
            _currentYearRevenue,
            _previousYearRevenue,
            _formatPercentageChange(_currentYearRevenue, _previousYearRevenue),
          ],
          [
            'Month-to-Date Profit',
            _currentMonthProfit,
            _previousMonthProfit,
            _formatPercentageChange(_currentMonthProfit, _previousMonthProfit),
          ],
          [
            'Quarter-to-Date Profit',
            _currentQuarterProfit,
            _previousQuarterProfit,
            _formatPercentageChange(
                _currentQuarterProfit, _previousQuarterProfit),
          ],
          [
            'Year-to-Date Profit',
            _currentYearProfit,
            _previousYearProfit,
            _formatPercentageChange(_currentYearProfit, _previousYearProfit),
          ],
          [''], // Empty row as separator
          ['Commission Metrics', '', '', ''],
          [
            'Commission Balance',
            _commissionBalance,
            '',
            '',
          ],
          [
            'Confirmed Commissions',
            _confirmedCommissions,
            '',
            '',
          ],
          [
            'Total Commissions',
            _totalCommissions,
            '',
            '',
          ],
          [
            'Total Balance (Main + Commission)',
            _totalBalance,
            '',
            '',
          ],
        ];
        filename = 'financial_metrics_${DateTime.now().toIso8601String()}.csv';
        break;
      default:
        return;
    }

    String csv = const ListToCsvConverter().convert(rows);
    final bytes = utf8.encode(csv);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download = filename;
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    _withdrawalsSubscription?.cancel();
    _historySubscription?.cancel();
    _notificationsSubscription?.cancel();
    _financeSubscription?.cancel();
    _commissionHistorySubscription?.cancel();
    super.dispose();
  }

  // Build Commission History Table
  Widget _buildCommissionHistoryTable(bool isSmallScreen, bool isWideScreen) {
    final filteredCommissions = _getFilteredData(_commissionHistory, 'date');
    final totalCommissionPages = filteredCommissions.isEmpty
        ? 1
        : (filteredCommissions.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentCommissionPage > totalCommissionPages) {
      _currentCommissionPage = 1;
    }

    final startIndex = (_currentCommissionPage - 1) * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, filteredCommissions.length);
    final pagedCommissionHistory = filteredCommissions.isEmpty
        ? <Map<String, dynamic>>[]
        : filteredCommissions.sublist(startIndex, endIndex);

    return Card(
      color: Colors.white,
      elevation: 2,
      shadowColor: AppColors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Commission History',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w800,
                    color: AppColors.dark,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _exportToCsv('commission'),
                  icon:
                      const Icon(Icons.download, color: Colors.white, size: 18),
                  label: const Text(
                    'Export CSV',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: isWideScreen
                    ? 48
                    : isSmallScreen
                        ? 20
                        : 32,
                headingRowColor: MaterialStateProperty.all(
                  AppColors.blueLightest.withOpacity(0.7),
                ),
                dataRowColor: MaterialStateProperty.resolveWith((states) {
                  final index = pagedCommissionHistory.isEmpty
                      ? 0
                      : pagedCommissionHistory
                          .indexOf(pagedCommissionHistory.firstWhere(
                          (item) => states.contains(MaterialState.selected),
                          orElse: () => pagedCommissionHistory.first,
                        ));
                  return _getRowBackgroundColor(index, states);
                }),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blueLightest),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.blue.withOpacity(0.03),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'Commission ID',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Order ID',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Description',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amount',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Status',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Date',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                ],
                rows: pagedCommissionHistory.isEmpty
                    ? [
                        DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: isWideScreen
                                    ? 600
                                    : isSmallScreen
                                        ? 300
                                        : 400,
                                child: const Text(
                                  'No commission history found',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            ),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                          ],
                        ),
                      ]
                    : pagedCommissionHistory.map((item) {
                        return DataRow(
                          cells: [
                            DataCell(
                              Text(
                                item['id'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                item['orderId'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                item['description'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                item['amount'] != null
                                    ? '\$${item['amount'].toStringAsFixed(2)}'
                                    : 'N/A',
                                style:
                                    _cellStyle(isSmallScreen, isNumeric: true),
                              ),
                            ),
                            DataCell(
                              _buildStatusBadge(
                                  item['status'] ?? 'N/A', isSmallScreen),
                            ),
                            DataCell(
                              Text(
                                _formatDate(item['date']),
                                style: _cellStyle(isSmallScreen, isDate: true),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
              ),
            ),
            if (totalCommissionPages > 1)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildActionButton(
                      icon: Icons.chevron_left,
                      onPressed: _currentCommissionPage > 1
                          ? () => setState(() => _currentCommissionPage--)
                          : () {},
                      color: _currentCommissionPage > 1
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Previous page',
                      isSmallScreen: isSmallScreen,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Page $_currentCommissionPage of $totalCommissionPages',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 16,
                          color: AppColors.dark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    _buildActionButton(
                      icon: Icons.chevron_right,
                      onPressed: _currentCommissionPage < totalCommissionPages
                          ? () => setState(() => _currentCommissionPage++)
                          : () {},
                      color: _currentCommissionPage < totalCommissionPages
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Next page',
                      isSmallScreen: isSmallScreen,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper method to get status color
  Color _getStatusColor(String? status) {
    switch (status) {
      case 'Completed':
        return AppColors.green;
      case 'Pending':
        return AppColors.yellow;
      case 'Failed':
        return AppColors.red;
      default:
        return AppColors.grey;
    }
  }

  // Load commission data from Firestore
  Future<void> _loadCommissionData() async {
    try {
      if (!mounted) return;

      // Subscribe to finance document
      _financeSubscription?.cancel();
      _financeSubscription =
          _firestore.doc('admin/finances').snapshots().listen((snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data()!;
          setState(() {
            _commissionBalance = (data['balance'] ?? 0).toDouble();
            _confirmedCommissions =
                (data['confirmedCommissions'] ?? 0).toDouble();
            _totalCommissions = (data['totalCommissions'] ?? 0).toDouble();
            _totalBalance = _mainBalance + _commissionBalance;

            if (data['createdAt'] != null) {
              _commissionCreatedAt = (data['createdAt'] as Timestamp).toDate();
            }

            if (data['lastUpdated'] != null) {
              _commissionLastUpdated =
                  (data['lastUpdated'] as Timestamp).toDate();
            }
          });
        }
      }, onError: (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading commission data: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });

      // Subscribe to commission history subcollection
      _commissionHistorySubscription?.cancel();
      _commissionHistorySubscription = _firestore
          .collection('admin/finances/history')
          .orderBy('date', descending: true)
          .snapshots()
          .listen((snapshot) {
        final history = snapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList();
        setState(() {
          _commissionHistory = history;
        });
      }, onError: (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading commission history: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading commission data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 600;
        final isWideScreen = screenWidth > 800;

        // Use filtered payment history
        final filteredHistory = _getFilteredData(_paymentHistory, 'date');
        final startIndex = (_currentPage - 1) * _itemsPerPage;
        final endIndex =
            (startIndex + _itemsPerPage).clamp(0, filteredHistory.length);
        final pagedHistory = filteredHistory.sublist(startIndex, endIndex);

        return Scaffold(
          backgroundColor: const Color.fromARGB(255, 255, 255, 255),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Payments & Withdrawals',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: isSmallScreen ? 28 : 36,
                              fontWeight: FontWeight.w800,
                              color: AppColors.dark,
                            ),
                          ),
                          Row(
                            children: [
                              Stack(
                                children: [
                                  IconButton(
                                    icon: Icon(
                                      Icons.notifications,
                                      color: AppColors.grey,
                                      size: 28,
                                    ),
                                    onPressed: () => _showNotificationsDialog(),
                                    padding: const EdgeInsets.all(12),
                                  ),
                                  if (_unreadNotifications > 0)
                                    Positioned(
                                      right: 6,
                                      top: 6,
                                      child: CircleAvatar(
                                        radius: 10,
                                        backgroundColor: AppColors.red,
                                        child: Text(
                                          '$_unreadNotifications',
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              Tooltip(
                                message: 'Help',
                                child: IconButton(
                                  icon: Icon(
                                    Icons.help_outline,
                                    color: AppColors.grey,
                                    size: 28,
                                  ),
                                  onPressed: () {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                            'Help documentation coming soon!'),
                                      ),
                                    );
                                  },
                                  padding: const EdgeInsets.all(12),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      _buildDateFilterControls(isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildSummaryCards(screenWidth),
                      const SizedBox(height: 24),
                      _buildSalesRevenueMetricsDashboard(
                          isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildFinancialAnalyticsSummary(
                          isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildCommissionHistoryTable(isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildEarningsTable(isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildWithdrawalRequestsTable(
                          isSmallScreen, isWideScreen),
                      const SizedBox(height: 24),
                      _buildPaymentHistoryTable(
                          pagedHistory, isSmallScreen, isWideScreen),
                    ],
                  ),
                ),
        );
      },
    );
  }

  // Build date filter controls
  Widget _buildDateFilterControls(bool isSmallScreen, bool isWideScreen) {
    return Card(
      color: AppColors.componentBackColor,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date Period Selection',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w700,
                color: AppColors.dark,
              ),
            ),
            const SizedBox(height: 16),

            // Date filter controls
            Wrap(
              spacing: 16,
              runSpacing: 12,
              children: [
                // Predefined periods dropdown
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.blue.withOpacity(0.2)),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.blue.withOpacity(0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: DropdownButton<String>(
                    value: _selectedDatePeriod,
                    underline: const SizedBox(),
                    icon: const Icon(Icons.arrow_drop_down,
                        color: AppColors.blue),
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w600,
                    ),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedDatePeriod = newValue;
                          _isCustomDateRange = false;
                          _customStartDate = null;
                          _customEndDate = null;
                        });
                        _applyDateFilters();
                      }
                    },
                    items: _datePeriods
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
                ),

                // Custom date range button
                ElevatedButton.icon(
                  onPressed: () => _selectCustomDateRange(),
                  icon: Icon(
                    _isCustomDateRange
                        ? Icons.date_range
                        : Icons.calendar_today,
                    color: Colors.white,
                    size: 18,
                  ),
                  label: Text(
                    _isCustomDateRange ? 'Custom Range' : 'Select Custom Range',
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _isCustomDateRange ? AppColors.green : AppColors.blue,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),

                // Export all data button
                ElevatedButton.icon(
                  onPressed: () => _showExportDialog(),
                  icon:
                      const Icon(Icons.download, color: Colors.white, size: 18),
                  label: const Text(
                    'Export All Data',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.yellow,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),

            // Show current date range
            if (_isCustomDateRange &&
                _customStartDate != null &&
                _customEndDate != null)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.blueLightest.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.blue.withOpacity(0.2)),
                  ),
                  child: Text(
                    'Selected Range: ${DateFormat('dd/MM/yyyy').format(_customStartDate!)} - ${DateFormat('dd/MM/yyyy').format(_customEndDate!)}',
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

            // Loading indicator
            if (_isFilteringData)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.blue),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Filtering data...',
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: 14,
                        color: AppColors.grey,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Select custom date range
  Future<void> _selectCustomDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _customStartDate != null && _customEndDate != null
          ? DateTimeRange(start: _customStartDate!, end: _customEndDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.blue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.dark,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _customStartDate = picked.start;
        _customEndDate = picked.end;
        _isCustomDateRange = true;
        _selectedDatePeriod = 'Custom Range';
      });
      _applyDateFilters();
    }
  }

  // Show export dialog with format options
  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        elevation: 2,
        shadowColor: AppColors.blue.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'Export Data',
          style: TextStyle(
            fontFamily: 'Space',
            fontWeight: FontWeight.w700,
            fontSize: 20,
            color: AppColors.dark,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select export format and data type:',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.dark.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 16),

            // Export format buttons
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                _buildExportFormatButton('CSV', Icons.table_chart, () {
                  Navigator.pop(context);
                  _exportAllData('csv');
                }),
                _buildExportFormatButton('PDF', Icons.picture_as_pdf, () {
                  Navigator.pop(context);
                  _exportAllData('pdf');
                }),
                _buildExportFormatButton('Excel', Icons.grid_on, () {
                  Navigator.pop(context);
                  _exportAllData('excel');
                }),
              ],
            ),

            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.blueLightest.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.blue.withOpacity(0.2)),
              ),
              child: Text(
                'Export will include all filtered data: financial metrics, commission details, transaction history, and analytics.',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.blue,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: AppColors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build export format button
  Widget _buildExportFormatButton(
      String format, IconData icon, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white, size: 18),
      label: Text(
        format,
        style: const TextStyle(
          fontFamily: 'Space',
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.blue,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 0,
      ),
    );
  }

  // Export all data with date filtering
  void _exportAllData(String format) {
    try {
      final dateRange = _getDateRange();
      final startDate = dateRange['start'];
      final endDate = dateRange['end'];

      // Get filtered data
      final filteredOrders = _getFilteredOrders();
      final filteredWithdrawals =
          _getFilteredData(_withdrawalRequests, 'requestedAt');
      final filteredHistory = _getFilteredData(_paymentHistory, 'date');
      final filteredCommissions = _getFilteredData(_commissionHistory, 'date');

      // Generate filename with date range
      String dateRangeStr = '';
      if (startDate != null && endDate != null) {
        dateRangeStr =
            '_${DateFormat('ddMMyyyy').format(startDate)}_to_${DateFormat('ddMMyyyy').format(endDate)}';
      } else if (_selectedDatePeriod != 'Lifetime') {
        dateRangeStr = '_${_selectedDatePeriod.replaceAll(' ', '_')}';
      }

      final timestamp = DateTime.now().toIso8601String().split('T')[0];

      switch (format.toLowerCase()) {
        case 'csv':
          _exportToCSV(filteredOrders, filteredWithdrawals, filteredHistory,
              filteredCommissions, dateRangeStr, timestamp);
          break;
        case 'pdf':
          _exportToPDF(filteredOrders, filteredWithdrawals, filteredHistory,
              filteredCommissions, dateRangeStr, timestamp);
          break;
        case 'excel':
          _exportToExcel(filteredOrders, filteredWithdrawals, filteredHistory,
              filteredCommissions, dateRangeStr, timestamp);
          break;
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('Data exported successfully as ${format.toUpperCase()}'),
          backgroundColor: AppColors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error exporting data: $e'),
          backgroundColor: AppColors.red,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  // Export to CSV format
  void _exportToCSV(
    List<OrderModel> orders,
    List<Map<String, dynamic>> withdrawals,
    List<Map<String, dynamic>> history,
    List<Map<String, dynamic>> commissions,
    String dateRangeStr,
    String timestamp,
  ) {
    final List<List<dynamic>> allData = [
      // Header section
      ['Payment & Withdrawal Report'],
      [
        'Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}'
      ],
      ['Date Range: $_selectedDatePeriod'],
      if (_isCustomDateRange &&
          _customStartDate != null &&
          _customEndDate != null)
        [
          'Custom Range: ${DateFormat('dd/MM/yyyy').format(_customStartDate!)} - ${DateFormat('dd/MM/yyyy').format(_customEndDate!)}'
        ],
      [''],

      // Financial Summary
      ['FINANCIAL SUMMARY'],
      ['Metric', 'Current Value', 'Previous Value', 'Change (%)'],
      [
        'Month-to-Date Revenue',
        _currentMonthRevenue,
        _previousMonthRevenue,
        _formatPercentageChange(_currentMonthRevenue, _previousMonthRevenue)
      ],
      [
        'Quarter-to-Date Revenue',
        _currentQuarterRevenue,
        _previousQuarterRevenue,
        _formatPercentageChange(_currentQuarterRevenue, _previousQuarterRevenue)
      ],
      [
        'Year-to-Date Revenue',
        _currentYearRevenue,
        _previousYearRevenue,
        _formatPercentageChange(_currentYearRevenue, _previousYearRevenue)
      ],
      ['Average Order Value', _averageOrderValue, '', ''],
      ['Commission Balance', _commissionBalance, '', ''],
      ['Total Balance', _totalBalance, '', ''],
      [''],

      // Orders data
      ['COMPLETED ORDERS'],
      ['Order ID', 'Website', 'Amount', 'Date'],
      ...orders.map((order) => [
            order.orderId,
            order.websiteUrl,
            order.totalPrice,
            _formatDate(order.completionDate),
          ]),
      [''],

      // Withdrawals data
      ['WITHDRAWAL REQUESTS'],
      ['Withdrawal ID', 'User', 'Amount', 'PayPal Email', 'Status', 'Date'],
      ...withdrawals.map((req) => [
            req['withdrawalId'],
            req['userId'],
            req['amount'],
            req['paypalEmail'],
            req['status'],
            _formatDate(req['requestedAt']),
          ]),
      [''],

      // Commission data
      ['COMMISSION HISTORY'],
      ['Commission ID', 'Order ID', 'Description', 'Amount', 'Status', 'Date'],
      ...commissions.map((item) => [
            item['id'],
            item['orderId'],
            item['description'],
            item['amount'],
            item['status'],
            _formatDate(item['date']),
          ]),
    ];

    String csv = const ListToCsvConverter().convert(allData);
    final bytes = utf8.encode(csv);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download = 'payment_report${dateRangeStr}_$timestamp.csv';
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  // Export to PDF format (simplified - creates CSV-like content)
  void _exportToPDF(
    List<OrderModel> orders,
    List<Map<String, dynamic>> withdrawals,
    List<Map<String, dynamic>> history,
    List<Map<String, dynamic>> commissions,
    String dateRangeStr,
    String timestamp,
  ) {
    // For now, we'll create a text-based PDF content
    // In a real implementation, you'd use a PDF library like pdf package
    final content = '''
Payment & Withdrawal Report
Generated on: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}
Date Range: $_selectedDatePeriod
${_isCustomDateRange && _customStartDate != null && _customEndDate != null ? 'Custom Range: ${DateFormat('dd/MM/yyyy').format(_customStartDate!)} - ${DateFormat('dd/MM/yyyy').format(_customEndDate!)}' : ''}

FINANCIAL SUMMARY
Month-to-Date Revenue: \$${_currentMonthRevenue.toStringAsFixed(2)}
Quarter-to-Date Revenue: \$${_currentQuarterRevenue.toStringAsFixed(2)}
Year-to-Date Revenue: \$${_currentYearRevenue.toStringAsFixed(2)}
Average Order Value: \$${_averageOrderValue.toStringAsFixed(2)}
Commission Balance: \$${_commissionBalance.toStringAsFixed(2)}
Total Balance: \$${_totalBalance.toStringAsFixed(2)}

COMPLETED ORDERS (${orders.length} orders)
${orders.map((order) => '${order.orderId} | ${order.websiteUrl} | \$${order.totalPrice.toStringAsFixed(2)} | ${_formatDate(order.completionDate)}').join('\n')}

WITHDRAWAL REQUESTS (${withdrawals.length} requests)
${withdrawals.map((req) => '${req['withdrawalId']} | ${req['userId']} | \$${req['amount'].toStringAsFixed(2)} | ${req['status']} | ${_formatDate(req['requestedAt'])}').join('\n')}

COMMISSION HISTORY (${commissions.length} entries)
${commissions.map((item) => '${item['id']} | ${item['orderId']} | \$${item['amount'].toStringAsFixed(2)} | ${item['status']} | ${_formatDate(item['date'])}').join('\n')}
''';

    final bytes = utf8.encode(content);
    final blob = html.Blob([bytes], 'text/plain');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download = 'payment_report${dateRangeStr}_$timestamp.txt';
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  // Export to Excel format (creates CSV with .xlsx extension)
  void _exportToExcel(
    List<OrderModel> orders,
    List<Map<String, dynamic>> withdrawals,
    List<Map<String, dynamic>> history,
    List<Map<String, dynamic>> commissions,
    String dateRangeStr,
    String timestamp,
  ) {
    // For simplicity, we'll create a CSV file with Excel-compatible format
    _exportToCSV(
        orders, withdrawals, history, commissions, dateRangeStr, timestamp);

    // Note: For true Excel export, you'd use packages like excel or syncfusion_flutter_xlsio
  }

  Widget _buildSummaryCards(double screenWidth) {
    // Use filtered data for summary calculations
    final filteredOrders = _getFilteredOrders();
    final filteredWithdrawals =
        _getFilteredData(_withdrawalRequests, 'requestedAt');

    final totalEarnings = filteredOrders.fold<double>(
        0, (total, order) => total + order.totalPrice);
    final pendingWithdrawals = filteredWithdrawals.fold<double>(
        0, (total, req) => total + req['amount']);
    final crossAxisCount = screenWidth < 600
        ? 1
        : screenWidth < 900
            ? 2
            : 3;
    final cardWidth = screenWidth < 600
        ? screenWidth * 0.9
        : screenWidth / crossAxisCount - 32;

    return Column(
      children: [
        GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          crossAxisSpacing: 24,
          mainAxisSpacing: 24,
          childAspectRatio: 2.5,
          children: [
            _buildSummaryCard(
              title: 'Main Balance',
              value: '\$${_mainBalance.toStringAsFixed(2)}',
              color: AppColors.blue,
              icon: Icons.account_balance_wallet,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  AppColors.blue.withOpacity(0.1),
                  AppColors.blue.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            _buildSummaryCard(
              title: 'Commission Balance',
              value: '\$${_commissionBalance.toStringAsFixed(2)}',
              color: AppColors.green,
              icon: Icons.monetization_on,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  AppColors.green.withOpacity(0.1),
                  AppColors.green.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            _buildSummaryCard(
              title: 'Total Balance',
              value: '\$${_totalBalance.toStringAsFixed(2)}',
              color: Colors.purple,
              icon: Icons.account_balance,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  Colors.purple.withOpacity(0.1),
                  Colors.purple.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              subtitle: 'Main + Commission',
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: crossAxisCount,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          crossAxisSpacing: 24,
          mainAxisSpacing: 24,
          childAspectRatio: 2.5,
          children: [
            _buildSummaryCard(
              title: 'Total Publishers Earnings',
              value: '\$${totalEarnings.toStringAsFixed(2)}',
              color: AppColors.yellow,
              icon: Icons.people,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  AppColors.yellow.withOpacity(0.1),
                  AppColors.yellow.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            _buildSummaryCard(
              title: 'Total Commissions',
              value: '\$${_totalCommissions.toStringAsFixed(2)}',
              color: AppColors.blue,
              icon: Icons.attach_money,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  AppColors.blue.withOpacity(0.1),
                  AppColors.blue.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            _buildSummaryCard(
              title: 'Total Requested Withdrawals',
              value: '\$${pendingWithdrawals.toStringAsFixed(2)}',
              color: AppColors.red,
              icon: Icons.hourglass_empty,
              cardWidth: cardWidth,
              gradient: LinearGradient(
                colors: [
                  AppColors.red.withOpacity(0.1),
                  AppColors.red.withOpacity(0.3)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
    required LinearGradient gradient,
    String? subtitle,
  }) {
    final isSmallScreen = cardWidth < 200;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.w700,
                        color: color,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: isSmallScreen ? 12 : 14,
                          color: AppColors.grey,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              CircleAvatar(
                backgroundColor: color.withOpacity(0.2),
                radius: isSmallScreen ? 20 : 24,
                child: Icon(
                  icon,
                  color: color,
                  size: isSmallScreen ? 22 : 26,
                ),
              ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 28 : 28,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEarningsTable(bool isSmallScreen, bool isWideScreen) {
    final filteredOrders = _getFilteredOrders();
    final totalEarningsPages = filteredOrders.isEmpty
        ? 1
        : (filteredOrders.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentEarningsPage > totalEarningsPages) {
      _currentEarningsPage = 1;
    }

    final startIndex = (_currentEarningsPage - 1) * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, filteredOrders.length);
    final pagedEarnings = filteredOrders.isEmpty
        ? <OrderModel>[]
        : filteredOrders.sublist(startIndex, endIndex);

    return Card(
      color: Colors.white,
      elevation: 2,
      shadowColor: AppColors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Publisher Earnings',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w800,
                    color: AppColors.dark,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _exportToCsv('earnings'),
                  icon:
                      const Icon(Icons.download, color: Colors.white, size: 18),
                  label: const Text(
                    'Export CSV',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: isWideScreen
                    ? 48
                    : isSmallScreen
                        ? 20
                        : 32,
                headingRowColor: MaterialStateProperty.all(
                  AppColors.blueLightest.withOpacity(0.7),
                ),
                dataRowColor: MaterialStateProperty.resolveWith((states) {
                  final index = pagedEarnings.isEmpty
                      ? 0
                      : pagedEarnings.indexOf(pagedEarnings.firstWhere(
                          (item) => states.contains(MaterialState.selected),
                          orElse: () => pagedEarnings.first,
                        ));
                  return _getRowBackgroundColor(index, states);
                }),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blueLightest),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.blue.withOpacity(0.03),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'Order ID',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Website',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amount',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Date',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                ],
                rows: pagedEarnings.isEmpty
                    ? [
                        DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: isWideScreen
                                    ? 600
                                    : isSmallScreen
                                        ? 300
                                        : 400,
                                child: const Text(
                                  'No earnings found',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            ),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                          ],
                        ),
                      ]
                    : pagedEarnings.map((order) {
                        return DataRow(
                          cells: [
                            DataCell(
                              Text(
                                order.orderId ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                order.websiteUrl,
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                '\$${order.totalPrice.toStringAsFixed(2)}',
                                style:
                                    _cellStyle(isSmallScreen, isNumeric: true),
                              ),
                            ),
                            DataCell(
                              Text(
                                _formatDate(order.completionDate),
                                style: _cellStyle(isSmallScreen, isDate: true),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
              ),
            ),
            if (totalEarningsPages > 1)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildActionButton(
                      icon: Icons.chevron_left,
                      onPressed: _currentEarningsPage > 1
                          ? () => setState(() => _currentEarningsPage--)
                          : () {},
                      color: _currentEarningsPage > 1
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Previous page',
                      isSmallScreen: isSmallScreen,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Page $_currentEarningsPage of $totalEarningsPages',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 16,
                          color: AppColors.dark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    _buildActionButton(
                      icon: Icons.chevron_right,
                      onPressed: _currentEarningsPage < totalEarningsPages
                          ? () => setState(() => _currentEarningsPage++)
                          : () {},
                      color: _currentEarningsPage < totalEarningsPages
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Next page',
                      isSmallScreen: isSmallScreen,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalRequestsTable(bool isSmallScreen, bool isWideScreen) {
    final filteredWithdrawals =
        _getFilteredData(_withdrawalRequests, 'requestedAt');
    final totalWithdrawalsPages = filteredWithdrawals.isEmpty
        ? 1
        : (filteredWithdrawals.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentWithdrawalsPage > totalWithdrawalsPages) {
      _currentWithdrawalsPage = 1;
    }

    final startIndex = (_currentWithdrawalsPage - 1) * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, filteredWithdrawals.length);
    final pagedWithdrawals = filteredWithdrawals.isEmpty
        ? <Map<String, dynamic>>[]
        : filteredWithdrawals.sublist(startIndex, endIndex);

    return Card(
      color: Colors.white,
      elevation: 2,
      shadowColor: AppColors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Withdrawal Requests',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w800,
                    color: AppColors.dark,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _exportToCsv('withdrawals'),
                  icon:
                      const Icon(Icons.download, color: Colors.white, size: 18),
                  label: const Text(
                    'Export CSV',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: isWideScreen
                    ? 48
                    : isSmallScreen
                        ? 20
                        : 32,
                headingRowColor: MaterialStateProperty.all(
                  AppColors.blueLightest.withOpacity(0.7),
                ),
                dataRowColor: MaterialStateProperty.resolveWith((states) {
                  final index = pagedWithdrawals.isEmpty
                      ? 0
                      : pagedWithdrawals.indexOf(pagedWithdrawals.firstWhere(
                          (item) => states.contains(MaterialState.selected),
                          orElse: () => pagedWithdrawals.first,
                        ));
                  return _getRowBackgroundColor(index, states);
                }),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blueLightest),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.blue.withOpacity(0.03),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'Withdrawal ID',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'User',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amount',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'PayPal Email',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Date',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Status',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Actions',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                ],
                rows: pagedWithdrawals.isEmpty
                    ? [
                        DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: isWideScreen
                                    ? 600
                                    : isSmallScreen
                                        ? 300
                                        : 400,
                                child: const Text(
                                  'No withdrawal requests found',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            ),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                          ],
                        ),
                      ]
                    : pagedWithdrawals.map((req) {
                        final status = req['status'] ?? 'Pending';
                        return DataRow(
                          cells: [
                            DataCell(
                              Text(
                                req['withdrawalId'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                req['userId'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                req['amount'] != null
                                    ? '\$${req['amount'].toStringAsFixed(2)}'
                                    : 'N/A',
                                style:
                                    _cellStyle(isSmallScreen, isNumeric: true),
                              ),
                            ),
                            DataCell(
                              Text(
                                req['paypalEmail'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                _formatDate(req['requestedAt']),
                                style: _cellStyle(isSmallScreen, isDate: true),
                              ),
                            ),
                            DataCell(
                              _buildStatusBadge(
                                  status.isNotEmpty
                                      ? status[0].toUpperCase() +
                                          status.substring(1)
                                      : 'N/A',
                                  isSmallScreen),
                            ),
                            DataCell(
                              status == 'Pending'
                                  ? Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ElevatedButton(
                                          onPressed: () => _processWithdrawal(
                                              req['withdrawalId'], true),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: AppColors.green,
                                            foregroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 8,
                                            ),
                                            elevation: 2,
                                          ),
                                          child: const Text(
                                            'Approve',
                                            style: TextStyle(
                                              fontFamily: 'Space',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        ElevatedButton(
                                          onPressed: () async {
                                            final reasonController =
                                                TextEditingController();
                                            final confirm =
                                                await showDialog<bool>(
                                              context: context,
                                              builder: (context) => AlertDialog(
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                ),
                                                title: const Text(
                                                  'Reject Withdrawal',
                                                  style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontWeight: FontWeight.w700,
                                                  ),
                                                ),
                                                content: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    const Text(
                                                      'Provide a reason for rejection:',
                                                      style: TextStyle(
                                                        fontFamily: 'Space',
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 12),
                                                    TextField(
                                                      controller:
                                                          reasonController,
                                                      decoration:
                                                          InputDecoration(
                                                        labelText: 'Reason',
                                                        border:
                                                            OutlineInputBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                        ),
                                                      ),
                                                      style: const TextStyle(
                                                        fontFamily: 'Space',
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () =>
                                                        Navigator.pop(
                                                            context, false),
                                                    child: const Text(
                                                      'Cancel',
                                                      style: TextStyle(
                                                        fontFamily: 'Space',
                                                        fontSize: 14,
                                                        color: AppColors.grey,
                                                      ),
                                                    ),
                                                  ),
                                                  TextButton(
                                                    onPressed: () =>
                                                        Navigator.pop(
                                                            context, true),
                                                    child: const Text(
                                                      'Reject',
                                                      style: TextStyle(
                                                        fontFamily: 'Space',
                                                        fontSize: 14,
                                                        color: AppColors.red,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                            if (confirm == true) {
                                              _processWithdrawal(
                                                  req['withdrawalId'], false,
                                                  reason: reasonController
                                                          .text.isNotEmpty
                                                      ? reasonController.text
                                                      : null);
                                            }
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: AppColors.red,
                                            foregroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 8,
                                            ),
                                            elevation: 2,
                                          ),
                                          child: const Text(
                                            'Reject',
                                            style: TextStyle(
                                              fontFamily: 'Space',
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    )
                                  : const Text(
                                      '-',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: 14,
                                        color: Colors.grey,
                                      ),
                                    ),
                            ),
                          ],
                        );
                      }).toList(),
              ),
            ),
            if (totalWithdrawalsPages > 1)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildActionButton(
                      icon: Icons.chevron_left,
                      onPressed: _currentWithdrawalsPage > 1
                          ? () => setState(() => _currentWithdrawalsPage--)
                          : () {},
                      color: _currentWithdrawalsPage > 1
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Previous page',
                      isSmallScreen: isSmallScreen,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Page $_currentWithdrawalsPage of $totalWithdrawalsPages',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 16,
                          color: AppColors.dark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    _buildActionButton(
                      icon: Icons.chevron_right,
                      onPressed: _currentWithdrawalsPage < totalWithdrawalsPages
                          ? () => setState(() => _currentWithdrawalsPage++)
                          : () {},
                      color: _currentWithdrawalsPage < totalWithdrawalsPages
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Next page',
                      isSmallScreen: isSmallScreen,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentHistoryTable(List<Map<String, dynamic>> history,
      bool isSmallScreen, bool isWideScreen) {
    final totalPages = history.isEmpty ? 1 : (history.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > totalPages) {
      _currentPage = 1;
    }

    return Card(
      color: Colors.white,
      elevation: 2,
      shadowColor: AppColors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Payment History',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w800,
                    color: AppColors.dark,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _exportToCsv('history'),
                  icon:
                      const Icon(Icons.download, color: Colors.white, size: 18),
                  label: const Text(
                    'Export CSV',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: isWideScreen
                    ? 48
                    : isSmallScreen
                        ? 20
                        : 32,
                headingRowColor: MaterialStateProperty.all(
                  AppColors.blueLightest.withOpacity(0.7),
                ),
                dataRowColor: MaterialStateProperty.resolveWith((states) {
                  final index = history.isEmpty
                      ? 0
                      : history.indexOf(history.firstWhere(
                          (item) => states.contains(MaterialState.selected),
                          orElse: () => history.first,
                        ));
                  return _getRowBackgroundColor(index, states);
                }),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blueLightest),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.blue.withOpacity(0.03),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'ID',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Description',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Amount',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Status',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Type',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'Date',
                      style: _headerStyle(isSmallScreen),
                    ),
                  ),
                ],
                rows: history.isEmpty
                    ? [
                        DataRow(
                          cells: [
                            DataCell(
                              SizedBox(
                                width: isWideScreen
                                    ? 600
                                    : isSmallScreen
                                        ? 300
                                        : 400,
                                child: const Text(
                                  'No payment history found',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                            ),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                            const DataCell(Text('')),
                          ],
                        ),
                      ]
                    : history.map((item) {
                        return DataRow(
                          cells: [
                            DataCell(
                              Text(
                                item['id'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                item['description'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                item['amount'] != null
                                    ? '\$${item['amount'].toStringAsFixed(2)}'
                                    : 'N/A',
                                style:
                                    _cellStyle(isSmallScreen, isNumeric: true),
                              ),
                            ),
                            DataCell(
                              _buildStatusBadge(
                                  item['status'] ?? 'N/A', isSmallScreen),
                            ),
                            DataCell(
                              Text(
                                item['type'] ?? 'N/A',
                                style: _cellStyle(isSmallScreen),
                              ),
                            ),
                            DataCell(
                              Text(
                                _formatDate(item['date']),
                                style: _cellStyle(isSmallScreen, isDate: true),
                              ),
                            ),
                          ],
                        );
                      }).toList(),
              ),
            ),
            if (totalPages > 1)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildActionButton(
                      icon: Icons.chevron_left,
                      onPressed: _currentPage > 1
                          ? () => setState(() => _currentPage--)
                          : () {},
                      color: _currentPage > 1 ? AppColors.blue : AppColors.grey,
                      tooltip: 'Previous page',
                      isSmallScreen: isSmallScreen,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Page $_currentPage of $totalPages',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 16,
                          color: AppColors.dark,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    _buildActionButton(
                      icon: Icons.chevron_right,
                      onPressed: _currentPage < totalPages
                          ? () => setState(() => _currentPage++)
                          : () {},
                      color: _currentPage < totalPages
                          ? AppColors.blue
                          : AppColors.grey,
                      tooltip: 'Next page',
                      isSmallScreen: isSmallScreen,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        elevation: 2,
        shadowColor: AppColors.blue.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontFamily: 'Space',
            fontWeight: FontWeight.w700,
            fontSize: 20,
            color: AppColors.dark,
          ),
        ),
        content: SizedBox(
          width: 400,
          height: 300,
          child: _notifications.isEmpty
              ? const Center(
                  child: Text(
                    'No notifications',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                )
              : ListView.builder(
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) {
                    final notification = _notifications[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 1,
                      shadowColor: AppColors.blue.withOpacity(0.1),
                      color: notification['read']
                          ? Colors.white
                          : AppColors.blueLightest.withOpacity(0.5),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        title: Text(
                          notification['message'] ?? 'N/A',
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          _formatDate(notification['timestamp']),
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        trailing: notification['read']
                            ? null
                            : Container(
                                decoration: BoxDecoration(
                                  color: AppColors.blue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.mark_chat_read,
                                    color: AppColors.blue,
                                    size: 20,
                                  ),
                                  onPressed: () =>
                                      _markNotificationRead(notification['id']),
                                  tooltip: 'Mark as read',
                                  style: ButtonStyle(
                                    overlayColor: MaterialStateProperty
                                        .resolveWith<Color>(
                                      (Set<MaterialState> states) {
                                        if (states
                                            .contains(MaterialState.hovered)) {
                                          return AppColors.blue
                                              .withOpacity(0.2);
                                        }
                                        return Colors.transparent;
                                      },
                                    ),
                                  ),
                                ),
                              ),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Close',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 14 : 16,
      fontWeight: FontWeight.w700,
      color: AppColors.dark,
      letterSpacing: 0.3,
    );
  }

  TextStyle _cellStyle(bool isSmallScreen,
      {bool isNumeric = false, bool isDate = false}) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 13 : 14,
      color: AppColors.dark.withOpacity(0.8),
      fontWeight: isNumeric ? FontWeight.w600 : FontWeight.normal,
      fontStyle: isDate ? FontStyle.italic : FontStyle.normal,
      letterSpacing: 0.2,
    );
  }

  // Get background color for alternating rows
  Color _getRowBackgroundColor(int index, Set<MaterialState> states) {
    // If the row is hovered, return a light blue color
    if (states.contains(MaterialState.hovered)) {
      return AppColors.blueLightest;
    }

    // For alternating row colors
    if (index % 2 == 0) {
      return Colors.white;
    } else {
      return AppColors.componentBackColor;
    }
  }

  // Get status badge styling
  Widget _buildStatusBadge(String status, bool isSmallScreen) {
    final Color statusColor = _getStatusColor(status);
    final Color backgroundColor = statusColor.withOpacity(0.1);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.2), width: 1),
      ),
      child: Text(
        status,
        style: TextStyle(
          fontFamily: 'Space',
          fontSize: isSmallScreen ? 12 : 13,
          fontWeight: FontWeight.w600,
          color: statusColor,
        ),
      ),
    );
  }

  // Get action button styling
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    required String tooltip,
    bool isSmallScreen = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: IconButton(
          icon: Icon(icon, size: isSmallScreen ? 18 : 20),
          onPressed: onPressed,
          color: color,
          padding: const EdgeInsets.all(8),
          constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.resolveWith<Color>(
              (Set<MaterialState> states) {
                if (states.contains(MaterialState.hovered)) {
                  return color.withOpacity(0.2);
                }
                return Colors.transparent;
              },
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }
}
