import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

class ImprovedWebsiteDialog extends StatefulWidget {
  final WebsiteModel website;
  final String publisherName;
  final String publisherEmail;
  final Function(Map<String, dynamic>) onEdit;
  final VoidCallback onDelete;
  final Function(String) onChangeStatus;

  const ImprovedWebsiteDialog({
    super.key,
    required this.website,
    required this.publisherName,
    required this.publisherEmail,
    required this.onEdit,
    required this.onDelete,
    required this.onChangeStatus,
  });

  @override
  State<ImprovedWebsiteDialog> createState() => _ImprovedWebsiteDialogState();
}

class _ImprovedWebsiteDialogState extends State<ImprovedWebsiteDialog> {
  int _selectedTab = 0;
  List<OrderModel> _orders = [];
  bool _isLoadingOrders = true;
  String? _errorMessage;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    super.dispose();
  }

  void _loadOrders() {
    setState(() {
      _isLoadingOrders = true;
      _errorMessage = null;
    });

    try {
      _ordersSubscription = FirebaseFirestore.instance
          .collection('orders')
          .where('websiteId', isEqualTo: widget.website.websiteId)
          .orderBy('orderDate', descending: true)
          .snapshots()
          .listen((snapshot) {
        final orders = snapshot.docs.map((doc) {
          final data = doc.data();
          return OrderModel.fromMap(data..['orderId'] = doc.id);
        }).toList();

        setState(() {
          _orders = orders;
          _isLoadingOrders = false;
        });
      }, onError: (error) {
        print('Error loading orders: $error');
        setState(() {
          _errorMessage = 'Error loading orders: $error';
          _isLoadingOrders = false;
        });
      });
    } catch (e) {
      print('Error loading orders: $e');
      setState(() {
        _errorMessage = 'Error loading orders: $e';
        _isLoadingOrders = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 600,
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Container(
          width: 600,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: const Color.fromRGBO(26, 115, 232, 0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
            ],
            border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                decoration: BoxDecoration(
                  color: _getLightColorForIcon(AppColors.blue),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(20)),
                  border: const Border(
                    bottom: BorderSide(
                      color: Color.fromRGBO(232, 240, 254, 1.0),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.language,
                            color: AppColors.blue,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          widget.website.url,
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppColors.dark,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.close, color: AppColors.grey),
                        onPressed: () => Navigator.pop(context),
                        tooltip: 'Close',
                      ),
                    ),
                  ],
                ),
              ),

              // Tabs
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: const Border(
                    bottom: BorderSide(
                      color: Color.fromRGBO(232, 240, 254, 1.0),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    _buildTab('Details', 0),
                    _buildTab('Orders', 1),
                    _buildTab('Analytics', 2),
                  ],
                ),
              ),

              // Content
              SizedBox(
                height: 500,
                child: _selectedTab == 0
                    ? _buildDetailsTab()
                    : _selectedTab == 1
                        ? _buildOrdersTab()
                        : _buildAnalyticsTab(),
              ),

              // Footer
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: const Border(
                    top: BorderSide(
                      color: Color.fromRGBO(232, 240, 254, 1.0),
                      width: 1,
                    ),
                  ),
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildStatusDropdown(),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: () => _showEditDialog(context, widget.website),
                      icon: const Icon(Icons.edit, size: 18),
                      label: const Text('Edit'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        elevation: 0,
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: () {
                        widget.onDelete();
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.delete, size: 18),
                      label: const Text('Delete'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        elevation: 0,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTab == index;

    return InkWell(
      onTap: () => setState(() => _selectedTab = index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? AppColors.blue : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontFamily: 'Space',
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? AppColors.blue : AppColors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 1.0)),
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
      ),
      child: DropdownButton<String>(
        value: widget.website.status,
        underline: const SizedBox(),
        icon: const Icon(Icons.arrow_drop_down, color: AppColors.blue),
        items: ['Approved', 'Rejected', 'Deleted', 'Pending']
            .map((status) => DropdownMenuItem(
                  value: status,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: status == 'Approved'
                              ? AppColors.green
                              : AppColors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        status,
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontSize: 14,
                          color: status == 'Approved'
                              ? AppColors.green
                              : AppColors.red,
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
        onChanged: (value) {
          if (value != null) {
            widget.onChangeStatus(value);
            Navigator.pop(context);
          }
        },
      ),
    );
  }

  Color _getLightColorForIcon(Color color) {
    if (color == AppColors.blue) {
      return const Color.fromRGBO(232, 240, 254, 1.0);
    } else if (color == AppColors.green) {
      return const Color.fromRGBO(232, 245, 233, 1.0);
    } else if (color == AppColors.yellow) {
      return const Color.fromRGBO(255, 243, 224, 1.0);
    } else if (color == AppColors.red) {
      return const Color.fromRGBO(255, 235, 238, 1.0);
    } else if (color == Colors.orange) {
      return const Color.fromRGBO(255, 236, 217, 1.0);
    } else {
      return const Color.fromRGBO(240, 240, 240, 1.0);
    }
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Website Information', Icons.web),
          _buildDetailRow('Website ID', widget.website.websiteId ?? 'N/A'),
          _buildDetailRow('URL', widget.website.url),
          _buildDetailRow('Domain', widget.website.domainName),
          _buildDetailRow('Language', widget.website.language ?? 'N/A'),
          _buildDetailRow('Country', widget.website.country),
          _buildDetailRow('Status', widget.website.status,
              valueColor: widget.website.status == 'Approved'
                  ? AppColors.green
                  : AppColors.red),
          const SizedBox(height: 24),
          _buildSectionTitle('Publisher Information', Icons.person),
          _buildDetailRow('Publisher', widget.publisherName),
          _buildDetailRow('Publisher Email', widget.publisherEmail),
          _buildDetailRow('Contact Email', widget.website.contactEmail),
          const SizedBox(height: 24),
          _buildSectionTitle('SEO Metrics', Icons.trending_up),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'DA',
                  widget.website.da.toString(),
                  AppColors.blue,
                  Icons.bar_chart,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'DR',
                  widget.website.dr.toString(),
                  AppColors.green,
                  Icons.show_chart,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricCard(
                  'Traffic',
                  widget.website.traffic.toString(),
                  Colors.orange,
                  Icons.people,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSpamScoreRow(),
          const SizedBox(height: 24),
          _buildSectionTitle('Pricing', Icons.attach_money),
          _buildDetailRow('Base Price',
              '\$${widget.website.basePricing.toStringAsFixed(2)}'),
          _buildDetailRow('Special Topics Price',
              '\$${widget.website.specialTopicsAdditionalPrice.toStringAsFixed(2)}'),
          _buildDetailRow(
              'Final Price', '\$${widget.website.pricing.toStringAsFixed(2)}',
              valueColor: AppColors.blue, valueFontWeight: FontWeight.bold),
          _buildDetailRow('Has Special Pricing',
              widget.website.hasSpecialTopicsPricing.toString()),
          const SizedBox(height: 24),
          _buildSectionTitle('Content Guidelines', Icons.description),
          _buildDetailRow('Word Count Range',
              '${widget.website.wordCountMin} - ${widget.website.wordCountMax} words'),
          _buildDetailRow('Max Links', widget.website.maxLinks.toString()),
          _buildDetailRow('Backlink Type', widget.website.backlinkType,
              valueColor:
                  widget.website.backlinkType.toLowerCase() == 'dofollow'
                      ? AppColors.green
                      : AppColors.grey),
          _buildDetailRow(
              'Is Sponsored', widget.website.isSponsored.toString()),
          const SizedBox(height: 24),
          _buildSectionTitle('Topics', Icons.topic),
          _buildTopicsSection('Categories', widget.website.categories),
          _buildTopicsSection('Allowed Topics', widget.website.allowedTopics),
          _buildTopicsSection(
              'Disallowed Topics', widget.website.disallowedTopics),
          _buildTopicsSection('Content Type', widget.website.contentType),
          const SizedBox(height: 24),
          _buildSectionTitle('Additional Information', Icons.info),
          _buildDetailRow(
              'Notes',
              widget.website.notes.isEmpty
                  ? 'No notes available'
                  : widget.website.notes),
          _buildDetailRow('Created At', _formatDate(widget.website.createdAt)),
          _buildDetailRow(
              'Last Updated', _formatDate(widget.website.lastUpdated)),
          _buildDetailRow(
              'Submission Date', _formatDate(widget.website.submissionDate)),
          if (widget.website.rejectionReason != null)
            _buildDetailRow('Rejection Reason', widget.website.rejectionReason!,
                valueColor: AppColors.red),
        ],
      ),
    );
  }

  Widget _buildOrdersTab() {
    if (_isLoadingOrders) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: AppColors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadOrders,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (_orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _getLightColorForIcon(AppColors.blue),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.shopping_cart,
                color: AppColors.blue,
                size: 48,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'No orders found for this website',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _loadOrders,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Orders (${_orders.length})',
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _loadOrders,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: _orders.length,
              itemBuilder: (context, index) {
                final order = _orders[index];
                return _buildOrderCard(order);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    final statusColor = _getStatusColor(order.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shadowColor: AppColors.blue.withAlpha(25),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getLightColorForIcon(AppColors.blue),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.description,
                        color: AppColors.blue,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order #${order.orderId?.substring(0, 8) ?? 'N/A'}',
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.dark,
                          ),
                        ),
                        Text(
                          _formatDate(order.orderDate),
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 12,
                            color: AppColors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: statusColor.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    order.status,
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            _buildOrderDetailRow('Post Title', order.postTitle),
            _buildOrderDetailRow('Word Count', order.wordCount.toString()),
            _buildOrderDetailRow('Links', order.links.length.toString()),
            _buildOrderDetailRow('Backlink Type', order.backlinkType),
            _buildOrderDetailRow('Payment Status', order.paymentStatus),
            _buildOrderDetailRow(
              'Price',
              '\$${order.totalPrice.toStringAsFixed(2)}',
              valueColor: AppColors.blue,
              valueFontWeight: FontWeight.bold,
            ),
            if (order.completionDate != null)
              _buildOrderDetailRow(
                'Completed On',
                _formatDate(order.completionDate!),
              ),
            if (order.rejectionReason != null)
              _buildOrderDetailRow(
                'Rejection Reason',
                order.rejectionReason!,
                valueColor: AppColors.red,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetailRow(String label, String value,
      {Color? valueColor, FontWeight? valueFontWeight}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: valueColor ?? AppColors.dark,
                fontWeight: valueFontWeight ?? FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppColors.green;
      case 'pending':
      case 'in progress':
        return AppColors.yellow;
      case 'rejected':
      case 'cancelled':
        return AppColors.red;
      default:
        return AppColors.grey;
    }
  }

  Widget _buildAnalyticsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildAnalyticsCard(
                'Views',
                widget.website.viewsCount.toString(),
                Icons.visibility,
                AppColors.blue,
              ),
              const SizedBox(width: 20),
              _buildAnalyticsCard(
                'Clicks',
                widget.website.clicksCount.toString(),
                Icons.touch_app,
                Colors.orange,
              ),
            ],
          ),
          const SizedBox(height: 40),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _getLightColorForIcon(AppColors.grey),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.bar_chart,
                  color: AppColors.grey,
                  size: 48,
                ),
                SizedBox(height: 12),
                Text(
                  'Detailed analytics coming soon',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 16,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getLightColorForIcon(AppColors.blue),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.blue,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value,
      {Color? valueColor, FontWeight? valueFontWeight}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 150,
            padding: const EdgeInsets.only(right: 12),
            decoration: const BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Color.fromRGBO(232, 240, 254, 1.0),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: valueColor ?? AppColors.dark,
                fontWeight: valueFontWeight ?? FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
      String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(25),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withAlpha(50)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getLightColorForIcon(color),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 14,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      width: 160,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: color.withAlpha(50)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getLightColorForIcon(color),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Space',
              fontSize: 16,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpamScoreRow() {
    // Get the spam score value or use null if not available
    final spamScore = widget.website.spamScore;

    // Determine color based on spam score
    Color scoreColor;
    if (spamScore == null) {
      scoreColor = AppColors.grey;
    } else if (spamScore <= 30.0) {
      scoreColor = Colors.green; // Low spam score (good)
    } else if (spamScore <= 70.0) {
      scoreColor = Colors.orange; // Medium spam score (caution)
    } else {
      scoreColor = Colors.red; // High spam score (bad)
    }

    // Format the spam score as a percentage with one decimal place
    final formattedScore = spamScore != null
        ? '${spamScore.toStringAsFixed(1)}%'
        : 'Not available';

    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 150,
            padding: const EdgeInsets.only(right: 12),
            decoration: const BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Color.fromRGBO(232, 240, 254, 1.0),
                  width: 1,
                ),
              ),
            ),
            child: const Text(
              'Spam Score',
              style: TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Row(
              children: [
                Icon(
                  spamScore == null
                      ? Icons.info_outline
                      : (spamScore <= 30.0
                          ? Icons.check_circle
                          : spamScore <= 70.0
                              ? Icons.warning
                              : Icons.error),
                  color: scoreColor,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  formattedScore,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 14,
                    color: scoreColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicsSection(String title, List<String> topics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontFamily: 'Space',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: topics.isEmpty
              ? [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getLightColorForIcon(AppColors.grey),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      'None specified',
                      style: TextStyle(
                        fontFamily: 'Space',
                        fontSize: 12,
                        color: AppColors.grey,
                      ),
                    ),
                  ),
                ]
              : topics
                  .map((topic) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _getLightColorForIcon(AppColors.blue),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          topic,
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 12,
                            color: AppColors.blue,
                          ),
                        ),
                      ))
                  .toList(),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }

  void _showEditDialog(BuildContext context, WebsiteModel website) {
    final daController = TextEditingController(text: website.da.toString());
    final drController = TextEditingController(text: website.dr.toString());
    final trafficController =
        TextEditingController(text: website.traffic.toString());
    final pricingController =
        TextEditingController(text: website.pricing.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit Website - ${website.url}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: daController,
                decoration:
                    const InputDecoration(labelText: 'Domain Authority'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: drController,
                decoration: const InputDecoration(labelText: 'Domain Rating'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: trafficController,
                decoration: const InputDecoration(labelText: 'Monthly Traffic'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: pricingController,
                decoration: const InputDecoration(labelText: 'Pricing (USD)'),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final updates = <String, dynamic>{};

              final da = int.tryParse(daController.text);
              if (da != null) updates['da'] = da;

              final dr = int.tryParse(drController.text);
              if (dr != null) updates['dr'] = dr;

              final traffic = int.tryParse(trafficController.text);
              if (traffic != null) updates['traffic'] = traffic;

              final pricing = double.tryParse(pricingController.text);
              if (pricing != null) updates['pricing'] = pricing;

              if (updates.isNotEmpty) {
                widget.onEdit(updates);
                Navigator.pop(context);
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
