import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

class ImprovedUserDetailsDialog extends StatefulWidget {
  final String uid;
  final String name;
  final String email;
  final bool emailVerified;
  final String mobileNumber;
  final String profilePictureUrl;
  final bool isPublisher;
  final Timestamp? createdAt;
  final Timestamp? lastLogin;
  final Map<String, dynamic> additionalDetails;
  final bool isBlocked;
  final bool isAdmin;
  final List<String> assignedOrderIds;
  final double mainBalance;
  final double reservedBalance;
  final double buyerFunds;
  final double reservedFunds;

  const ImprovedUserDetailsDialog({
    super.key,
    required this.uid,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.mobileNumber,
    required this.profilePictureUrl,
    required this.isPublisher,
    this.createdAt,
    this.lastLogin,
    required this.additionalDetails,
    required this.isBlocked,
    required this.isAdmin,
    required this.assignedOrderIds,
    required this.mainBalance,
    required this.reservedBalance,
    required this.buyerFunds,
    required this.reservedFunds,
  });

  @override
  State<ImprovedUserDetailsDialog> createState() =>
      _ImprovedUserDetailsDialogState();
}

class _ImprovedUserDetailsDialogState extends State<ImprovedUserDetailsDialog>
    with SingleTickerProviderStateMixin {
  int _orderCount = 0;
  int _websiteCount = 0;
  bool _isLoading = true;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fetchCounts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchCounts() async {
    setState(() => _isLoading = true);
    try {
      // Fetch order count (use assignedOrderIds if publisher, else query orders)
      if (widget.isPublisher) {
        _orderCount = widget.assignedOrderIds.length;
      } else {
        final orderSnapshot = await FirebaseFirestore.instance
            .collection('orders')
            .where('buyerId', isEqualTo: widget.uid)
            .get();
        _orderCount = orderSnapshot.docs.length;
      }

      // Fetch website count
      final websiteSnapshot = await FirebaseFirestore.instance
          .collection('websites')
          .where('publisherId', isEqualTo: widget.uid)
          .get();
      _websiteCount = websiteSnapshot.docs.length;

      if (mounted) {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    return DateFormat('MMM dd, yyyy hh:mm a').format(timestamp.toDate());
  }

  String _formatCurrency(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        constraints: BoxConstraints(
          maxWidth: 800,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.blue.withAlpha(50),
                    AppColors.blue.withAlpha(12),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.blue.withAlpha(30),
                        backgroundImage: widget.profilePictureUrl.isNotEmpty
                            ? NetworkImage(widget.profilePictureUrl)
                            : null,
                        child: widget.profilePictureUrl.isEmpty
                            ? Text(
                                widget.name.isNotEmpty
                                    ? widget.name[0].toUpperCase()
                                    : '?',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.blue,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.name,
                              style: const TextStyle(
                                fontFamily: 'Space',
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppColors.dark,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.email,
                              style: const TextStyle(
                                fontFamily: 'Space',
                                fontSize: 16,
                                color: AppColors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: widget.isBlocked
                              ? Colors.red.withAlpha(25)
                              : Colors.green.withAlpha(25),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          widget.isBlocked ? 'Blocked' : 'Active',
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: widget.isBlocked ? Colors.red : Colors.green,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // User role badges
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildRoleBadge(
                        widget.isAdmin ? 'Admin' : 'User',
                        widget.isAdmin ? Colors.purple : AppColors.blue,
                        Icons.admin_panel_settings,
                      ),
                      const SizedBox(width: 12),
                      _buildRoleBadge(
                        widget.isPublisher ? 'Publisher' : 'Advertiser',
                        widget.isPublisher ? Colors.green : Colors.orange,
                        widget.isPublisher ? Icons.publish : Icons.campaign,
                      ),
                      const SizedBox(width: 12),
                      _buildRoleBadge(
                        widget.emailVerified
                            ? 'Verified Email'
                            : 'Unverified Email',
                        widget.emailVerified ? Colors.green : Colors.red,
                        widget.emailVerified ? Icons.check_circle : Icons.error,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Tabs
            TabBar(
              controller: _tabController,
              labelColor: AppColors.blue,
              unselectedLabelColor: AppColors.grey,
              indicatorColor: AppColors.blue,
              tabs: const [
                Tab(text: 'Details'),
                Tab(text: 'Orders'),
                Tab(text: 'Analytics'),
              ],
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildDetailsTab(isSmallScreen),
                  _buildOrdersTab(isSmallScreen),
                  _buildAnalyticsTab(isSmallScreen),
                ],
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(15),
                borderRadius:
                    const BorderRadius.vertical(bottom: Radius.circular(16)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.grey,
                    ),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleBadge(String title, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withAlpha(50), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab(bool isSmallScreen) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User information section
          _buildSectionHeader('User Information', Icons.person),
          const SizedBox(height: 16),
          _buildDetailRow('User ID', widget.uid),
          _buildDetailRow('Mobile Number',
              widget.mobileNumber.isEmpty ? 'N/A' : widget.mobileNumber),
          _buildDetailRow('Created At', _formatDate(widget.createdAt)),
          _buildDetailRow('Last Login', _formatDate(widget.lastLogin)),
          _buildDetailRow('Provider',
              widget.additionalDetails['provider']?.toString() ?? 'N/A'),

          const SizedBox(height: 24),

          // Financial information section
          _buildSectionHeader(
              'Financial Information', Icons.account_balance_wallet),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                  child: _buildMetricCard(
                      'Main Balance',
                      _formatCurrency(widget.mainBalance),
                      Icons.account_balance,
                      AppColors.blue)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildMetricCard(
                      'Reserved Balance',
                      _formatCurrency(widget.reservedBalance),
                      Icons.lock,
                      Colors.orange)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                  child: _buildMetricCard(
                      'Buyer Funds',
                      _formatCurrency(widget.buyerFunds),
                      Icons.shopping_cart,
                      Colors.green)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildMetricCard(
                      'Reserved Funds',
                      _formatCurrency(widget.reservedFunds),
                      Icons.lock_clock,
                      Colors.purple)),
            ],
          ),

          const SizedBox(height: 24),

          // Additional details section
          if (widget.additionalDetails.isNotEmpty) ...[
            _buildSectionHeader('Additional Details', Icons.info),
            const SizedBox(height: 16),
            ...widget.additionalDetails.entries
                .where((entry) => entry.key != 'provider')
                .map((entry) => _buildDetailRow(
                      entry.key.replaceFirst(
                          entry.key[0], entry.key[0].toUpperCase()),
                      entry.value.toString(),
                    )),
          ],
        ],
      ),
    );
  }

  Widget _buildOrdersTab(bool isSmallScreen) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMetricCard(
                  'Total Orders',
                  _orderCount.toString(),
                  Icons.shopping_bag,
                  AppColors.blue,
                  isLarge: true,
                ),
                const SizedBox(height: 20),
                if (widget.assignedOrderIds.isNotEmpty) ...[
                  _buildSectionHeader('Assigned Orders', Icons.assignment),
                  const SizedBox(height: 16),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.assignedOrderIds.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                          side: BorderSide(color: Colors.grey.withAlpha(50)),
                        ),
                        child: ListTile(
                          leading:
                              const Icon(Icons.receipt, color: AppColors.blue),
                          title:
                              Text('Order #${widget.assignedOrderIds[index]}'),
                          trailing:
                              const Icon(Icons.arrow_forward_ios, size: 16),
                        ),
                      );
                    },
                  ),
                ] else ...[
                  Center(
                    child: Column(
                      children: [
                        Icon(Icons.receipt_long,
                            size: 64, color: Colors.grey.withAlpha(100)),
                        const SizedBox(height: 16),
                        const Text(
                          'No orders found',
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 16,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          );
  }

  Widget _buildAnalyticsTab(bool isSmallScreen) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Websites',
                        _websiteCount.toString(),
                        Icons.language,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildMetricCard(
                        'Orders',
                        _orderCount.toString(),
                        Icons.shopping_bag,
                        AppColors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                _buildSectionHeader('Activity Summary', Icons.analytics),
                const SizedBox(height: 16),
                Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: BorderSide(color: Colors.grey.withAlpha(50)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildActivityItem('Account Created',
                            _formatDate(widget.createdAt), Icons.person_add),
                        const Divider(),
                        _buildActivityItem('Last Login',
                            _formatDate(widget.lastLogin), Icons.login),
                        if (widget.isPublisher) ...[
                          const Divider(),
                          _buildActivityItem('Websites Published',
                              _websiteCount.toString(), Icons.language),
                        ],
                        const Divider(),
                        _buildActivityItem('Orders Processed',
                            _orderCount.toString(), Icons.shopping_cart),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.blue.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: AppColors.blue,
            size: 18,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontFamily: 'Space',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: AppColors.dark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
      String title, String value, IconData icon, Color color,
      {bool isLarge = false}) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color.withAlpha(50)),
      ),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: isLarge ? 24 : 18,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isLarge ? 18 : 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
            SizedBox(height: isLarge ? 16 : 12),
            Text(
              value,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isLarge ? 32 : 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.blue.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.blue,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'Space',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.dark,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontFamily: 'Space',
                    fontSize: 12,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
