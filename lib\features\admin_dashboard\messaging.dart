import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class MessagingPage extends StatefulWidget {
  const MessagingPage({Key? key}) : super(key: key);

  @override
  _MessagingPageState createState() => _MessagingPageState();
}

class _MessagingPageState extends State<MessagingPage> {
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  String? _selectedUserId;
  String? _selectedUserName;
  bool _isLoading = true;
  bool _isSending = false;
  bool _sendToAll = false;
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _fetchUsers();
    _searchController.addListener(() {
      setState(() {
        _filterUsers();
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchUsers() async {
    setState(() => _isLoading = true);
    try {
      final snapshot =
          await FirebaseFirestore.instance.collection('users').get();
      if (mounted) {
        setState(() {
          _users = snapshot.docs.map((doc) {
            final data = doc.data();
            return {
              'uid': doc.id,
              'name': data['name'] ?? 'N/A',
              'email': data['email'] ?? 'N/A',
            };
          }).toList();
          _filteredUsers = _users;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching users: $e'),
            backgroundColor: AppColors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      _filteredUsers = _users;
    } else {
      _filteredUsers = _users.where((user) {
        final name = user['name'].toString().toLowerCase();
        final email = user['email'].toString().toLowerCase();
        return name.contains(query) || email.contains(query);
      }).toList();
    }
  }

  Future<void> _sendMessage() async {
    if (!_sendToAll && _selectedUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a user'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }
    if (_messageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a message'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isSending = true);
    try {
      final messageData = {
        'message': _messageController.text.trim(),
        'senderId': 'admin', // Replace with actual admin UID
        'timestamp': Timestamp.now(),
        'read': false,
      };

      if (_sendToAll) {
        // Batch write to all users
        final batch = FirebaseFirestore.instance.batch();
        for (var user in _users) {
          final messageRef = FirebaseFirestore.instance
              .collection('users')
              .doc(user['uid'])
              .collection('messages')
              .doc();
          batch.set(messageRef, messageData);
        }
        await batch.commit();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Message sent to all ${_users.length} users'),
              backgroundColor: AppColors.green,
            ),
          );
        }
      } else {
        // Send to single user
        await FirebaseFirestore.instance
            .collection('users')
            .doc(_selectedUserId)
            .collection('messages')
            .add(messageData);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Message sent to ${_selectedUserName ?? "user"}'),
              backgroundColor: AppColors.green,
            ),
          );
        }
      }

      if (mounted) {
        _messageController.clear();
        setState(() {
          _selectedUserId = null;
          _selectedUserName = null;
          _searchController.clear();
          _sendToAll = false;
        });
        _fetchUsers();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending message: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSending = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromRGBO(
                  248, 249, 252, 1.0), // AppColors.componentBackColor
              Colors.white,
            ],
            stops: [0.0, 0.8],
          ),
        ),
        child: SingleChildScrollView(
          child: Center(
            child: Container(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.04),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(20)),
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(26, 115, 232, 0.08),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                          spreadRadius: 2,
                        ),
                      ],
                      gradient: LinearGradient(
                        colors: [
                          Colors.white,
                          Color.fromRGBO(
                              232, 240, 254, 0.3), // AppColors.blueLightest
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Color.fromRGBO(232, 240, 254, 0.5),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.notifications,
                                color: AppColors.blue,
                                size: isSmallScreen ? 24 : 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              'Send Notification',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: isSmallScreen ? 22 : 28,
                                fontWeight: FontWeight.bold,
                                color: AppColors.dark,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Color.fromRGBO(232, 240, 254, 0.5),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Tooltip(
                            message: 'Refresh user list',
                            child: IconButton(
                              icon: Icon(Icons.refresh, color: AppColors.blue),
                              onPressed: _fetchUsers,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Content
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.person,
                                      color: AppColors.blue,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  _buildSectionTitle(
                                      'Recipient', isSmallScreen),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                                decoration: BoxDecoration(
                                  color: Color.fromRGBO(232, 240, 254, 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Color.fromRGBO(232, 240, 254, 0.8),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      height: 24,
                                      width: 24,
                                      child: Checkbox(
                                        value: _sendToAll,
                                        activeColor: AppColors.blue,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        onChanged: (value) {
                                          setState(() {
                                            _sendToAll = value ?? false;
                                            if (_sendToAll) {
                                              _selectedUserId = null;
                                              _selectedUserName = null;
                                              _searchController.clear();
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Send to All Users',
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: 15,
                                        fontWeight: FontWeight.w500,
                                        color: _sendToAll
                                            ? AppColors.blue
                                            : AppColors.dark,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 12),
                              Autocomplete<Map<String, dynamic>>(
                                optionsBuilder: (TextEditingValue value) {
                                  if (value.text.isEmpty || _sendToAll) {
                                    return [];
                                  }
                                  return _filteredUsers.where((user) {
                                    final name =
                                        user['name'].toString().toLowerCase();
                                    final email =
                                        user['email'].toString().toLowerCase();
                                    return name.contains(
                                            value.text.toLowerCase()) ||
                                        email
                                            .contains(value.text.toLowerCase());
                                  });
                                },
                                displayStringForOption: (user) =>
                                    '${user['name']} (${user['email']})',
                                fieldViewBuilder: (context, controller,
                                    focusNode, onFieldSubmitted) {
                                  _searchController.text = controller.text;
                                  return TextField(
                                    controller: controller,
                                    focusNode: focusNode,
                                    enabled: !_sendToAll,
                                    decoration: InputDecoration(
                                      hintText: 'Search by name or email...',
                                      hintStyle: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: 14,
                                        color: Colors.grey[400],
                                      ),
                                      prefixIcon: Icon(Icons.search,
                                          color: AppColors.blue),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Color.fromRGBO(
                                                232, 240, 254, 1.0)),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: BorderSide(
                                            color: Color.fromRGBO(
                                                232, 240, 254, 1.0)),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                        borderSide: const BorderSide(
                                            color: AppColors.blue, width: 2),
                                      ),
                                      filled: true,
                                      fillColor:
                                          Color.fromRGBO(248, 249, 252, 0.5),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 16,
                                      ),
                                    ),
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      color: AppColors.dark,
                                    ),
                                    onSubmitted: (value) => onFieldSubmitted(),
                                  );
                                },
                                onSelected: (user) {
                                  setState(() {
                                    _selectedUserId = user['uid'];
                                    _selectedUserName = user['name'];
                                    _searchController.text =
                                        '${user['name']} (${user['email']})';
                                  });
                                },
                                optionsViewBuilder:
                                    (context, onSelected, options) {
                                  return Align(
                                    alignment: Alignment.topLeft,
                                    child: Material(
                                      elevation: 4,
                                      borderRadius: BorderRadius.circular(12),
                                      child: Container(
                                        constraints: const BoxConstraints(
                                            maxHeight: 300),
                                        width: 400,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Color.fromRGBO(
                                                232, 240, 254, 0.8),
                                          ),
                                        ),
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          itemCount: options.length,
                                          itemBuilder: (context, index) {
                                            final user =
                                                options.elementAt(index);
                                            return Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 4,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Color.fromRGBO(
                                                    248, 249, 252, 0.5),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: Color.fromRGBO(
                                                      232, 240, 254, 0.8),
                                                ),
                                              ),
                                              child: ListTile(
                                                leading: Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  decoration: BoxDecoration(
                                                    color: Color.fromRGBO(
                                                        232, 240, 254, 0.5),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Icon(
                                                    Icons.person,
                                                    color: AppColors.blue,
                                                    size: 20,
                                                  ),
                                                ),
                                                title: Text(
                                                  user['name'],
                                                  style: const TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                subtitle: Text(
                                                  user['email'],
                                                  style: const TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize: 12,
                                                    color: AppColors.grey,
                                                  ),
                                                ),
                                                onTap: () => onSelected(user),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(height: 24),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.message,
                                      color: AppColors.blue,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  _buildSectionTitle('Message', isSmallScreen),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color.fromRGBO(26, 115, 232, 0.03),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                      spreadRadius: 0,
                                    ),
                                  ],
                                  border: Border.all(
                                      color:
                                          Color.fromRGBO(232, 240, 254, 0.8)),
                                ),
                                child: TextField(
                                  controller: _messageController,
                                  maxLines: 5,
                                  decoration: InputDecoration(
                                    hintText:
                                        'Enter your notification message...',
                                    hintStyle: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 14,
                                      color: Colors.grey[400],
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                          color: Color.fromRGBO(
                                              232, 240, 254, 1.0)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide(
                                          color: Color.fromRGBO(
                                              232, 240, 254, 1.0)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                          color: AppColors.blue, width: 2),
                                    ),
                                    filled: true,
                                    fillColor:
                                        Color.fromRGBO(248, 249, 252, 0.5),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 16,
                                    ),
                                  ),
                                  style: const TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: 15,
                                    color: AppColors.dark,
                                  ),
                                ),
                              ),
                            ],
                          ),
                  ),
                  // Footer
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.vertical(
                          bottom: Radius.circular(20)),
                      border: Border(
                        top: BorderSide(
                            color: Color.fromRGBO(232, 240, 254, 0.8)),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: ElevatedButton.icon(
                            onPressed: _isSending ? null : _sendMessage,
                            icon: _isSending
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.send, color: Colors.white),
                            label: const Text(
                              'Send Notification',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.blue,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 24),
                              elevation: 0,
                              disabledBackgroundColor:
                                  Color.fromRGBO(232, 240, 254, 0.5),
                              disabledForegroundColor: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, bool isSmallScreen) {
    return Text(
      title,
      style: TextStyle(
        fontFamily: 'Space',
        fontSize: isSmallScreen ? 18 : 20,
        fontWeight: FontWeight.bold,
        color: AppColors.dark,
      ),
    );
  }
}
