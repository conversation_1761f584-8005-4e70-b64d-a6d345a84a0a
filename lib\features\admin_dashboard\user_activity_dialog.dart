import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:intl/intl.dart';

class UserActivityDialog extends StatefulWidget {
  final UserModel user;
  final FirestoreService firestoreService;

  const UserActivityDialog({
    super.key,
    required this.user,
    required this.firestoreService,
  });

  @override
  State<UserActivityDialog> createState() => _UserActivityDialogState();
}

class _UserActivityDialogState extends State<UserActivityDialog> {
  List<Map<String, dynamic>> _activityLogs = [];
  bool _isLoading = true;
  String _selectedFilter = 'All';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadActivityLogs();
  }

  Future<void> _loadActivityLogs() async {
    try {
      setState(() => _isLoading = true);
      final logs = await widget.firestoreService.getSubCollection(
        collectionPath: 'users',
        documentId: widget.user.uid,
        subCollectionPath: 'history',
        queryBuilder: (query) {
          query = query.orderBy('date', descending: true).limit(50);
          if (_selectedFilter != 'All') {
            query = query.where('type', isEqualTo: _selectedFilter);
          }
          if (_startDate != null) {
            query = query.where('date',
                isGreaterThanOrEqualTo: Timestamp.fromDate(_startDate!));
          }
          if (_endDate != null) {
            query = query.where('date',
                isLessThanOrEqualTo:
                    Timestamp.fromDate(_endDate!.add(const Duration(days: 1))));
          }
          return query;
        },
      );
      setState(() {
        _activityLogs = logs;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading activity logs: $e');
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading logs: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _loadActivityLogs,
          ),
        ),
      );
    }
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadActivityLogs();
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isWideScreen = screenWidth > 800;
        final dialogWidth = isWideScreen ? 800.0 : screenWidth * 0.9;

        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            width: dialogWidth,
            constraints: const BoxConstraints(maxHeight: 600),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        'Activity Logs - ${widget.user.email}',
                        style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: isWideScreen ? 20 : 18,
                            fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  children: [
                    DropdownButton<String>(
                      value: _selectedFilter,
                      items: ['All', 'Transaction', 'Order', 'Withdraw']
                          .map((filter) => DropdownMenuItem(
                                value: filter,
                                child: Text(filter,
                                    style: TextStyle(
                                        fontSize: isWideScreen ? 16 : 14)),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() => _selectedFilter = value!);
                        _loadActivityLogs();
                      },
                    ),
                    // TextButton(
                    //   onPressed: () => _selectDateRange(context),
                    //   child: Text('Select Date Range',
                    //       style: TextStyle(fontSize: isWideScreen ? 16 : 14)),
                    // ),
                    if (_startDate != null && _endDate != null)
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          '${DateFormat('dd/MM/yy').format(_startDate!)} - ${DateFormat('dd/MM/yy').format(_endDate!)}',
                          style: TextStyle(
                              color: Colors.grey,
                              fontSize: isWideScreen ? 14 : 12),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _activityLogs.isEmpty
                          ? const Center(
                              child: Text('No activity logs found',
                                  style: TextStyle(
                                      fontFamily: 'Space', fontSize: 16)))
                          : SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: SingleChildScrollView(
                                child: DataTable(
                                  columnSpacing: isWideScreen ? 60 : 20,
                                  headingRowColor: WidgetStateProperty.all(
                                      Colors.grey[100]),
                                  dataRowColor:
                                      WidgetStateProperty.all(Colors.white),
                                  columns: [
                                    DataColumn(
                                        label: Text('ID',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                    DataColumn(
                                        label: Text('Description',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                    DataColumn(
                                        label: Text('Amount',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                    DataColumn(
                                        label: Text('Status',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                    DataColumn(
                                        label: Text('Date',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                    DataColumn(
                                        label: Text('Type',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontWeight: FontWeight.bold,
                                                fontSize:
                                                    isWideScreen ? 16 : 14))),
                                  ],
                                  rows: _activityLogs
                                      .map((log) => DataRow(cells: [
                                            DataCell(Text(log['id'] ?? 'N/A',
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                            DataCell(Text(
                                                log['description'] ?? 'N/A',
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                            DataCell(Text(
                                                log['amount'] != null
                                                    ? '\$${log['amount'].toStringAsFixed(2)}'
                                                    : 'N/A',
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                            DataCell(Text(
                                                log['status'] ?? 'N/A',
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                            DataCell(Text(
                                                _formatDate(log['date']),
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                            DataCell(Text(log['type'] ?? 'N/A',
                                                style: TextStyle(
                                                    fontSize: isWideScreen
                                                        ? 14
                                                        : 12))),
                                          ]))
                                      .toList(),
                                ),
                              ),
                            ),
                ),
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Close',
                        style: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: isWideScreen ? 16 : 14)),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }
}

