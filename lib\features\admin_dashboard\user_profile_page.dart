import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

import '../../core/services/cRUD_services.dart';

class UserProfileDialog extends StatefulWidget {
  final String uid;
  final FirestoreService firestoreService;

  const UserProfileDialog({
    super.key,
    required this.uid,
    required this.firestoreService,
  });

  @override
  State<UserProfileDialog> createState() => _UserProfileDialogState();
}

class _UserProfileDialogState extends State<UserProfileDialog> {
  UserModel? _user;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  Future<void> _fetchUserData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final userData = await widget.firestoreService.getDocument(
        collectionPath: 'users',
        documentId: widget.uid,
      );

      if (userData == null) {
        setState(() {
          _errorMessage = 'User not found';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _user = UserModel.fromMap(userData, widget.uid);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error fetching user data: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isWideScreen = screenWidth > 800;
        final dialogWidth = isWideScreen ? 600.0 : screenWidth * 0.9;

        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: dialogWidth,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, AppColors.light],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                _buildContent(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.blue, AppColors.blue.withOpacity(0.8)],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              _user != null ? '${_user!.name}\'s Profile' : 'User Profile',
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(32),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _errorMessage!,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: _fetchUserData,
              child: const Text(
                'Retry',
                style: TextStyle(
                  fontFamily: 'Space',
                  color: AppColors.blue,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Flexible(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfilePicture(),
            const SizedBox(height: 16),
            _buildSectionTitle('Basic Information'),
            _buildDetailRow('UID', _user!.uid),
            _buildDetailRow('Name', _user!.name),
            _buildDetailRow('Email', _user!.email),
            _buildDetailRow('Mobile Number',
                _user!.mobileNumber.isEmpty ? 'N/A' : _user!.mobileNumber),
            _buildDetailRow(
                'Email Verified', _user!.emailVerified ? 'Yes' : 'No'),
            const SizedBox(height: 16),
            _buildSectionTitle('Status'),
            _buildDetailRow('Is Admin', _user!.isAdmin ? 'Yes' : 'No'),
            _buildDetailRow('Is Publisher', _user!.isPublisher ? 'Yes' : 'No'),
            _buildDetailRow('Is Blocked', _user!.isBlocked ? 'Yes' : 'No'),
            const SizedBox(height: 16),
            _buildSectionTitle('Timestamps'),
            _buildDetailRow('Created At', _formatDate(_user!.createdAt)),
            _buildDetailRow('Last Login', _formatDate(_user!.lastLogin)),
            const SizedBox(height: 16),
            _buildSectionTitle('Assigned Orders'),
            _buildOrdersList(),
            const SizedBox(height: 16),
            _buildSectionTitle('Additional Details'),
            _buildAdditionalDetails(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontFamily: 'Space',
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.dark,
        ),
      ),
    );
  }

  Widget _buildProfilePicture() {
    return Center(
      child: CircleAvatar(
        radius: 50,
        backgroundColor: AppColors.grey.withOpacity(0.2),
        backgroundImage: _user!.profilePictureUrl.isNotEmpty
            ? NetworkImage(_user!.profilePictureUrl)
            : null,
        child: _user!.profilePictureUrl.isEmpty
            ? Icon(
                Icons.person,
                size: 50,
                color: AppColors.grey,
              )
            : null,
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.dark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    if (_user!.assignedOrderIds.isEmpty) {
      return const Text(
        'No assigned orders',
        style: TextStyle(
          fontFamily: 'Space',
          fontSize: 16,
          color: AppColors.grey,
        ),
      );
    }

    return Column(
      children: _user!.assignedOrderIds.map((orderId) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Text(
                orderId,
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 16,
                  color: AppColors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAdditionalDetails() {
    if (_user!.additionalDetails.isEmpty) {
      return const Text(
        'No additional details',
        style: TextStyle(
          fontFamily: 'Space',
          fontSize: 16,
          color: AppColors.grey,
        ),
      );
    }

    return Column(
      children: _user!.additionalDetails.entries.map((entry) {
        return _buildDetailRow(
          entry.key,
          entry.value.toString(),
        );
      }).toList(),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }
}
