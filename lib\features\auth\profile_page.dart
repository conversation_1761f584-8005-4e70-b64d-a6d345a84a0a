import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/services/auth_service.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final AuthService _authService = AuthService();
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _mobileController;
  late TextEditingController _profilePictureController;
  bool _isPublisher = false;
  bool _isEditing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _emailController = TextEditingController();
    _mobileController = TextEditingController();
    _profilePictureController = TextEditingController();

    // Load initial data
    _authService.getUserData().then((userData) {
      if (userData != null) {
        setState(() {
          _nameController.text = userData['name'] ?? '';
          _emailController.text = userData['email'] ?? '';
          _mobileController.text = userData['mobileNumber'] ?? '';
          _profilePictureController.text = userData['profilePictureUrl'] ?? '';
          _isPublisher = userData['isPublisher'] ?? false;
        });
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _profilePictureController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        await _authService.updateUserProfile(
          name: _nameController.text,
          mobileNumber: _mobileController.text,
          profilePictureUrl: _profilePictureController.text,
          isPublisher: _isPublisher,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
        setState(() {
          _isEditing = false;
          _isLoading = false;
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update profile: $e')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  // Future<void> _pickImage() async {
  //   final picker = ImagePicker();
  //   final pickedFile = await picker.pickImage(source: ImageSource.gallery);
  //   if (pickedFile != null) {
  //     setState(() => _isLoading = true);
  //     try {
  //       final bytes = await pickedFile.readAsBytes(); // Web returns Uint8List
  //       String fileName = 'profile_${_authService.currentUser!.uid}.jpg';
  //       Reference storageRef =
  //           FirebaseStorage.instance.ref().child('profile_pics/$fileName');

  //       // Upload to Storage
  //       await storageRef.putData(
  //           bytes, SettableMetadata(contentType: 'image/jpeg'));
  //       String downloadUrl = await storageRef.getDownloadURL();

  //       // Update controller
  //       _profilePictureController.text = downloadUrl;

  //       // Update Firestore
  //       await FirebaseFirestore.instance
  //           .collection('users')
  //           .doc(_authService.currentUser!.uid)
  //           .update({
  //         'profilePictureUrl': downloadUrl,
  //         'updatedAt': FieldValue.serverTimestamp(),
  //       });

  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('Profile picture updated')),
  //       );
  //     } catch (e) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         SnackBar(content: Text('Failed to upload image: $e')),
  //       );
  //     } finally {
  //       setState(() => _isLoading = false);
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.close : Icons.edit),
            onPressed: _isLoading
                ? null
                : () {
                    setState(() {
                      _isEditing = !_isEditing;
                    });
                  },
          ),
        ],
      ),
      body: StreamBuilder<Map<String, dynamic>?>(
        stream: _authService.streamUserData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting &&
              !snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }

          final userData = snapshot.data ?? {};
          if (!_isEditing) {
            _nameController.text = userData['name'] ?? '';
            _emailController.text = userData['email'] ?? '';
            _mobileController.text = userData['mobileNumber'] ?? '';
            _profilePictureController.text =
                userData['profilePictureUrl'] ?? '';
            _isPublisher = userData['isPublisher'] ?? false;
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Picture
                  Center(
                    child: Stack(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundImage:
                              _profilePictureController.text.isNotEmpty
                                  ? NetworkImage(_profilePictureController.text)
                                  : null,
                          child: _profilePictureController.text.isEmpty
                              ? const Icon(Icons.person, size: 50)
                              : null,
                        ),
                        // if (_isEditing)
                        //   Positioned(
                        //     bottom: 0,
                        //     right: 0,
                        //     child: IconButton(
                        //       icon: const Icon(Icons.camera_alt),
                        //       onPressed: _isLoading ? null : _pickImage,
                        //     ),
                        //   ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Name Field
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Name',
                      border: OutlineInputBorder(),
                    ),
                    enabled: _isEditing && !_isLoading,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your name';
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) => _saveProfile(), // Submit on Enter
                  ),
                  const SizedBox(height: 16),

                  // Email Field (Read-only)
                  TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      border: OutlineInputBorder(),
                    ),
                    enabled: false,
                  ),
                  const SizedBox(height: 16),

                  // Email Verification Status
                  Row(
                    children: [
                      Text(
                        'Email Verified: ${userData['emailVerified'] ?? false ? 'Yes' : 'No'}',
                        style: const TextStyle(fontSize: 16),
                      ),
                      if (!(userData['emailVerified'] ?? false) && _isEditing)
                        TextButton(
                          onPressed: _isLoading
                              ? null
                              : () async {
                                  await _authService.sendEmailVerification();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('Verification email sent')),
                                  );
                                },
                          child: const Text('Verify Now'),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Mobile Number Field
                  TextFormField(
                    controller: _mobileController,
                    decoration: const InputDecoration(
                      labelText: 'Mobile Number',
                      border: OutlineInputBorder(),
                    ),
                    enabled: _isEditing && !_isLoading,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (!RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(value)) {
                          return 'Please enter a valid mobile number';
                        }
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) => _saveProfile(), // Submit on Enter
                  ),
                  // const SizedBox(height: 16),

                  // // Profile Picture URL Field
                  // TextFormField(
                  //   controller: _profilePictureController,
                  //   decoration: const InputDecoration(
                  //     labelText: 'Profile Picture URL',
                  //     border: OutlineInputBorder(),
                  //   ),
                  //   enabled: _isEditing && !_isLoading,
                  //   validator: (value) {
                  //     if (value != null && value.isNotEmpty) {
                  //       if (!Uri.tryParse(value)!.isAbsolute) {
                  //         return 'Please enter a valid URL';
                  //       }
                  //     }
                  //     return null;
                  //   },
                  //   onFieldSubmitted: (_) => _saveProfile(), // Submit on Enter
                  // ),
                  const SizedBox(height: 16),

                  // Publisher Status
                  Row(
                    children: [
                      const Text('Publisher Status:',
                          style: TextStyle(fontSize: 16)),
                      const SizedBox(width: 10),
                      Switch(
                        value: _isPublisher,
                        onChanged: _isEditing && !_isLoading
                            ? (value) {
                                setState(() {
                                  _isPublisher = value;
                                });
                              }
                            : null,
                      ),
                      Text(_isPublisher ? 'Yes' : 'No',
                          style: const TextStyle(fontSize: 16)),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Save Button
                  if (_isEditing)
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveProfile,
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Save Changes'),
                    ),

                  // Additional Actions
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _isLoading
                        ? null
                        : () async {
                            await _authService.signOut();
                            Navigator.of(context).pushReplacementNamed('/auth');
                          },
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.redAccent),
                    child: const Text('Sign Out'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
