import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/features/landing/landing_page.dart';
import 'dart:html' as html;

class LandingDataPage extends StatefulWidget {
  const LandingDataPage({super.key});

  @override
  State<LandingDataPage> createState() => _LandingDataPageState();
}

class _LandingDataPageState extends State<LandingDataPage> {
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final isMediumScreen = MediaQuery.of(context).size.width < 900;

    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.blue,
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading landing page data...',
                    style: GoogleFonts.getFont(
                      'Space Grotesk',
                      fontSize: 16,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(
                        248, 249, 252, 1.0), // AppColors.componentBackColor
                    Colors.white,
                  ],
                  stops: [0.0, 0.8],
                ),
              ),
              child: SingleChildScrollView(
                child: Center(
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    margin: const EdgeInsets.symmetric(
                        vertical: 24, horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          margin: const EdgeInsets.only(bottom: 24),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(26, 115, 232, 0.08),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                                spreadRadius: 2,
                              ),
                            ],
                            gradient: LinearGradient(
                              colors: [
                                Colors.white,
                                Color.fromRGBO(232, 240, 254,
                                    0.3), // AppColors.blueLightest
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Color.fromRGBO(232, 240, 254, 0.5),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.web,
                                      color: AppColors.blue,
                                      size: isSmallScreen ? 24 : 28,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Text(
                                    'Landing Page Management',
                                    style: GoogleFonts.getFont(
                                      'Space Grotesk',
                                      fontSize: isSmallScreen ? 22 : 28,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                ],
                              ),
                              ElevatedButton.icon(
                                onPressed: () {
                                  final url = Uri.base
                                      .resolve('/#/landing-preview')
                                      .toString();
                                  html.window.open(url, '_blank');
                                },
                                icon: const Icon(Icons.visibility),
                                label: Text(
                                  'Preview Page',
                                  style: GoogleFonts.getFont(
                                    'Space Grotesk',
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.blue,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 16,
                                  ),
                                  elevation: 0,
                                ),
                              )
                            ],
                          ),
                        ),
                        // Form Content
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Color.fromRGBO(26, 115, 232, 0.04),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: isMediumScreen
                              ? _buildSingleColumnForm(isSmallScreen)
                              : _buildTwoColumnForm(isSmallScreen),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildSingleColumnForm(bool isSmallScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeroSection(isSmallScreen),
        _buildStatsSection(isSmallScreen),
        _buildServicesSection(isSmallScreen),
        _buildTestimonialsSection(isSmallScreen),
        _buildFooterSection(isSmallScreen),
      ],
    );
  }

  Widget _buildTwoColumnForm(bool isSmallScreen) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeroSection(isSmallScreen),
              _buildStatsSection(isSmallScreen),
            ],
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildServicesSection(isSmallScreen),
              _buildTestimonialsSection(isSmallScreen),
              _buildFooterSection(isSmallScreen),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeroSection(bool isSmallScreen) {
    return _buildSection('Hero Section', isSmallScreen, [
      const HeroForm(),
    ]);
  }

  Widget _buildStatsSection(bool isSmallScreen) {
    return _buildSection('Stats', isSmallScreen, [
      const StatForm(),
      const SizedBox(height: 16),
      const StatsList(),
    ]);
  }

  Widget _buildServicesSection(bool isSmallScreen) {
    return _buildSection('Services', isSmallScreen, [
      const ServiceForm(),
      const SizedBox(height: 16),
      const ServicesList(),
    ]);
  }

  Widget _buildTestimonialsSection(bool isSmallScreen) {
    return _buildSection('Testimonials', isSmallScreen, [
      const TestimonialForm(),
      const SizedBox(height: 16),
      const TestimonialsList(),
    ]);
  }

  Widget _buildFooterSection(bool isSmallScreen) {
    return _buildSection('Footer Sections', isSmallScreen, [
      const FooterForm(),
      const SizedBox(height: 16),
      const FooterList(),
    ]);
  }

  Widget _buildSection(
      String title, bool isSmallScreen, List<Widget> children) {
    IconData sectionIcon = _getSectionIcon(title);

    return Padding(
      padding: const EdgeInsets.only(bottom: 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(232, 240, 254, 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  sectionIcon,
                  color: AppColors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.getFont(
                  'Space Grotesk',
                  fontSize: isSmallScreen ? 18 : 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(26, 115, 232, 0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
            ),
            child: Column(
              children: children
                  .map((child) => Padding(
                        padding: const EdgeInsets.only(bottom: 20),
                        child: child,
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'Hero Section':
        return Icons.home;
      case 'Stats':
        return Icons.bar_chart;
      case 'Services':
        return Icons.design_services;
      case 'Testimonials':
        return Icons.format_quote;
      case 'Footer Sections':
        return Icons.format_list_bulleted;
      default:
        return Icons.web;
    }
  }
}

class HeroForm extends StatefulWidget {
  const HeroForm({super.key});

  @override
  State<HeroForm> createState() => _HeroFormState();
}

class _HeroFormState extends State<HeroForm> {
  final _titleController = TextEditingController();
  final _subtitleController = TextEditingController();
  final _imageUrlController = TextEditingController();
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  @override
  void initState() {
    super.initState();
    _fetchHeroData();
  }

  Future<void> _fetchHeroData() async {
    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('hero')
          .doc('content')
          .get();
      if (doc.exists && mounted) {
        final data = doc.data() as Map<String, dynamic>;
        setState(() {
          _titleController.text = data['title'] ?? '';
          _subtitleController.text = data['subtitle'] ?? '';
          _imageUrlController.text = data['imageUrl'] ?? '';
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching hero data: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'title': {
        'value': _titleController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Title is required' : null,
      },
      'subtitle': {
        'value': _subtitleController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Subtitle is required' : null,
      },
      'imageUrl': {
        'value': _imageUrlController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Image URL is required' : null,
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _saveHero() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.save, color: AppColors.blue),
            const SizedBox(width: 8),
            const Text('Confirm Save'),
          ],
        ),
        content:
            const Text('Are you sure you want to save these hero settings?'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.dark,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('hero')
          .doc('content')
          .set({
        'title': _titleController.text,
        'subtitle': _subtitleController.text,
        'imageUrl': _imageUrlController.text,
      }, SetOptions(merge: true));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Hero settings saved successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving hero settings: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _titleController,
          label: 'Title',
          errorText: _errors['title'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _subtitleController,
          label: 'Subtitle',
          errorText: _errors['subtitle'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _imageUrlController,
          label: 'Image URL',
          errorText: _errors['imageUrl'],
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _saveHero,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.save),
            label: Text(
              'Save Hero Section',
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              elevation: 0,
              disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
              disabledForegroundColor: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    required bool isSmallScreen,
  }) {
    IconData fieldIcon = _getFieldIcon(label);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              fieldIcon,
              color: AppColors.blue,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.blue, width: 2),
            ),
            filled: true,
            fillColor: Color.fromRGBO(248, 249, 252, 0.5),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            errorText: errorText,
            errorStyle: GoogleFonts.getFont(
              'Space Grotesk',
              color: AppColors.red,
              fontSize: 12,
            ),
            hintText: 'Enter ${label.toLowerCase()}...',
            hintStyle: GoogleFonts.getFont(
              'Space Grotesk',
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: 15,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }

  IconData _getFieldIcon(String label) {
    switch (label.toLowerCase()) {
      case 'title':
        return Icons.title;
      case 'subtitle':
        return Icons.subject;
      case 'image url':
        return Icons.image;
      case 'number (e.g., 5000+)':
        return Icons.numbers;
      case 'label':
        return Icons.label;
      case 'description':
        return Icons.description;
      case 'features (comma-separated)':
        return Icons.format_list_bulleted;
      case 'items (comma-separated)':
        return Icons.list;
      case 'name':
        return Icons.person;
      case 'position':
        return Icons.work;
      case 'comment':
        return Icons.comment;
      case 'rating (1-5)':
        return Icons.star;
      default:
        return Icons.edit;
    }
  }
}

class StatForm extends StatefulWidget {
  const StatForm({super.key});

  @override
  State<StatForm> createState() => _StatFormState();
}

class _StatFormState extends State<StatForm> {
  final _numberController = TextEditingController();
  final _labelController = TextEditingController();
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'number': {
        'value': _numberController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Number is required' : null,
      },
      'label': {
        'value': _labelController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Label is required' : null,
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _addStat() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('stats')
          .add({
        'number': _numberController.text,
        'label': _labelController.text,
      });
      _numberController.clear();
      _labelController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Stat added successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding stat: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _numberController,
          label: 'Number (e.g., 5000+)',
          errorText: _errors['number'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _labelController,
          label: 'Label',
          errorText: _errors['label'],
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _addStat,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.add_chart),
            label: Text(
              'Add Stat',
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              elevation: 0,
              disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
              disabledForegroundColor: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.blue, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
            errorStyle: GoogleFonts.getFont(
              'Space Grotesk',
              color: AppColors.red,
              fontSize: 12,
            ),
          ),
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: 14,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }
}

class StatsList extends StatelessWidget {
  const StatsList({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('stats')
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final stats = snapshot.data!.docs;
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: stats.length,
          itemBuilder: (context, index) {
            final stat = stats[index].data() as Map<String, dynamic>;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.bar_chart,
                    color: AppColors.blue,
                    size: 24,
                  ),
                ),
                title: Text(
                  stat['number'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColors.dark,
                  ),
                ),
                subtitle: Text(
                  stat['label'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
                trailing: Container(
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(255, 235, 238, 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.red),
                    onPressed: () async {
                      bool? confirm = await showDialog<bool>(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Row(
                            children: [
                              const Icon(Icons.warning, color: AppColors.red),
                              const SizedBox(width: 8),
                              const Text('Confirm Delete'),
                            ],
                          ),
                          content: const Text(
                              'Are you sure you want to delete this stat?'),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.dark,
                              ),
                              child: const Text('Cancel'),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context, true),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.red,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Delete'),
                            ),
                          ],
                        ),
                      );

                      if (confirm == true) {
                        await FirebaseFirestore.instance
                            .collection('landing')
                            .doc('main')
                            .collection('stats')
                            .doc(stats[index].id)
                            .delete();
                      }
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class ServiceForm extends StatefulWidget {
  const ServiceForm({super.key});

  @override
  State<ServiceForm> createState() => _ServiceFormState();
}

class _ServiceFormState extends State<ServiceForm> {
  final _iconController = TextEditingController();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _featuresController = TextEditingController();
  bool _isLoading = false;
  final Map<String, String?> _errors = {};
  String? _selectedIcon;

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'icon': {
        'value': _selectedIcon ?? '',
        'validator': (String value) =>
            value.isEmpty ? 'Icon is required' : null,
      },
      'title': {
        'value': _titleController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Title is required' : null,
      },
      'description': {
        'value': _descriptionController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Description is required' : null,
      },
      'features': {
        'value': _featuresController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Features are required' : null,
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _addService() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('services')
          .add({
        'icon': _selectedIcon,
        'title': _titleController.text,
        'description': _descriptionController.text,
        'features': _featuresController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
      });
      _iconController.clear();
      _titleController.clear();
      _descriptionController.clear();
      _featuresController.clear();
      setState(() => _selectedIcon = null);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Service added successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding service: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDropdown(
          label: 'Icon',
          value: _selectedIcon,
          items: ['article', 'link', 'analytics'],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedIcon = value);
            }
          },
          errorText: _errors['icon'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _titleController,
          label: 'Title',
          errorText: _errors['title'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _descriptionController,
          label: 'Description',
          errorText: _errors['description'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _featuresController,
          label: 'Features (comma-separated)',
          errorText: _errors['features'],
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _addService,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.design_services),
            label: Text(
              'Add Service',
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              elevation: 0,
              disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
              disabledForegroundColor: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.blue, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
            errorStyle: GoogleFonts.getFont(
              'Space Grotesk',
              color: AppColors.red,
              fontSize: 12,
            ),
          ),
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: 14,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    String? errorText,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            underline: const SizedBox(),
            items: items
                .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(
                        item,
                        style: GoogleFonts.getFont(
                          'Space Grotesk',
                          fontSize: 14,
                          color: AppColors.dark,
                        ),
                      ),
                    ))
                .toList(),
            onChanged: onChanged,
            dropdownColor: Colors.white,
            focusColor: AppColors.blue,
          ),
        ),
        if (errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              errorText,
              style: GoogleFonts.getFont(
                'Space Grotesk',
                color: AppColors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}

class ServicesList extends StatelessWidget {
  const ServicesList({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('services')
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final services = snapshot.data!.docs;
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: services.length,
          itemBuilder: (context, index) {
            final service = services[index].data() as Map<String, dynamic>;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.design_services,
                    color: AppColors.blue,
                    size: 24,
                  ),
                ),
                title: Text(
                  service['title'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColors.dark,
                  ),
                ),
                subtitle: Text(
                  service['description'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
                trailing: Container(
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(255, 235, 238, 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.red),
                    onPressed: () async {
                      bool? confirm = await showDialog<bool>(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Row(
                            children: [
                              const Icon(Icons.warning, color: AppColors.red),
                              const SizedBox(width: 8),
                              const Text('Confirm Delete'),
                            ],
                          ),
                          content: const Text(
                              'Are you sure you want to delete this service?'),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.dark,
                              ),
                              child: const Text('Cancel'),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context, true),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.red,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Delete'),
                            ),
                          ],
                        ),
                      );

                      if (confirm == true) {
                        await FirebaseFirestore.instance
                            .collection('landing')
                            .doc('main')
                            .collection('services')
                            .doc(services[index].id)
                            .delete();
                      }
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class TestimonialForm extends StatefulWidget {
  const TestimonialForm({super.key});

  @override
  State<TestimonialForm> createState() => _TestimonialFormState();
}

class _TestimonialFormState extends State<TestimonialForm> {
  final _nameController = TextEditingController();
  final _positionController = TextEditingController();
  final _commentController = TextEditingController();
  final _ratingController = TextEditingController();
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'name': {
        'value': _nameController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Name is required' : null,
      },
      'position': {
        'value': _positionController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Position is required' : null,
      },
      'comment': {
        'value': _commentController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Comment is required' : null,
      },
      'rating': {
        'value': _ratingController.text,
        'validator': (String value) {
          final rating = int.tryParse(value);
          if (rating == null || rating < 1 || rating > 5) {
            return 'Rating must be between 1 and 5';
          }
          return null;
        },
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _addTestimonial() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('testimonials')
          .add({
        'name': _nameController.text,
        'position': _positionController.text,
        'comment': _commentController.text,
        'rating': int.tryParse(_ratingController.text) ?? 5,
      });
      _nameController.clear();
      _positionController.clear();
      _commentController.clear();
      _ratingController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Testimonial added successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding testimonial: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _nameController,
          label: 'Name',
          errorText: _errors['name'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _positionController,
          label: 'Position',
          errorText: _errors['position'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _commentController,
          label: 'Comment',
          errorText: _errors['comment'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _ratingController,
          label: 'Rating (1-5)',
          keyboardType: TextInputType.number,
          errorText: _errors['rating'],
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _addTestimonial,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.format_quote),
            label: Text(
              'Add Testimonial',
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              elevation: 0,
              disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
              disabledForegroundColor: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    required bool isSmallScreen,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.blue, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
            errorStyle: GoogleFonts.getFont(
              'Space Grotesk',
              color: AppColors.red,
              fontSize: 12,
            ),
          ),
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: 14,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }
}

class TestimonialsList extends StatelessWidget {
  const TestimonialsList({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('testimonials')
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final testimonials = snapshot.data!.docs;
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: testimonials.length,
          itemBuilder: (context, index) {
            final testimonial =
                testimonials[index].data() as Map<String, dynamic>;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.format_quote,
                    color: AppColors.blue,
                    size: 24,
                  ),
                ),
                title: Text(
                  testimonial['name'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColors.dark,
                  ),
                ),
                subtitle: Text(
                  testimonial['comment'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
                trailing: Container(
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(255, 235, 238, 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.red),
                    onPressed: () async {
                      bool? confirm = await showDialog<bool>(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Row(
                            children: [
                              const Icon(Icons.warning, color: AppColors.red),
                              const SizedBox(width: 8),
                              const Text('Confirm Delete'),
                            ],
                          ),
                          content: const Text(
                              'Are you sure you want to delete this testimonial?'),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.dark,
                              ),
                              child: const Text('Cancel'),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context, true),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.red,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Delete'),
                            ),
                          ],
                        ),
                      );

                      if (confirm == true) {
                        await FirebaseFirestore.instance
                            .collection('landing')
                            .doc('main')
                            .collection('testimonials')
                            .doc(testimonials[index].id)
                            .delete();
                      }
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class FooterForm extends StatefulWidget {
  const FooterForm({super.key});

  @override
  State<FooterForm> createState() => _FooterFormState();
}

class _FooterFormState extends State<FooterForm> {
  final _titleController = TextEditingController();
  final _itemsController = TextEditingController();
  bool _isLoading = false;
  final Map<String, String?> _errors = {};

  bool _validateForm() {
    _errors.clear();
    final validators = {
      'title': {
        'value': _titleController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Title is required' : null,
      },
      'items': {
        'value': _itemsController.text,
        'validator': (String value) =>
            value.isEmpty ? 'Items are required' : null,
      },
    };

    bool isValid = true;
    validators.forEach((key, config) {
      final error = (config['validator'] as String? Function(
          String))(config['value'] as String);
      if (error != null) {
        setState(() => _errors[key] = error);
        isValid = false;
      }
    });

    return isValid;
  }

  Future<void> _addFooterSection() async {
    if (!_validateForm()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      await FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('footer')
          .add({
        'title': _titleController.text,
        'items': _itemsController.text
            .split(',')
            .map((e) => e.trim())
            .where((e) => e.isNotEmpty)
            .toList(),
      });
      _titleController.clear();
      _itemsController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Footer section added successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding footer section: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _titleController,
          label: 'Title',
          errorText: _errors['title'],
          isSmallScreen: isSmallScreen,
        ),
        _buildTextField(
          controller: _itemsController,
          label: 'Items (comma-separated)',
          errorText: _errors['items'],
          isSmallScreen: isSmallScreen,
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _addFooterSection,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.format_list_bulleted),
            label: Text(
              'Add Footer Section',
              style: GoogleFonts.getFont(
                'Space Grotesk',
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
              elevation: 0,
              disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
              disabledForegroundColor: Colors.grey,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? errorText,
    required bool isSmallScreen,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.dark,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.blue, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
            errorStyle: GoogleFonts.getFont(
              'Space Grotesk',
              color: AppColors.red,
              fontSize: 12,
            ),
          ),
          style: GoogleFonts.getFont(
            'Space Grotesk',
            fontSize: 14,
            color: AppColors.dark,
          ),
        ),
      ],
    );
  }
}

class FooterList extends StatelessWidget {
  const FooterList({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('landing')
          .doc('main')
          .collection('footer')
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final footers = snapshot.data!.docs;
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: footers.length,
          itemBuilder: (context, index) {
            final footer = footers[index].data() as Map<String, dynamic>;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(26, 115, 232, 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 0.8)),
              ),
              child: ListTile(
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(232, 240, 254, 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.format_list_bulleted,
                    color: AppColors.blue,
                    size: 24,
                  ),
                ),
                title: Text(
                  footer['title'] ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColors.dark,
                  ),
                ),
                subtitle: Text(
                  (footer['items'] as List<dynamic>?)?.join(', ') ?? '',
                  style: GoogleFonts.getFont(
                    'Space Grotesk',
                    fontSize: 14,
                    color: AppColors.grey,
                  ),
                ),
                trailing: Container(
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(255, 235, 238, 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.red),
                    onPressed: () async {
                      bool? confirm = await showDialog<bool>(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: Row(
                            children: [
                              const Icon(Icons.warning, color: AppColors.red),
                              const SizedBox(width: 8),
                              const Text('Confirm Delete'),
                            ],
                          ),
                          content: const Text(
                              'Are you sure you want to delete this footer section?'),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              style: TextButton.styleFrom(
                                foregroundColor: AppColors.dark,
                              ),
                              child: const Text('Cancel'),
                            ),
                            ElevatedButton(
                              onPressed: () => Navigator.pop(context, true),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.red,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text('Delete'),
                            ),
                          ],
                        ),
                      );

                      if (confirm == true) {
                        await FirebaseFirestore.instance
                            .collection('landing')
                            .doc('main')
                            .collection('footer')
                            .doc(footers[index].id)
                            .delete();
                      }
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
