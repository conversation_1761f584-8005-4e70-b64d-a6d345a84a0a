// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/improved_website_dialog.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/widgets/improved_data_table.dart';
import 'package:intl/intl.dart';
import 'package:csv/csv.dart';
import 'package:universal_html/html.dart' as html;

class WebsiteManagementScreen extends StatefulWidget {
  const WebsiteManagementScreen({super.key});

  @override
  State<WebsiteManagementScreen> createState() =>
      _WebsiteManagementScreenState();
}

class _WebsiteManagementScreenState extends State<WebsiteManagementScreen> {
  // Helper methods for modern UI styling
  Widget _buildStatusBadge(String status, {double? fontSize}) {
    IconData icon;
    Color color;

    switch (status) {
      case 'Approved':
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case 'Rejected':
        icon = Icons.cancel;
        color = Colors.red;
        break;
      case 'Pending':
        icon = Icons.pending;
        color = AppColors.blue;
        break;
      case 'On Hold':
        icon = Icons.pause_circle_filled;
        color = AppColors.yellow;
        break;
      default:
        icon = Icons.help;
        color = AppColors.grey;
    }

    return ImprovedStatusBadge(
      text: status,
      color: color,
      icon: icon,
    );
  }

  Widget _buildSpamScoreCell(double? spamScore) {
    // Determine color based on spam score
    Color scoreColor;
    IconData scoreIcon;

    if (spamScore == null) {
      scoreColor = AppColors.grey;
      scoreIcon = Icons.info_outline;
    } else if (spamScore <= 30.0) {
      scoreColor = Colors.green; // Low spam score (good)
      scoreIcon = Icons.check_circle;
    } else if (spamScore <= 70.0) {
      scoreColor = Colors.orange; // Medium spam score (caution)
      scoreIcon = Icons.warning;
    } else {
      scoreColor = Colors.red; // High spam score (bad)
      scoreIcon = Icons.error;
    }

    // Format the spam score as a percentage with one decimal place
    final formattedScore =
        spamScore != null ? '${spamScore.toStringAsFixed(1)}%' : 'N/A';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: scoreColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: scoreColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            scoreIcon,
            size: 14,
            color: scoreColor,
          ),
          const SizedBox(width: 4),
          Text(
            formattedScore,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: scoreColor,
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _headerStyle(bool isSmallScreen) {
    return TextStyle(
      fontFamily: 'Space',
      fontSize: isSmallScreen ? 14 : 16,
      fontWeight: FontWeight.w600,
      color: AppColors.dark,
    );
  }

  Color _getLightColorForIcon(Color color) {
    return color.withOpacity(0.1);
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  String _selectedStatus = 'All';
  final TextEditingController _searchController = TextEditingController();
  List<WebsiteModel> _websites = [];
  Map<String, Map<String, String>> _publisherDetails = {};
  Map<String, int> _orderCounts = {}; // New map to store order counts
  bool _isLoading = false;
  bool _isSearchLoading = false;
  Timer? _debounce;
  String? _adminUid;
  List<String> _selectedWebsiteIds = [];
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  String _sortField = 'submissionDate';
  bool _sortAscending = false;
  final TextEditingController _daMinController = TextEditingController();
  final TextEditingController _daMaxController = TextEditingController();
  final TextEditingController _drMinController = TextEditingController();
  final TextEditingController _drMaxController = TextEditingController();
  final TextEditingController _trafficMinController = TextEditingController();
  final TextEditingController _trafficMaxController = TextEditingController();
  final TextEditingController _priceMinController = TextEditingController();
  final TextEditingController _priceMaxController = TextEditingController();
  String? _selectedCategory;
  String? _selectedCountry;
  Set<String> _expandedRows = {};
  StreamSubscription<QuerySnapshot>? _websitesSubscription;
  StreamSubscription<QuerySnapshot>? _usersSubscription;
  final _pendingTableScrollController = ScrollController();
  final _websitesScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
    _searchController.addListener(_onSearchChanged);
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data();
      // Uncomment for production
      // if (userData == null || userData['isAdmin'] != true) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(
      //       content: Text('Access denied: Admins only'),
      //       backgroundColor: Colors.red,
      //     ),
      //   );
      //   context.go('/login');
      //   return;
      // }

      setState(() {
        _adminUid = user.uid;
        _isLoading = true;
      });
      _subscribeToData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking admin access: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        _isSearchLoading = true;
      });
      // Filtering is done locally, no need to reload
      Future.delayed(const Duration(milliseconds: 300), () {
        setState(() => _isSearchLoading = false);
      });
    });
  }

  Future<void> _fetchOrderCount(String websiteId) async {
    try {
      final snapshot = await _firestore
          .collection('orders')
          .where('websiteId', isEqualTo: websiteId)
          .get();
      setState(() {
        _orderCounts[websiteId] = snapshot.docs.length;
      });
    } catch (e) {
      print('Error fetching order count for $websiteId: $e');
    }
  }

  void _subscribeToData() {
    // Subscribe to websites
    _websitesSubscription?.cancel();
    _websitesSubscription = _firestore
        .collection('websites')
        .orderBy(_sortField, descending: !_sortAscending)
        .snapshots()
        .listen((snapshot) {
      final websites = snapshot.docs
          .map(
              (doc) => WebsiteModel.fromMap(doc.data()..['websiteId'] = doc.id))
          .toList();

      final ownerIds =
          websites.map((w) => w.publisherId).whereType<String>().toSet();

      // Fetch order counts for all websites
      for (var website in websites) {
        _fetchOrderCount(website.websiteId!);
      }

      setState(() {
        _websites = websites;
        _isLoading = false;
      });

      // Subscribe to users (publishers)
      _usersSubscription?.cancel();
      _usersSubscription = _firestore
          .collection('users')
          .where(FieldPath.documentId, whereIn: ownerIds.toList())
          .snapshots()
          .listen((snapshot) {
        final publisherDetails = <String, Map<String, String>>{};
        for (var doc in snapshot.docs) {
          final userData = doc.data();
          publisherDetails[doc.id] = {
            'name': userData['name'] ?? 'Unknown',
            'email': userData['email'] ?? 'N/A',
          };
        }

        setState(() {
          _publisherDetails = publisherDetails;
        });
      }, onError: (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading publishers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      });
    }, onError: (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading websites: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _subscribeToData,
          ),
        ),
      );
    });
  }

  List<WebsiteModel> _filteredWebsites(List<WebsiteModel> websites) {
    var filtered = websites;

    // Apply status filter
    if (_selectedStatus != 'All') {
      filtered = filtered
          .where((website) => website.status == _selectedStatus)
          .toList();
    }

    // Apply DA filters
    if (_daMinController.text.isNotEmpty) {
      final daMin = int.tryParse(_daMinController.text);
      if (daMin != null) {
        filtered = filtered.where((website) => website.da >= daMin).toList();
      }
    }
    if (_daMaxController.text.isNotEmpty) {
      final daMax = int.tryParse(_daMaxController.text);
      if (daMax != null) {
        filtered = filtered.where((website) => website.da <= daMax).toList();
      }
    }

    // Apply DR filters
    if (_drMinController.text.isNotEmpty) {
      final drMin = int.tryParse(_drMinController.text);
      if (drMin != null) {
        filtered = filtered.where((website) => website.dr >= drMin).toList();
      }
    }
    if (_drMaxController.text.isNotEmpty) {
      final drMax = int.tryParse(_drMaxController.text);
      if (drMax != null) {
        filtered = filtered.where((website) => website.dr <= drMax).toList();
      }
    }

    // Apply traffic filters
    if (_trafficMinController.text.isNotEmpty) {
      final trafficMin = int.tryParse(_trafficMinController.text);
      if (trafficMin != null) {
        filtered =
            filtered.where((website) => website.traffic >= trafficMin).toList();
      }
    }
    if (_trafficMaxController.text.isNotEmpty) {
      final trafficMax = int.tryParse(_trafficMaxController.text);
      if (trafficMax != null) {
        filtered =
            filtered.where((website) => website.traffic <= trafficMax).toList();
      }
    }

    // Apply price filters
    if (_priceMinController.text.isNotEmpty) {
      final priceMin = double.tryParse(_priceMinController.text);
      if (priceMin != null) {
        filtered =
            filtered.where((website) => website.pricing >= priceMin).toList();
      }
    }
    if (_priceMaxController.text.isNotEmpty) {
      final priceMax = double.tryParse(_priceMaxController.text);
      if (priceMax != null) {
        filtered =
            filtered.where((website) => website.pricing <= priceMax).toList();
      }
    }

    // Apply category filter
    if (_selectedCategory != null && _selectedCategory != 'All') {
      filtered = filtered
          .where((website) => website.categories.contains(_selectedCategory))
          .toList();
    }

    // Apply country filter
    if (_selectedCountry != null && _selectedCountry != 'All') {
      filtered = filtered
          .where((website) => website.country == _selectedCountry)
          .toList();
    }

    return filtered;
  }

  List<WebsiteModel> _searchedWebsites(List<WebsiteModel> websites) {
    if (_searchController.text.isEmpty) return websites;
    final searchTerm = _searchController.text.toLowerCase();
    return websites
        .where((website) =>
            website.url.toLowerCase().contains(searchTerm) ||
            website.domainName.toLowerCase().contains(searchTerm) ||
            website.websiteId!.toLowerCase().contains(searchTerm) ||
            website.categories.any(
                (category) => category.toLowerCase().contains(searchTerm)) ||
            website.allowedTopics
                .any((topic) => topic.toLowerCase().contains(searchTerm)) ||
            website.disallowedTopics
                .any((topic) => topic.toLowerCase().contains(searchTerm)) ||
            website.country.toLowerCase().contains(searchTerm) ||
            website.backlinkType.toLowerCase().contains(searchTerm) ||
            (_publisherDetails[website.publisherId]?['name'] ?? '')
                .toLowerCase()
                .contains(searchTerm) ||
            (_publisherDetails[website.publisherId]?['email'] ?? '')
                .toLowerCase()
                .contains(searchTerm))
        .toList();
  }

  Future<void> _logAuditAction(
      String websiteId, String action, String? details) async {
    try {
      await _firestore
          .collection('websites')
          .doc(websiteId)
          .collection('audit_logs')
          .add({
        'adminId': _adminUid,
        'action': action,
        'details': details,
        'timestamp': Timestamp.now(),
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error logging audit action: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _changeStatus(WebsiteModel website, String newStatus) async {
    if (newStatus == website.status) return;

    String? rejectionReason;
    if (newStatus == 'Rejected') {
      final reasonController = TextEditingController();
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Reject Website'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Provide a reason for rejecting ${website.url}:'),
              TextField(
                controller: reasonController,
                decoration: const InputDecoration(
                  labelText: 'Rejection Reason',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Reject', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirm != true) return;
      rejectionReason =
          reasonController.text.isNotEmpty ? reasonController.text : null;
    }

    try {
      // Update Firestore
      await _firestore.collection('websites').doc(website.websiteId).update({
        'status': newStatus,
        'rejectionReason': newStatus == 'Rejected' ? rejectionReason : null,
        'lastUpdated': Timestamp.now(),
      });

      // Update local state
      setState(() {
        final index =
            _websites.indexWhere((w) => w.websiteId == website.websiteId);
        if (index != -1) {
          _websites[index] = website.copyWith(
            status: newStatus,
            rejectionReason: newStatus == 'Rejected' ? rejectionReason : null,
            lastUpdated: Timestamp.now(),
          );
        }
      });

      await _logAuditAction(
        website.websiteId!,
        'Status Changed',
        'Changed to $newStatus${rejectionReason != null ? ': $rejectionReason' : ''}',
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Website status updated to $newStatus')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating status: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _editWebsite(
      WebsiteModel website, Map<String, dynamic> updates) async {
    try {
      // Update Firestore
      await _firestore.collection('websites').doc(website.websiteId).update({
        ...updates,
        'lastUpdated': Timestamp.now(),
      });

      // Update local state
      setState(() {
        final index =
            _websites.indexWhere((w) => w.websiteId == website.websiteId);
        if (index != -1) {
          _websites[index] = website.copyWith(
            da: updates['da'] ?? website.da,
            dr: updates['dr'] ?? website.dr,
            traffic: updates['traffic'] ?? website.traffic,
            pricing: updates['pricing'] ?? website.pricing,
            basePricing: updates['basePricing'] ?? website.basePricing,
            specialTopicsAdditionalPrice:
                updates['specialTopicsAdditionalPrice'] ??
                    website.specialTopicsAdditionalPrice,
            wordCountMin: updates['wordCountMin'] ?? website.wordCountMin,
            wordCountMax: updates['wordCountMax'] ?? website.wordCountMax,
            maxLinks: updates['maxLinks'] ?? website.maxLinks,
            categories: updates['categories'] ?? website.categories,
            allowedTopics: updates['allowedTopics'] ?? website.allowedTopics,
            disallowedTopics:
                updates['disallowedTopics'] ?? website.disallowedTopics,
            contentType: updates['contentType'] ?? website.contentType,
            contactEmail: updates['contactEmail'] ?? website.contactEmail,
            notes: updates['notes'] ?? website.notes,
            lastUpdated: Timestamp.now(),
          );
        }
      });

      await _logAuditAction(
        website.websiteId!,
        'Edited Website',
        'Updated fields: ${updates.keys.join(", ")}',
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Website updated')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating website: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteWebsite(WebsiteModel website) async {
    final reasonController = TextEditingController();
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Website'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Are you sure you want to delete ${website.url}?'),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Deletion Reason',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    try {
      // Archive to deleted_websites
      await _firestore
          .collection('deleted_websites')
          .doc(website.websiteId)
          .set({
        ...website.toMap(),
        'deletedAt': Timestamp.now(),
        'deletedBy': _adminUid,
        'deletionReason':
            reasonController.text.isNotEmpty ? reasonController.text : null,
      });

      // Delete from websites
      await _firestore.collection('websites').doc(website.websiteId).delete();

      // Update local state
      setState(() {
        _websites.removeWhere((w) => w.websiteId == website.websiteId);
        _orderCounts.remove(website.websiteId);
      });

      await _logAuditAction(
        website.websiteId!,
        'Deleted Website',
        reasonController.text.isNotEmpty
            ? reasonController.text
            : 'No reason provided',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Website ${website.url} deleted'),
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () async {
              await _firestore
                  .collection('websites')
                  .doc(website.websiteId)
                  .set(website.toMap());
              await _firestore
                  .collection('deleted_websites')
                  .doc(website.websiteId)
                  .delete();
              // Local state will be updated by stream
            },
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting website: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _bulkAction(String action, String? status) async {
    if (_selectedWebsiteIds.isEmpty) return;

    try {
      for (var websiteId in _selectedWebsiteIds) {
        final website = _websites.firstWhere((w) => w.websiteId == websiteId);
        if (action == 'status' && status != null) {
          String? rejectionReason;
          if (status == 'Rejected') {
            rejectionReason = 'Bulk rejection';
          }
          await _firestore.collection('websites').doc(websiteId).update({
            'status': status,
            'rejectionReason': rejectionReason,
            'lastUpdated': Timestamp.now(),
          });
          await _logAuditAction(
            websiteId,
            'Bulk Status Changed',
            'Changed to $status${rejectionReason != null ? ': $rejectionReason' : ''}',
          );
          // Update local state
          setState(() {
            final index = _websites.indexWhere((w) => w.websiteId == websiteId);
            if (index != -1) {
              _websites[index] = website.copyWith(
                status: status,
                rejectionReason: rejectionReason,
                lastUpdated: Timestamp.now(),
              );
            }
          });
        } else if (action == 'delete') {
          await _firestore.collection('deleted_websites').doc(websiteId).set({
            ...website.toMap(),
            'deletedAt': Timestamp.now(),
            'deletedBy': _adminUid,
            'deletionReason': 'Bulk deletion',
          });
          await _firestore.collection('websites').doc(websiteId).delete();
          await _logAuditAction(websiteId, 'Bulk Deleted', 'Bulk deletion');
          // Update local state
          setState(() {
            _websites.removeWhere((w) => w.websiteId == websiteId);
            _orderCounts.remove(websiteId);
          });
        }
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Bulk $action completed')),
      );
      setState(() => _selectedWebsiteIds.clear());
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error performing bulk $action: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _exportToCsv(List<WebsiteModel> websites) {
    final List<List<dynamic>> rows = [
      [
        'Orders',
        'Website ID',
        'URL',
        'Domain Name',
        'Language',
        'Publisher Name',
        'Publisher Email',
        'Is Sponsored',
        'Categories',
        'DA',
        'DR',
        'Traffic',
        'Pricing',
        'Base Pricing',
        'Special Topics Additional Price',
        'Has Special Topics Pricing',
        'Backlink Type',
        'Allowed Topics',
        'Disallowed Topics',
        'Word Count Min',
        'Word Count Max',
        'Max Links',
        'Status',
        'Submission Date',
        'Created At',
        'Last Updated',
        'Is Active',
        'Content Type',
        'Contact Email',
        'Notes',
        'Views Count',
        'Clicks Count',
        'Country',
        'Rejection Reason',
      ],
      ...websites.map((website) => [
            _orderCounts[website.websiteId] ?? 0,
            website.websiteId,
            website.url,
            website.domainName,
            website.language ?? 'N/A',
            _publisherDetails[website.publisherId]?['name'] ?? 'Unknown',
            _publisherDetails[website.publisherId]?['email'] ?? 'N/A',
            website.isSponsored,
            website.categories.join(', '),
            website.da,
            website.dr,
            website.traffic,
            '\$${website.pricing.toStringAsFixed(2)}',
            '\$${website.basePricing.toStringAsFixed(2)}',
            '\$${website.specialTopicsAdditionalPrice.toStringAsFixed(2)}',
            website.hasSpecialTopicsPricing,
            website.backlinkType,
            website.allowedTopics.join(', '),
            website.disallowedTopics.join(', '),
            website.wordCountMin,
            website.wordCountMax,
            website.maxLinks,
            website.status,
            _formatDate(website.submissionDate),
            _formatDate(website.createdAt),
            _formatDate(website.lastUpdated),
            website.isActive,
            website.contentType.join(', '),
            website.contactEmail,
            website.notes,
            website.viewsCount,
            website.clicksCount,
            website.country,
            website.rejectionReason ?? 'N/A',
          ]),
    ];

    String csv = const ListToCsvConverter().convert(rows);
    final bytes = utf8.encode(csv);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download = 'websites_export_${DateTime.now().toIso8601String()}.csv';
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  void _viewDetails(WebsiteModel website) {
    showDialog(
      context: context,
      builder: (context) => ImprovedWebsiteDialog(
        website: website,
        publisherName:
            _publisherDetails[website.publisherId]?['name'] ?? 'Unknown',
        publisherEmail:
            _publisherDetails[website.publisherId]?['email'] ?? 'N/A',
        onEdit: (updates) => _editWebsite(website, updates),
        onDelete: () => _deleteWebsite(website),
        onChangeStatus: (status) => _changeStatus(website, status),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _daMinController.dispose();
    _daMaxController.dispose();
    _drMinController.dispose();
    _drMaxController.dispose();
    _trafficMinController.dispose();
    _trafficMaxController.dispose();
    _priceMinController.dispose();
    _priceMaxController.dispose();
    _debounce?.cancel();
    _websitesSubscription?.cancel();
    _usersSubscription?.cancel();
    super.dispose();
  }

  Widget _buildTab(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => setState(() {
        _selectedStatus = title;
        _currentPage = 1;
      }),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [AppColors.yellow, AppColors.yellow.withOpacity(0.7)],
                )
              : null,
          color: isSelected ? null : AppColors.light,
          borderRadius: BorderRadius.circular(12),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.yellow.withOpacity(0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Text(
          title,
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 16,
            color: AppColors.dark,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildImprovedTab(
    String title,
    bool isSelected,
    IconData icon,
    VoidCallback onTap, {
    Color color = AppColors.blue,
    int? badgeCount,
  }) {
    final lightColor = color == AppColors.blue
        ? const Color.fromRGBO(232, 240, 254, 1.0)
        : color == AppColors.green
            ? const Color.fromRGBO(232, 245, 233, 1.0)
            : color == AppColors.red
                ? const Color.fromRGBO(255, 235, 238, 1.0)
                : const Color.fromRGBO(245, 248, 253, 1.0);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? lightColor : Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color:
                isSelected ? color : const Color.fromRGBO(232, 240, 254, 0.8),
            width: isSelected ? 1.5 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? color : AppColors.grey,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: isSelected ? color : AppColors.dark,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (badgeCount != null && badgeCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  badgeCount.toString(),
                  style: const TextStyle(
                    fontFamily: 'Space',
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdminSummary(List<WebsiteModel> websites, double screenWidth) {
    final totalWebsites = websites.length;
    final pendingWebsites = websites.where((w) => w.status == 'Pending').length;
    final approvedWebsites =
        websites.where((w) => w.status == 'Approved').length;
    final rejectedWebsites =
        websites.where((w) => w.status == 'Rejected').length;
    final holdedWebsites = websites.where((w) => w.status == 'On Hold').length;

    final crossAxisCount = screenWidth < 600
        ? 1
        : screenWidth < 900
            ? 2
            : 5;
    final cardWidth = screenWidth < 600
        ? screenWidth * 0.9
        : screenWidth / crossAxisCount - 32;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          title: 'Total Websites',
          value: totalWebsites.toString(),
          color: AppColors.blue,
          icon: Icons.web,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Approved',
          value: approvedWebsites.toString(),
          color: Colors.green,
          icon: Icons.check_circle,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'On Hold',
          value: holdedWebsites.toString(),
          color: AppColors.yellow,
          icon: Icons.cancel,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Pending',
          value: pendingWebsites.toString(),
          color: AppColors.yellow,
          icon: Icons.hourglass_empty,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Rejected',
          value: rejectedWebsites.toString(),
          color: AppColors.red,
          icon: Icons.cancel,
          cardWidth: cardWidth,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
  }) {
    final isSmallScreen = cardWidth < 200;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: cardWidth,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.componentBackColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              CircleAvatar(
                backgroundColor: color.withOpacity(0.1),
                child: Icon(icon, color: color, size: isSmallScreen ? 20 : 24),
              ),
            ],
          ),
          Spacer(),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 24 : 32,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingWebsitesTable(List<WebsiteModel> pendingWebsites) {
    return SizedBox(
      width: double.infinity,
      child: Card(
        elevation: 0,
        color: AppColors.componentBackColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pending Websites',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
              const SizedBox(height: 16),
              Scrollbar(
                thumbVisibility: true,
                controller: _pendingTableScrollController,
                child: SingleChildScrollView(
                  controller: _pendingTableScrollController,
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columnSpacing: 24,
                    border: TableBorder(
                      horizontalInside: BorderSide(
                        color: Colors.grey.withOpacity(0.1),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    headingRowColor: MaterialStateProperty.all(
                      const Color(0xFFE8F0FE), // Light blue header
                    ),
                    dataRowColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                        if (states.contains(MaterialState.selected)) {
                          return AppColors.blue.withOpacity(0.05);
                        }
                        if (states.contains(MaterialState.hovered)) {
                          return AppColors.blue.withOpacity(0.03);
                        }
                        // Alternating row colors
                        final index = pendingWebsites.indexOf(
                          pendingWebsites.firstWhere(
                            (w) => states.contains(MaterialState.selected)
                                ? _selectedWebsiteIds.contains(w.websiteId)
                                : true,
                            orElse: () => pendingWebsites.first,
                          ),
                        );
                        return index % 2 == 0
                            ? Colors.white
                            : const Color(
                                0xFFF8FAFD); // Very light blue for alternate rows
                      },
                    ),
                    headingRowHeight: 56,
                    dataRowMinHeight: 48,
                    dataRowMaxHeight: 56,
                    columns: [
                      DataColumn(
                          label: Text('Orders', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Website ID', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('URL', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Domain', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Language', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('User ID', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Sponsored', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Categories', style: _headerStyle(false))),
                      DataColumn(label: Text('DA', style: _headerStyle(false))),
                      DataColumn(label: Text('DR', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Traffic', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Pricing', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Base Pricing', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Special Topics Price',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Special Pricing',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Backlink Type',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Allowed Topics',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Disallowed Topics',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Word Count Min',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Word Count Max',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Max Links', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Status', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Submission Date',
                              style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Created At', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Last Updated', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Active', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Content Type', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Contact Email',
                              style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Notes', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Views Count', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Clicks Count', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Country', style: _headerStyle(false))),
                      DataColumn(
                          label:
                              Text('Spam Score', style: _headerStyle(false))),
                      DataColumn(
                          label: Text('Actions', style: _headerStyle(false))),
                    ],
                    rows: pendingWebsites.map((website) {
                      return DataRow(
                        cells: [
                          DataCell(Text(
                            (_orderCounts[website.websiteId] ?? 0).toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.websiteId ?? 'N/A',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w500,
                            ),
                          )),
                          DataCell(
                            Tooltip(
                              message: website.url,
                              child: Text(
                                website.url.length > 20
                                    ? '${website.url.substring(0, 17)}...'
                                    : website.url,
                                style: TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 14,
                                  color: AppColors.blue,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                          DataCell(Text(
                            website.domainName,
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.language ?? 'N/A',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.publisherId ?? 'N/A',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: website.isSponsored
                                    ? const Color(
                                        0xFFE0F2FE) // Light blue background
                                    : const Color(
                                        0xFFF3F4F6), // Light gray background
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: website.isSponsored
                                      ? const Color(
                                          0xFF93C5FD) // Medium blue border
                                      : const Color(
                                          0xFFD1D5DB), // Medium gray border
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                website.isSponsored ? 'Yes' : 'No',
                                style: TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: website.isSponsored
                                      ? AppColors.blue
                                      : AppColors.grey,
                                ),
                              ),
                            ),
                          ),
                          DataCell(Text(
                            website.categories.join(', '),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.da.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.dr.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.traffic.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            '\$${website.pricing.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            '\$${website.basePricing.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            '\$${website.specialTopicsAdditionalPrice.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: website.hasSpecialTopicsPricing
                                    ? const Color(
                                        0xFFE0F2FE) // Light blue background
                                    : const Color(
                                        0xFFF3F4F6), // Light gray background
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: website.hasSpecialTopicsPricing
                                      ? const Color(
                                          0xFF93C5FD) // Medium blue border
                                      : const Color(
                                          0xFFD1D5DB), // Medium gray border
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                website.hasSpecialTopicsPricing ? 'Yes' : 'No',
                                style: TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: website.hasSpecialTopicsPricing
                                      ? AppColors.blue
                                      : AppColors.grey,
                                ),
                              ),
                            ),
                          ),
                          DataCell(Text(
                            website.backlinkType,
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.allowedTopics.join(', '),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          )),
                          DataCell(Text(
                            website.disallowedTopics.join(', '),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          )),
                          DataCell(Text(
                            website.wordCountMin.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.wordCountMax.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.maxLinks.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(_buildStatusBadge(website.status)),
                          DataCell(Text(
                            _formatDate(website.submissionDate),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontStyle: FontStyle.italic,
                            ),
                          )),
                          DataCell(Text(
                            _formatDate(website.createdAt),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontStyle: FontStyle.italic,
                            ),
                          )),
                          DataCell(Text(
                            _formatDate(website.lastUpdated),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontStyle: FontStyle.italic,
                            ),
                          )),
                          DataCell(
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: website.isActive
                                    ? const Color(
                                        0xFFDCFCE7) // Light green background
                                    : const Color(
                                        0xFFFEE2E2), // Light red background
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: website.isActive
                                      ? const Color(
                                          0xFF86EFAC) // Medium green border
                                      : const Color(
                                          0xFFFCA5A5), // Medium red border
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    website.isActive
                                        ? Icons.check_circle
                                        : Icons.cancel,
                                    size: 12,
                                    color: website.isActive
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    website.isActive ? 'Active' : 'Inactive',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: website.isActive
                                          ? Colors.green
                                          : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          DataCell(Text(
                            website.contentType.join(', '),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(_buildSpamScoreCell(website.spamScore)),
                          DataCell(Text(
                            website.contactEmail,
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(Text(
                            website.notes,
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          )),
                          DataCell(Text(
                            website.viewsCount.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.clicksCount.toString(),
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                          DataCell(Text(
                            website.country,
                            style: TextStyle(
                              fontFamily: 'Space',
                              fontSize: 14,
                              color: AppColors.dark,
                            ),
                          )),
                          DataCell(
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ImprovedActionButton(
                                  icon: Icons.check_circle,
                                  color: Colors.green,
                                  tooltip: 'Approve Website',
                                  onPressed: () =>
                                      _changeStatus(website, 'Approved'),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                ImprovedActionButton(
                                  icon: Icons.cancel,
                                  color: Colors.red,
                                  tooltip: 'Reject Website',
                                  onPressed: () =>
                                      _changeStatus(website, 'Rejected'),
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                ImprovedActionButton(
                                  icon: Icons.info,
                                  color: AppColors.blue,
                                  tooltip: 'View Details',
                                  onPressed: () => _viewDetails(website),
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 600;
        final isWideScreen = screenWidth > 800;

        final filteredWebsites = _filteredWebsites(_websites);
        final searchedWebsites = _searchedWebsites(filteredWebsites);
        final totalPages = (searchedWebsites.length / _itemsPerPage).ceil();
        final startIndex = (_currentPage - 1) * _itemsPerPage;
        final endIndex =
            (startIndex + _itemsPerPage).clamp(0, searchedWebsites.length);
        final pagedWebsites = searchedWebsites.sublist(startIndex, endIndex);
        final pendingWebsites = _filteredWebsites(_websites)
            .where((w) => w.status == 'Pending')
            .toList();

        return Scaffold(
          backgroundColor: Colors.white,
          body: Container(
            color: Colors.white,
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: const Color.fromRGBO(26, 115, 232, 0.08),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                                spreadRadius: 0,
                              ),
                            ],
                            border: Border.all(
                                color:
                                    const Color.fromRGBO(232, 240, 254, 0.8)),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: const Color.fromRGBO(
                                          232, 240, 254, 1.0),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Icon(
                                      Icons.language,
                                      color: AppColors.blue,
                                      size: 28,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Text(
                                    'Website Management',
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 24 : 32,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  isSmallScreen
                                      ? Tooltip(
                                          message: 'Add Website',
                                          child: IconButton(
                                            icon: const Icon(Icons.add,
                                                color: AppColors.blue),
                                            onPressed: () {
                                              context.go('/add-website');
                                            },
                                          ),
                                        )
                                      : Tooltip(
                                          message: 'Add Website',
                                          child: ElevatedButton.icon(
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: AppColors.blue,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 16),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              elevation: 0,
                                            ),
                                            label: const Text(
                                              'Add Website',
                                              style: TextStyle(
                                                fontFamily: 'Space',
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            icon: const Icon(
                                              color: Colors.white,
                                              Icons.add,
                                              size: 20,
                                            ),
                                            onPressed: () {
                                              context.go('/add-website');
                                            },
                                          ),
                                        ),
                                  Tooltip(
                                    message: 'Help',
                                    child: IconButton(
                                      icon: const Icon(Icons.help_outline,
                                          color: AppColors.grey),
                                      onPressed: () {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                              content: Text(
                                                  'Help documentation coming soon!')),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        _buildAdminSummary(_websites, screenWidth),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            if (pendingWebsites.isNotEmpty)
                              _buildPendingWebsitesTable(pendingWebsites),
                            const SizedBox(height: 24),
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(
                                        26, 115, 232, 0.08),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                    spreadRadius: 0,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8)),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: const Icon(
                                          Icons.filter_list,
                                          color: AppColors.blue,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Filters',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 18 : 20,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.dark,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Stack(
                                    alignment: Alignment.centerRight,
                                    children: [
                                      TextField(
                                        controller: _searchController,
                                        decoration: InputDecoration(
                                          hintText:
                                              'Search by URL, domain, category, publisher...',
                                          hintStyle: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: isSmallScreen ? 14 : 16,
                                            color: AppColors.grey,
                                          ),
                                          fillColor: const Color.fromRGBO(
                                              245, 248, 253, 1.0),
                                          filled: true,
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            borderSide: const BorderSide(
                                              color: Color.fromRGBO(
                                                  232, 240, 254, 1.0),
                                              width: 1,
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            borderSide: const BorderSide(
                                              color: Color.fromRGBO(
                                                  232, 240, 254, 1.0),
                                              width: 1,
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            borderSide: const BorderSide(
                                              color: AppColors.blue,
                                              width: 1,
                                            ),
                                          ),
                                          prefixIcon: const Icon(Icons.search,
                                              color: AppColors.blue),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 12),
                                        ),
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 14 : 16,
                                        ),
                                      ),
                                      if (_isSearchLoading)
                                        const Padding(
                                          padding: EdgeInsets.only(right: 16),
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: AppColors.blue,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(
                                        26, 115, 232, 0.08),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                    spreadRadius: 0,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8)),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: const Icon(
                                          Icons.view_list,
                                          color: AppColors.blue,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Status Filter',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 18 : 20,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.dark,
                                        ),
                                      ),
                                      const Spacer(),
                                      Container(
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              245, 248, 253, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          children: [
                                            Tooltip(
                                              message: 'Export to CSV',
                                              child: IconButton(
                                                icon: const Icon(Icons.download,
                                                    color: AppColors.blue),
                                                onPressed: () => _exportToCsv(
                                                    searchedWebsites),
                                              ),
                                            ),
                                            Tooltip(
                                              message: 'Refresh data',
                                              child: IconButton(
                                                icon: const Icon(Icons.refresh,
                                                    color: AppColors.blue),
                                                onPressed: _subscribeToData,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: [
                                        _buildImprovedTab(
                                          'All',
                                          _selectedStatus == 'All',
                                          Icons.all_inclusive,
                                          () => setState(
                                              () => _selectedStatus = 'All'),
                                        ),
                                        const SizedBox(width: 12),
                                        _buildImprovedTab(
                                          'Pending',
                                          _selectedStatus == 'Pending',
                                          Icons.pending,
                                          () => setState(() =>
                                              _selectedStatus = 'Pending'),
                                          badgeCount: pendingWebsites.length,
                                        ),
                                        const SizedBox(width: 12),
                                        _buildImprovedTab(
                                          'Approved',
                                          _selectedStatus == 'Approved',
                                          Icons.check_circle,
                                          () => setState(() =>
                                              _selectedStatus = 'Approved'),
                                          color: AppColors.green,
                                        ),
                                        const SizedBox(width: 12),
                                        _buildImprovedTab(
                                          'Rejected',
                                          _selectedStatus == 'Rejected',
                                          Icons.cancel,
                                          () => setState(() =>
                                              _selectedStatus = 'Rejected'),
                                          color: AppColors.red,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        if (_selectedWebsiteIds.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 24.0),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppColors.yellow.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${_selectedWebsiteIds.length} website(s) selected',
                                    style: const TextStyle(
                                      fontFamily: 'Space',
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.dark,
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      DropdownButton<String>(
                                        hint: const Text('Change Status'),
                                        items: ['Approved', 'Rejected']
                                            .map((status) => DropdownMenuItem(
                                                  value: status,
                                                  child: Text(status),
                                                ))
                                            .toList(),
                                        onChanged: (value) =>
                                            _bulkAction('status', value),
                                      ),
                                      const SizedBox(width: 8),
                                      ElevatedButton(
                                        onPressed: () =>
                                            _bulkAction('delete', null),
                                        style: ElevatedButton.styleFrom(
                                          foregroundColor: AppColors.light,
                                          backgroundColor: AppColors.red,
                                        ),
                                        child: const Text('Delete Selected'),
                                      ),
                                      const SizedBox(width: 8),
                                      TextButton(
                                        onPressed: () => setState(
                                            () => _selectedWebsiteIds.clear()),
                                        child: const Text('Clear Selection'),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        const SizedBox(height: 16),
                        if (searchedWebsites.isEmpty)
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.web_asset_off,
                                    size: isSmallScreen ? 48 : 64,
                                    color: AppColors.grey),
                                const SizedBox(height: 16),
                                Text(
                                  'No websites found',
                                  style: TextStyle(
                                    fontFamily: 'Space',
                                    fontSize: isSmallScreen ? 16 : 18,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                        else
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Results',
                                style: TextStyle(
                                    fontFamily: 'Space',
                                    color: Colors.black,
                                    fontSize: 24),
                              ),
                              Card(
                                elevation: 1,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16)),
                                child: Scrollbar(
                                  controller: _websitesScrollController,
                                  child: SingleChildScrollView(
                                    controller: _websitesScrollController,
                                    scrollDirection: Axis.horizontal,
                                    child: DataTable(
                                      columnSpacing: isWideScreen
                                          ? 40
                                          : isSmallScreen
                                              ? 16
                                              : 24,
                                      border: TableBorder(
                                        horizontalInside: BorderSide(
                                          color: Colors.grey.withOpacity(0.1),
                                          width: 1,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      headingRowColor:
                                          MaterialStateProperty.all(
                                        const Color(
                                            0xFFE8F0FE), // Light blue header
                                      ),
                                      dataRowColor: MaterialStateProperty
                                          .resolveWith<Color>(
                                        (Set<MaterialState> states) {
                                          if (states.contains(
                                              MaterialState.selected)) {
                                            return AppColors.blue
                                                .withOpacity(0.05);
                                          }
                                          if (states.contains(
                                              MaterialState.hovered)) {
                                            return AppColors.blue
                                                .withOpacity(0.03);
                                          }
                                          // Alternating row colors
                                          final index = pagedWebsites.indexOf(
                                            pagedWebsites.firstWhere(
                                              (w) => states.contains(
                                                      MaterialState.selected)
                                                  ? _selectedWebsiteIds
                                                      .contains(w.websiteId)
                                                  : true,
                                              orElse: () => pagedWebsites.first,
                                            ),
                                          );
                                          return index % 2 == 0
                                              ? Colors.white
                                              : const Color(
                                                  0xFFF8FAFD); // Very light blue for alternate rows
                                        },
                                      ),
                                      headingRowHeight: 56,
                                      dataRowMinHeight: 48,
                                      dataRowMaxHeight: 56,
                                      columns: [
                                        DataColumn(
                                          label: Checkbox(
                                            value: _selectedWebsiteIds.length ==
                                                searchedWebsites.length,
                                            onChanged: (value) {
                                              setState(() {
                                                if (value == true) {
                                                  _selectedWebsiteIds =
                                                      searchedWebsites
                                                          .map((w) =>
                                                              w.websiteId!)
                                                          .toList();
                                                } else {
                                                  _selectedWebsiteIds.clear();
                                                }
                                              });
                                            },
                                          ),
                                        ),
                                        DataColumn(
                                          label: Text('Orders',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Website ID',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('URL',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Domain',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Language',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('User ID',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Sponsored',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Categories',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('DA',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('DR',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Traffic',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Pricing',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Base Pricing',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Special Topics Price',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Special Pricing',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Backlink Type',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Allowed Topics',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Disallowed Topics',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Word Count Min',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Word Count Max',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Max Links',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Status',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Submission Date',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Created At',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Last Updated',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Active',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Content Type',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Contact Email',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Notes',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Country',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Spam Score',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                        DataColumn(
                                          label: Text('Actions',
                                              style:
                                                  _headerStyle(isSmallScreen)),
                                        ),
                                      ],
                                      rows: pagedWebsites
                                          .map((website) => DataRow(
                                                selected: _selectedWebsiteIds
                                                    .contains(
                                                        website.websiteId),
                                                onSelectChanged: (selected) {
                                                  setState(() {
                                                    if (selected == true) {
                                                      _selectedWebsiteIds.add(
                                                          website.websiteId!);
                                                    } else {
                                                      _selectedWebsiteIds
                                                          .remove(website
                                                              .websiteId);
                                                    }
                                                  });
                                                },
                                                cells: [
                                                  DataCell(
                                                    Checkbox(
                                                      value: _selectedWebsiteIds
                                                          .contains(website
                                                              .websiteId),
                                                      onChanged: (value) {
                                                        setState(() {
                                                          if (value == true) {
                                                            _selectedWebsiteIds
                                                                .add(website
                                                                    .websiteId!);
                                                          } else {
                                                            _selectedWebsiteIds
                                                                .remove(website
                                                                    .websiteId);
                                                          }
                                                        });
                                                      },
                                                    ),
                                                  ),
                                                  DataCell(Text(
                                                    (_orderCounts[website
                                                                .websiteId] ??
                                                            0)
                                                        .toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.websiteId ?? 'N/A',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  )),
                                                  DataCell(
                                                    Tooltip(
                                                      message: website.url,
                                                      child: Text(
                                                        website.url.length > 20
                                                            ? '${website.url.substring(0, 17)}...'
                                                            : website.url,
                                                        style: TextStyle(
                                                          fontFamily: 'Space',
                                                          fontSize: 14,
                                                          color: AppColors.blue,
                                                          decoration:
                                                              TextDecoration
                                                                  .underline,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(Text(
                                                    website.domainName,
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.language ?? 'N/A',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.publisherId ??
                                                        'N/A',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 8,
                                                          vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: website
                                                                .isSponsored
                                                            ? const Color(
                                                                0xFFE0F2FE) // Light blue background
                                                            : const Color(
                                                                0xFFF3F4F6), // Light gray background
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color: website
                                                                  .isSponsored
                                                              ? const Color(
                                                                  0xFF93C5FD) // Medium blue border
                                                              : const Color(
                                                                  0xFFD1D5DB), // Medium gray border
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        website.isSponsored
                                                            ? 'Yes'
                                                            : 'No',
                                                        style: TextStyle(
                                                          fontFamily: 'Space',
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: website
                                                                  .isSponsored
                                                              ? AppColors.blue
                                                              : AppColors.grey,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(Text(
                                                    website.categories
                                                        .join(', '),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                  )),
                                                  DataCell(Text(
                                                    website.da.toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.dr.toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.traffic.toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    '\$${website.pricing.toStringAsFixed(2)}',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    '\$${website.basePricing.toStringAsFixed(2)}',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    '\$${website.specialTopicsAdditionalPrice.toStringAsFixed(2)}',
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 8,
                                                          vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: website
                                                                .hasSpecialTopicsPricing
                                                            ? const Color(
                                                                0xFFE0F2FE) // Light blue background
                                                            : const Color(
                                                                0xFFF3F4F6), // Light gray background
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color: website
                                                                  .hasSpecialTopicsPricing
                                                              ? const Color(
                                                                  0xFF93C5FD) // Medium blue border
                                                              : const Color(
                                                                  0xFFD1D5DB), // Medium gray border
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        website.hasSpecialTopicsPricing
                                                            ? 'Yes'
                                                            : 'No',
                                                        style: TextStyle(
                                                          fontFamily: 'Space',
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: website
                                                                  .hasSpecialTopicsPricing
                                                              ? AppColors.blue
                                                              : AppColors.grey,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(Text(
                                                    website.backlinkType,
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.allowedTopics
                                                        .join(', '),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                  )),
                                                  DataCell(Text(
                                                    website.disallowedTopics
                                                        .join(', '),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                  )),
                                                  DataCell(Text(
                                                    website.wordCountMin
                                                        .toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.wordCountMax
                                                        .toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.maxLinks.toString(),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  )),
                                                  DataCell(_buildStatusBadge(
                                                      website.status)),
                                                  DataCell(Text(
                                                    _formatDate(
                                                        website.submissionDate),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontStyle:
                                                          FontStyle.italic,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    _formatDate(
                                                        website.createdAt),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontStyle:
                                                          FontStyle.italic,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    _formatDate(
                                                        website.lastUpdated),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                      fontStyle:
                                                          FontStyle.italic,
                                                    ),
                                                  )),
                                                  DataCell(
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 8,
                                                          vertical: 4),
                                                      decoration: BoxDecoration(
                                                        color: website.isActive
                                                            ? const Color(
                                                                0xFFDCFCE7) // Light green background
                                                            : const Color(
                                                                0xFFFEE2E2), // Light red background
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color: website
                                                                  .isActive
                                                              ? const Color(
                                                                  0xFF86EFAC) // Medium green border
                                                              : const Color(
                                                                  0xFFFCA5A5), // Medium red border
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Icon(
                                                            website.isActive
                                                                ? Icons
                                                                    .check_circle
                                                                : Icons.cancel,
                                                            size: 12,
                                                            color: website
                                                                    .isActive
                                                                ? Colors.green
                                                                : Colors.red,
                                                          ),
                                                          const SizedBox(
                                                              width: 4),
                                                          Text(
                                                            website.isActive
                                                                ? 'Active'
                                                                : 'Inactive',
                                                            style: TextStyle(
                                                              fontFamily:
                                                                  'Space',
                                                              fontSize: 12,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: website
                                                                      .isActive
                                                                  ? Colors.green
                                                                  : Colors.red,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  DataCell(Text(
                                                    website.contentType
                                                        .join(', '),
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                  )),
                                                  DataCell(Text(
                                                    website.contactEmail,
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(Text(
                                                    website.notes,
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    maxLines: 2,
                                                  )),
                                                  DataCell(Text(
                                                    website.country,
                                                    style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: 14,
                                                      color: AppColors.dark,
                                                    ),
                                                  )),
                                                  DataCell(_buildSpamScoreCell(
                                                      website.spamScore)),
                                                  DataCell(
                                                    Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        ImprovedActionButton(
                                                          icon: Icons.edit,
                                                          color:
                                                              AppColors.green,
                                                          tooltip:
                                                              'Edit Website',
                                                          onPressed: () =>
                                                              _viewDetails(
                                                                  website),
                                                          size: 20,
                                                        ),
                                                        const SizedBox(
                                                            width: 8),
                                                        ImprovedActionButton(
                                                          icon: Icons.delete,
                                                          color: AppColors.red,
                                                          tooltip:
                                                              'Delete Website',
                                                          onPressed: () =>
                                                              _deleteWebsite(
                                                                  website),
                                                          size: 20,
                                                        ),
                                                        const SizedBox(
                                                            width: 8),
                                                        ImprovedActionButton(
                                                          icon: Icons.info,
                                                          color: AppColors.blue,
                                                          tooltip:
                                                              'View Details',
                                                          onPressed: () =>
                                                              _viewDetails(
                                                                  website),
                                                          size: 20,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ))
                                          .toList(),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        const SizedBox(height: 16),
                        if (totalPages > 1)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                icon: Icon(Icons.chevron_left,
                                    color: AppColors.grey),
                                onPressed: _currentPage > 1
                                    ? () => setState(() => _currentPage--)
                                    : null,
                              ),
                              Text(
                                'Page $_currentPage of $totalPages',
                                style: const TextStyle(
                                  fontFamily: 'Space',
                                  color: AppColors.dark,
                                ),
                              ),
                              IconButton(
                                icon: Icon(Icons.chevron_right,
                                    color: AppColors.grey),
                                onPressed: _currentPage < totalPages
                                    ? () => setState(() => _currentPage++)
                                    : null,
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
          ),
        );
      },
    );
  }

  // TextStyle _headerStyle(bool isSmallScreen) {
  //   return TextStyle(
  //     fontFamily: 'Space',
  //     fontSize: isSmallScreen ? 14 : 16,
  //     fontWeight: FontWeight.bold,
  //     color: AppColors.dark,
  //   );
  // }

  void _toggleSort(String field) {
    setState(() {
      if (_sortField == field) {
        _sortAscending = !_sortAscending;
      } else {
        _sortField = field;
        _sortAscending = true;
      }
    });
    _subscribeToData();
  }

  List<DataRow> _buildDataRow(
      WebsiteModel website, bool isSmallScreen, int index) {
    final isExpanded = _expandedRows.contains(website.websiteId);
    final daController = TextEditingController(text: website.da.toString());
    final drController = TextEditingController(text: website.dr.toString());
    final trafficController =
        TextEditingController(text: website.traffic.toString());
    final pricingController =
        TextEditingController(text: website.pricing.toStringAsFixed(2));

    final mainRow = DataRow(
      selected: _selectedWebsiteIds.contains(website.websiteId),
      onSelectChanged: (selected) {
        setState(() {
          if (selected == true) {
            _selectedWebsiteIds.add(website.websiteId!);
          } else {
            _selectedWebsiteIds.remove(website.websiteId);
          }
        });
      },
      cells: [
        DataCell(
          Checkbox(
            value: _selectedWebsiteIds.contains(website.websiteId),
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _selectedWebsiteIds.add(website.websiteId!);
                } else {
                  _selectedWebsiteIds.remove(website.websiteId);
                }
              });
            },
          ),
        ),
        DataCell(
          Text(
            (_orderCounts[website.websiteId] ?? 0).toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: website.url,
            child: Text(
              website.url.length > 20
                  ? '${website.url.substring(0, 17)}...'
                  : website.url,
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
                color: AppColors.blue,
              ),
            ),
          ),
          onTap: () => _viewDetails(website),
        ),
        DataCell(
          Text(
            website.domainName,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            website.da.toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            website.dr.toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Text(
            website.traffic.toString(),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Tooltip(
            message: website.categories.join(', '),
            child: Text(
              website.categories.join(', ').length > 15
                  ? '${website.categories.join(', ').substring(0, 12)}...'
                  : website.categories.join(', '),
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            '\$${website.pricing.toStringAsFixed(2)}',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          DropdownButton<String>(
            value: ['On Hold', 'Pending', 'Approved', 'Rejected']
                    .contains(website.status)
                ? website.status
                : 'Pending',
            items: ['On Hold', 'Pending', 'Approved', 'Rejected']
                .map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(
                        status,
                        style: TextStyle(
                          color: status == 'Approved'
                              ? AppColors.green
                              : status == 'Rejected'
                                  ? AppColors.red
                                  : AppColors.yellow,
                          fontFamily: 'Space',
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ))
                .toList(),
            onChanged: (value) => _changeStatus(website, value!),
          ),
        ),
        DataCell(
          Tooltip(
            message: _publisherDetails[website.publisherId]?['email'] ?? 'N/A',
            child: Text(
              _publisherDetails[website.publisherId]?['name'] ?? 'Unknown',
              style: TextStyle(
                fontFamily: 'Space',
                fontSize: isSmallScreen ? 12 : 14,
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(website.submissionDate),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 12 : 14,
            ),
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Tooltip(
                message: 'View Details',
                child: IconButton(
                  icon: Icon(Icons.visibility,
                      color: AppColors.blue, size: isSmallScreen ? 20 : 24),
                  onPressed: () => _viewDetails(website),
                ),
              ),
              Tooltip(
                message: 'Edit Website',
                child: IconButton(
                  icon: Icon(Icons.edit,
                      color: AppColors.green, size: isSmallScreen ? 20 : 24),
                  onPressed: () => _viewDetails(website),
                ),
              ),
              Tooltip(
                message: 'Delete Website',
                child: IconButton(
                  icon: Icon(Icons.delete,
                      color: AppColors.red, size: isSmallScreen ? 20 : 24),
                  onPressed: () => _deleteWebsite(website),
                ),
              ),
            ],
          ),
        ),
      ],
    );

    return [
      mainRow,
      DataRow(
        cells: [
          DataCell.empty,
          DataCell(
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.light,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildEditableField(
                    label: 'DA',
                    controller: daController,
                    onSubmit: (value) => _editWebsite(website, {
                      'da': int.tryParse(value) ?? website.da,
                    }),
                  ),
                  _buildEditableField(
                    label: 'DR',
                    controller: drController,
                    onSubmit: (value) => _editWebsite(website, {
                      'dr': int.tryParse(value) ?? website.dr,
                    }),
                  ),
                  _buildEditableField(
                    label: 'Traffic',
                    controller: trafficController,
                    onSubmit: (value) => _editWebsite(website, {
                      'traffic': int.tryParse(value) ?? website.traffic,
                    }),
                  ),
                  _buildEditableField(
                    label: 'Pricing',
                    controller: pricingController,
                    onSubmit: (value) => _editWebsite(website, {
                      'pricing': double.tryParse(value) ?? website.pricing,
                    }),
                  ),
                  _buildDetailRow('Language', website.language ?? 'N/A'),
                  _buildDetailRow(
                      'Is Sponsored', website.isSponsored.toString()),
                  _buildDetailRow('Base Pricing',
                      '\$${website.basePricing.toStringAsFixed(2)}'),
                  _buildDetailRow('Special Topics Price',
                      '\$${website.specialTopicsAdditionalPrice.toStringAsFixed(2)}'),
                  _buildDetailRow('Has Special Pricing',
                      website.hasSpecialTopicsPricing.toString()),
                  _buildDetailRow('Backlink Type', website.backlinkType),
                  _buildDetailRow(
                      'Allowed Topics', website.allowedTopics.join(', ')),
                  _buildDetailRow(
                      'Disallowed Topics', website.disallowedTopics.join(', ')),
                  _buildDetailRow(
                      'Word Count Min', website.wordCountMin.toString()),
                  _buildDetailRow(
                      'Word Count Max', website.wordCountMax.toString()),
                  _buildDetailRow('Max Links', website.maxLinks.toString()),
                  _buildDetailRow('Created At', _formatDate(website.createdAt)),
                  _buildDetailRow(
                      'Last Updated', _formatDate(website.lastUpdated)),
                  _buildDetailRow('Is Active', website.isActive.toString()),
                  _buildDetailRow(
                      'Content Type', website.contentType.join(', ')),
                  _buildDetailRow('Contact Email', website.contactEmail),
                  _buildDetailRow('Notes', website.notes),
                  _buildDetailRow('Views Count', website.viewsCount.toString()),
                  _buildDetailRow(
                      'Clicks Count', website.clicksCount.toString()),
                  _buildDetailRow('Country', website.country),
                  if (website.rejectionReason != null)
                    _buildDetailRow(
                        'Rejection Reason', website.rejectionReason!),
                ],
              ),
            ),
          ),
        ],
      ),
    ];
  }

  Widget _buildEditableField({
    required String label,
    required TextEditingController controller,
    required Function(String) onSubmit,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: AppColors.dark,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
              ),
              onSubmitted: onSubmit,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: AppColors.dark,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 14,
                color: AppColors.dark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }
}

class WebsiteDetailsDialog extends StatefulWidget {
  final WebsiteModel website;
  final String publisherName;
  final String publisherEmail;
  final Function(Map<String, dynamic>) onEdit;
  final VoidCallback onDelete;
  final Function(String) onChangeStatus;

  const WebsiteDetailsDialog({
    super.key,
    required this.website,
    required this.publisherName,
    required this.publisherEmail,
    required this.onEdit,
    required this.onDelete,
    required this.onChangeStatus,
  });

  @override
  State<WebsiteDetailsDialog> createState() => _WebsiteDetailsDialogState();
}

class _WebsiteDetailsDialogState extends State<WebsiteDetailsDialog> {
  int _selectedTab = 0;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isWideScreen = screenWidth > 800;
        final dialogWidth = isWideScreen ? 700.0 : screenWidth * 0.9;

        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: dialogWidth,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, AppColors.light],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.blue,
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.language,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                widget.website.url,
                                style: const TextStyle(
                                  fontFamily: 'Space',
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                DefaultTabController(
                  length: 2,
                  child: Column(
                    children: [
                      TabBar(
                        onTap: (index) => setState(() => _selectedTab = index),
                        labelColor: AppColors.blue,
                        unselectedLabelColor: AppColors.grey,
                        indicatorColor: AppColors.blue,
                        labelStyle: const TextStyle(
                          fontFamily: 'Space',
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                        unselectedLabelStyle: const TextStyle(
                          fontFamily: 'Space',
                          fontWeight: FontWeight.normal,
                          fontSize: 16,
                        ),
                        tabs: const [
                          Tab(text: 'Details'),
                          Tab(text: 'Orders'),
                        ],
                      ),
                      SizedBox(
                        height: _selectedTab == 0 ? 500 : 300,
                        child: _selectedTab == 0
                            ? SingleChildScrollView(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildDetailRow('Website ID',
                                        widget.website.websiteId ?? 'N/A'),
                                    _buildDetailRow('URL', widget.website.url),
                                    _buildDetailRow(
                                        'Domain', widget.website.domainName),
                                    _buildDetailRow('Language',
                                        widget.website.language ?? 'N/A'),
                                    _buildDetailRow(
                                        'Publisher', widget.publisherName),
                                    _buildDetailRow('Publisher Email',
                                        widget.publisherEmail),
                                    _buildDetailRow('Is Sponsored',
                                        widget.website.isSponsored.toString()),
                                    _buildDetailRow('Categories',
                                        widget.website.categories.join(', ')),
                                    _buildDetailRow(
                                        'DA', widget.website.da.toString()),
                                    _buildDetailRow(
                                        'DR', widget.website.dr.toString()),
                                    _buildDetailRow('Traffic',
                                        widget.website.traffic.toString()),
                                    _buildDetailRow('Pricing',
                                        '\$${widget.website.pricing.toStringAsFixed(2)}'),
                                    _buildDetailRow('Base Pricing',
                                        '\$${widget.website.basePricing.toStringAsFixed(2)}'),
                                    _buildDetailRow('Special Topics Price',
                                        '\$${widget.website.specialTopicsAdditionalPrice.toStringAsFixed(2)}'),
                                    _buildDetailRow(
                                        'Has Special Pricing',
                                        widget.website.hasSpecialTopicsPricing
                                            .toString()),
                                    _buildDetailRow('Backlink Type',
                                        widget.website.backlinkType),
                                    _buildDetailRow(
                                        'Allowed Topics',
                                        widget.website.allowedTopics
                                            .join(', ')),
                                    _buildDetailRow(
                                        'Disallowed Topics',
                                        widget.website.disallowedTopics
                                            .join(', ')),
                                    _buildDetailRow('Word Count Min',
                                        widget.website.wordCountMin.toString()),
                                    _buildDetailRow('Word Count Max',
                                        widget.website.wordCountMax.toString()),
                                    _buildDetailRow('Max Links',
                                        widget.website.maxLinks.toString()),
                                    _buildDetailRow(
                                        'Status', widget.website.status),
                                    _buildDetailRow(
                                        'Submission Date',
                                        _formatDate(
                                            widget.website.submissionDate)),
                                    _buildDetailRow('Created At',
                                        _formatDate(widget.website.createdAt)),
                                    _buildDetailRow(
                                        'Last Updated',
                                        _formatDate(
                                            widget.website.lastUpdated)),
                                    _buildDetailRow('Is Active',
                                        widget.website.isActive.toString()),
                                    _buildDetailRow('Content Type',
                                        widget.website.contentType.join(', ')),
                                    _buildDetailRow('Contact Email',
                                        widget.website.contactEmail),
                                    _buildDetailRow(
                                        'Notes', widget.website.notes),
                                    _buildDetailRow('Views Count',
                                        widget.website.viewsCount.toString()),
                                    _buildDetailRow('Clicks Count',
                                        widget.website.clicksCount.toString()),
                                    _buildDetailRow(
                                        'Country', widget.website.country),
                                    if (widget.website.rejectionReason != null)
                                      _buildDetailRow('Rejection Reason',
                                          widget.website.rejectionReason!),
                                  ],
                                ),
                              )
                            : WebsiteOrdersDialog(website: widget.website),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.vertical(
                        bottom: Radius.circular(16)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 5,
                        offset: const Offset(0, -3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color: AppColors.blue.withOpacity(0.3)),
                        ),
                        child: DropdownButton<String>(
                          value: widget.website.status,
                          underline: const SizedBox(),
                          icon: const Icon(Icons.arrow_drop_down,
                              color: AppColors.blue),
                          items: ['Approved', 'Rejected']
                              .map((status) => DropdownMenuItem(
                                    value: status,
                                    child: Text(
                                      status,
                                      style: TextStyle(
                                        fontFamily: 'Space',
                                        color: status == 'Approved'
                                            ? AppColors.green
                                            : AppColors.red,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            widget.onChangeStatus(value!);
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () {
                              _showEditDialog(context, widget.website);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            icon: const Icon(Icons.edit, size: 18),
                            label: const Text(
                              'Edit',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          OutlinedButton.icon(
                            onPressed: () {
                              widget.onDelete();
                              Navigator.pop(context);
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppColors.red,
                              side: const BorderSide(color: AppColors.red),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            icon: const Icon(Icons.delete, size: 18),
                            label: const Text(
                              'Delete',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showEditDialog(BuildContext context, WebsiteModel website) {
    final daController = TextEditingController(text: website.da.toString());
    final drController = TextEditingController(text: website.dr.toString());
    final trafficController =
        TextEditingController(text: website.traffic.toString());
    final pricingController =
        TextEditingController(text: website.pricing.toStringAsFixed(2));
    final basePricingController =
        TextEditingController(text: website.basePricing.toStringAsFixed(2));
    final specialTopicsPriceController = TextEditingController(
        text: website.specialTopicsAdditionalPrice.toStringAsFixed(2));
    final wordCountMinController =
        TextEditingController(text: website.wordCountMin.toString());
    final wordCountMaxController =
        TextEditingController(text: website.wordCountMax.toString());
    final maxLinksController =
        TextEditingController(text: website.maxLinks.toString());
    final categoriesController =
        TextEditingController(text: website.categories.join(', '));
    final allowedTopicsController =
        TextEditingController(text: website.allowedTopics.join(', '));
    final disallowedTopicsController =
        TextEditingController(text: website.disallowedTopics.join(', '));
    final contentTypeController =
        TextEditingController(text: website.contentType.join(', '));
    final contactEmailController =
        TextEditingController(text: website.contactEmail);
    final notesController = TextEditingController(text: website.notes);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        titlePadding: EdgeInsets.zero,
        title: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.blue,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Edit Website - ${website.url}',
                  style: const TextStyle(
                    fontFamily: 'Space',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        contentPadding: const EdgeInsets.all(16),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: daController,
                decoration: InputDecoration(
                  labelText: 'Domain Authority',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: drController,
                decoration: InputDecoration(
                  labelText: 'Domain Rating',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: trafficController,
                decoration: InputDecoration(
                  labelText: 'Monthly Traffic',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: pricingController,
                decoration: InputDecoration(
                  labelText: 'Pricing (USD)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  prefixIcon:
                      const Icon(Icons.attach_money, color: AppColors.blue),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: basePricingController,
                decoration: InputDecoration(
                  labelText: 'Base Pricing (USD)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  prefixIcon:
                      const Icon(Icons.attach_money, color: AppColors.blue),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: specialTopicsPriceController,
                decoration: InputDecoration(
                  labelText: 'Special Topics Price (USD)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  prefixIcon:
                      const Icon(Icons.attach_money, color: AppColors.blue),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: wordCountMinController,
                      decoration: InputDecoration(
                        labelText: 'Word Count Min',
                        labelStyle: const TextStyle(
                          fontFamily: 'Space',
                          color: AppColors.dark,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.blue),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: AppColors.blue, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                      ),
                      style: const TextStyle(
                        fontFamily: 'Space',
                        fontSize: 14,
                        color: AppColors.dark,
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: wordCountMaxController,
                      decoration: InputDecoration(
                        labelText: 'Word Count Max',
                        labelStyle: const TextStyle(
                          fontFamily: 'Space',
                          color: AppColors.dark,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: AppColors.blue),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              const BorderSide(color: AppColors.blue, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                      ),
                      style: const TextStyle(
                        fontFamily: 'Space',
                        fontSize: 14,
                        color: AppColors.dark,
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: maxLinksController,
                decoration: InputDecoration(
                  labelText: 'Max Links',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: categoriesController,
                decoration: InputDecoration(
                  labelText: 'Categories (comma-separated)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: allowedTopicsController,
                decoration: InputDecoration(
                  labelText: 'Allowed Topics (comma-separated)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: disallowedTopicsController,
                decoration: InputDecoration(
                  labelText: 'Disallowed Topics (comma-separated)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: contentTypeController,
                decoration: InputDecoration(
                  labelText: 'Content Type (comma-separated)',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                enabled: false,
                controller: contactEmailController,
                decoration: InputDecoration(
                  labelText: 'Contact Email',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  prefixIcon: const Icon(Icons.email, color: AppColors.grey),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  filled: true,
                  fillColor: Colors.grey.withOpacity(0.1),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'Notes',
                  labelStyle: const TextStyle(
                    fontFamily: 'Space',
                    color: AppColors.dark,
                  ),
                  alignLabelWithHint: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: AppColors.blue),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide:
                        const BorderSide(color: AppColors.blue, width: 2),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 14,
                  color: AppColors.dark,
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actionsPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        actions: [
          OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.grey,
              side: BorderSide(color: AppColors.grey.withOpacity(0.5)),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              widget.onEdit({
                'da': int.tryParse(daController.text) ?? widget.website.da,
                'dr': int.tryParse(drController.text) ?? widget.website.dr,
                'traffic': int.tryParse(trafficController.text) ??
                    widget.website.traffic,
                'pricing': double.tryParse(pricingController.text) ??
                    widget.website.pricing,
                'basePricing': double.tryParse(basePricingController.text) ??
                    widget.website.basePricing,
                'specialTopicsAdditionalPrice':
                    double.tryParse(specialTopicsPriceController.text) ??
                        widget.website.specialTopicsAdditionalPrice,
                'wordCountMin': int.tryParse(wordCountMinController.text) ??
                    widget.website.wordCountMin,
                'wordCountMax': int.tryParse(wordCountMaxController.text) ??
                    widget.website.wordCountMax,
                'maxLinks': int.tryParse(maxLinksController.text) ??
                    widget.website.maxLinks,
                'categories': categoriesController.text
                    .split(',')
                    .map((e) => e.trim())
                    .toList(),
                'allowedTopics': allowedTopicsController.text
                    .split(',')
                    .map((e) => e.trim())
                    .toList(),
                'disallowedTopics': disallowedTopicsController.text
                    .split(',')
                    .map((e) => e.trim())
                    .toList(),
                'contentType': contentTypeController.text
                    .split(',')
                    .map((e) => e.trim())
                    .toList(),
                'contactEmail': contactEmailController.text,
                'notes': notesController.text,
              });
              Navigator.pop(context);
              Navigator.pop(context); // Close both dialogs
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Save Changes',
              style: TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 200,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppColors.dark,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.dark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }
}

class WebsiteOrdersDialog extends StatefulWidget {
  final WebsiteModel website;

  const WebsiteOrdersDialog({super.key, required this.website});

  @override
  State<WebsiteOrdersDialog> createState() => _WebsiteOrdersDialogState();
}

class _WebsiteOrdersDialogState extends State<WebsiteOrdersDialog> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  List<OrderModel> _orders = [];
  bool _isLoading = true;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;

  @override
  void initState() {
    super.initState();
    _subscribeToOrders();
  }

  void _subscribeToOrders() {
    _ordersSubscription?.cancel();
    _ordersSubscription = _firestore
        .collection('orders')
        .where('websiteId', isEqualTo: widget.website.websiteId)
        .snapshots()
        .listen((snapshot) {
      if (mounted) {
        final orders = snapshot.docs
            .map((doc) => OrderModel.fromMap(doc.data()..['orderId'] = doc.id))
            .toList();

        setState(() {
          _orders = orders;
          _isLoading = false;
        });
      }
    }, onError: (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading orders: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _orders.isEmpty
            ? Center(
                child: Text(
                  'No orders found for this website',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 16,
                    color: AppColors.grey,
                  ),
                ),
              )
            : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: DataTable(
                    columnSpacing: 24,
                    border: TableBorder(
                      horizontalInside: BorderSide(
                        color: Colors.grey.withOpacity(0.1),
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    headingRowColor: MaterialStateProperty.all(
                      const Color(0xFFE8F0FE), // Light blue header
                    ),
                    dataRowColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                        if (states.contains(MaterialState.selected)) {
                          return AppColors.blue.withOpacity(0.05);
                        }
                        if (states.contains(MaterialState.hovered)) {
                          return AppColors.blue.withOpacity(0.03);
                        }
                        // Alternating row colors
                        final index = _orders.indexOf(
                          _orders.firstWhere(
                            (o) => states.contains(MaterialState.selected)
                                ? o.orderId ==
                                    _orders[_orders.indexWhere((element) =>
                                            states.contains(
                                                MaterialState.selected))]
                                        .orderId
                                : true,
                            orElse: () => _orders.first,
                          ),
                        );
                        return index % 2 == 0
                            ? Colors.white
                            : const Color(
                                0xFFF8FAFD); // Very light blue for alternate rows
                      },
                    ),
                    headingRowHeight: 56,
                    dataRowMinHeight: 48,
                    dataRowMaxHeight: 56,
                    columns: [
                      DataColumn(
                          label: Text('Order ID',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.dark,
                              ))),
                      DataColumn(
                          label: Text('Buyer',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.dark,
                              ))),
                      DataColumn(
                          label: Text('Status',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.dark,
                              ))),
                      DataColumn(
                          label: Text('Created At',
                              style: TextStyle(
                                fontFamily: 'Space',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.dark,
                              ))),
                    ],
                    rows: _orders.map((order) {
                      return DataRow(cells: [
                        DataCell(Text(
                          order.orderId ?? 'N/A',
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            color: AppColors.dark,
                          ),
                        )),
                        DataCell(Text(
                          order.buyerId.isEmpty ? 'Unknown' : order.buyerId,
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            color: AppColors.dark,
                          ),
                        )),
                        DataCell(_buildStatusBadge(order.status)),
                        DataCell(Text(
                          _formatDate(order.orderDate),
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            color: AppColors.dark,
                            fontStyle: FontStyle.italic,
                          ),
                        )),
                      ]);
                    }).toList(),
                  ),
                ),
              );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }

  Widget _buildStatusBadge(String status) {
    IconData icon;
    Color color;

    switch (status) {
      case 'Completed':
        icon = Icons.check_circle;
        color = Colors.green;
        break;
      case 'Cancelled':
        icon = Icons.cancel;
        color = Colors.red;
        break;
      case 'In Progress':
        icon = Icons.pending;
        color = AppColors.blue;
        break;
      case 'Pending':
        icon = Icons.schedule;
        color = AppColors.yellow;
        break;
      default:
        icon = Icons.help;
        color = AppColors.grey;
    }

    return ImprovedStatusBadge(
      text: status,
      color: color,
      icon: icon,
    );
  }
}
