import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

class SimpleOrderDialog extends StatelessWidget {
  final OrderModel order;
  final WebsiteModel? website;
  final String buyerName;
  final String buyerEmail;
  final UserModel? publisher;

  const SimpleOrderDialog({
    super.key,
    required this.order,
    this.website,
    required this.buyerName,
    required this.buyerEmail,
    this.publisher,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(26, 115, 232, 0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color.fromRGBO(232, 240, 254, 1.0),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(Icons.receipt, color: AppColors.blue),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      'Order Details - ${order.orderId ?? 'N/A'}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('Website', website?.url ?? 'N/A'),
                  _buildDetailRow('Buyer', '$buyerName ($buyerEmail)'),
                  _buildDetailRow('Status', order.status),
                  if (order.statusDetail != null)
                    _buildDetailRow('Status Detail', order.statusDetail!),
                  _buildDetailRow('Order Date', _formatDate(order.orderDate)),
                  if (order.inProgressDate != null)
                    _buildDetailRow(
                        'In Progress Date', _formatDate(order.inProgressDate!)),
                  if (order.completionDate != null)
                    _buildDetailRow(
                        'Completion Date', _formatDate(order.completionDate!)),
                  _buildDetailRow('Total Price',
                      '\$${order.totalPrice.toStringAsFixed(2)}'),
                  if (order.actionBy != null)
                    _buildDetailRow('Last Action By', order.actionBy!),
                  if (order.actionTimestamp != null)
                    _buildDetailRow('Last Action Date',
                        _formatDate(order.actionTimestamp!)),
                ],
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                    top: BorderSide(color: Color.fromRGBO(232, 240, 254, 1.0))),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }
}

void showSimpleOrderDialog(
  BuildContext context, {
  required OrderModel order,
  WebsiteModel? website,
  required String buyerName,
  required String buyerEmail,
  UserModel? publisher,
}) {
  showDialog(
    context: context,
    builder: (context) => SimpleOrderDialog(
      order: order,
      website: website,
      buyerName: buyerName,
      buyerEmail: buyerEmail,
      publisher: publisher,
    ),
  );
}
