import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class ImprovedDataTable extends StatelessWidget {
  final List<ImprovedDataColumn> columns;
  final List<ImprovedDataRow> rows;
  final double columnSpacing;
  final Color headerColor;
  final Color rowColor;
  final Color alternateRowColor;
  final double borderRadius;
  final ScrollController? scrollController;

  const ImprovedDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.columnSpacing = 30,
    this.headerColor = const Color(0xFFE8F0FE),
    this.rowColor = Colors.white,
    this.alternateRowColor = const Color(0xFFF5F8FD),
    this.borderRadius = 12,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: const Color.fromRGBO(26, 115, 232, 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: const Color.fromRGBO(232, 240, 254, 0.8)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Container(
                color: headerColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  children: columns.map((column) {
                    return Container(
                      width: column.width,
                      padding: EdgeInsets.only(
                        left: columns.indexOf(column) == 0 ? 16 : 0,
                        right: columnSpacing,
                      ),
                      child: column.label,
                    );
                  }).toList(),
                ),
              ),
              
              // Data rows
              ...List.generate(rows.length, (index) {
                final row = rows[index];
                final isAlternate = index % 2 == 1;
                
                return Material(
                  color: isAlternate ? alternateRowColor : rowColor,
                  child: InkWell(
                    onTap: row.onTap,
                    hoverColor: AppColors.blue.withAlpha(10),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        children: List.generate(row.cells.length, (cellIndex) {
                          final cell = row.cells[cellIndex];
                          return Container(
                            width: columns[cellIndex].width,
                            padding: EdgeInsets.only(
                              left: cellIndex == 0 ? 16 : 0,
                              right: columnSpacing,
                            ),
                            child: cell,
                          );
                        }),
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
}

class ImprovedDataColumn {
  final Widget label;
  final double width;
  
  ImprovedDataColumn({
    required this.label,
    this.width = 150,
  });
}

class ImprovedDataRow {
  final List<Widget> cells;
  final VoidCallback? onTap;
  
  ImprovedDataRow({
    required this.cells,
    this.onTap,
  });
}

class ImprovedActionButton extends StatelessWidget {
  final IconData icon;
  final Color color;
  final String tooltip;
  final VoidCallback onPressed;
  final double size;
  
  const ImprovedActionButton({
    super.key,
    required this.icon,
    required this.color,
    required this.tooltip,
    required this.onPressed,
    this.size = 20,
  });
  
  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withAlpha(20),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: onPressed,
            hoverColor: color.withAlpha(30),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                icon,
                color: color,
                size: size,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ImprovedStatusBadge extends StatelessWidget {
  final String text;
  final Color color;
  final IconData? icon;
  
  const ImprovedStatusBadge({
    super.key,
    required this.text,
    required this.color,
    this.icon,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withAlpha(50), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
