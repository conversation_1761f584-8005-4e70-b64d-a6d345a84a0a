import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

class PoliciesManagementPage extends StatefulWidget {
  const PoliciesManagementPage({super.key});

  @override
  State<PoliciesManagementPage> createState() => _PoliciesManagementPageState();
}

class _PoliciesManagementPageState extends State<PoliciesManagementPage>
    with SingleTickerProviderStateMixin {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = true;
  late TabController _tabController;

  // Text controllers for each policy
  late TextEditingController _termsController;
  late TextEditingController _privacyController;
  late TextEditingController _refundController;
  late TextEditingController _publisherTermsController;

  // Track if policies have been modified
  bool _termsModified = false;
  bool _privacyModified = false;
  bool _refundModified = false;
  bool _publisherTermsModified = false;

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    _termsController = TextEditingController();
    _privacyController = TextEditingController();
    _refundController = TextEditingController();
    _publisherTermsController = TextEditingController();

    // Initialize tab controller
    _tabController = TabController(length: 4, vsync: this);

    // Load policies
    _loadPolicies();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _termsController.dispose();
    _privacyController.dispose();
    _refundController.dispose();
    _publisherTermsController.dispose();
    super.dispose();
  }

  Future<void> _loadPolicies() async {
    setState(() => _isLoading = true);

    try {
      // Load Terms & Conditions
      final termsDoc =
          await _firestore.collection('policies').doc('terms').get();
      if (termsDoc.exists && termsDoc.data() != null) {
        final data = termsDoc.data()!;
        if (data.containsKey('content')) {
          _termsController.text = data['content'] as String;
        }
      }

      // Load Privacy Policy
      final privacyDoc =
          await _firestore.collection('policies').doc('privacy').get();
      if (privacyDoc.exists && privacyDoc.data() != null) {
        final data = privacyDoc.data()!;
        if (data.containsKey('content')) {
          _privacyController.text = data['content'] as String;
        }
      }

      // Load Refund Policy
      final refundDoc =
          await _firestore.collection('policies').doc('refund').get();
      if (refundDoc.exists && refundDoc.data() != null) {
        final data = refundDoc.data()!;
        if (data.containsKey('content')) {
          _refundController.text = data['content'] as String;
        }
      }

      // Load Publisher Terms
      final publisherTermsDoc =
          await _firestore.collection('policies').doc('publisher_terms').get();
      if (publisherTermsDoc.exists && publisherTermsDoc.data() != null) {
        final data = publisherTermsDoc.data()!;
        if (data.containsKey('content')) {
          _publisherTermsController.text = data['content'] as String;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading policies: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _savePolicy(String policyType) async {
    setState(() => _isLoading = true);

    try {
      String content;
      switch (policyType) {
        case 'terms':
          content = _termsController.text;
          break;
        case 'privacy':
          content = _privacyController.text;
          break;
        case 'refund':
          content = _refundController.text;
          break;
        case 'publisher_terms':
          content = _publisherTermsController.text;
          break;
        default:
          throw Exception('Invalid policy type');
      }

      await _firestore.collection('policies').doc(policyType).set({
        'content': content,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_getPolicyTitle(policyType)} saved successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Reset modified flag
        setState(() {
          switch (policyType) {
            case 'terms':
              _termsModified = false;
              break;
            case 'privacy':
              _privacyModified = false;
              break;
            case 'refund':
              _refundModified = false;
              break;
            case 'publisher_terms':
              _publisherTermsModified = false;
              break;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving policy: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getPolicyTitle(String policyType) {
    switch (policyType) {
      case 'terms':
        return 'Terms & Conditions';
      case 'privacy':
        return 'Privacy Policy';
      case 'refund':
        return 'Refund Policy';
      case 'publisher_terms':
        return 'Publisher Terms';
      default:
        return 'Policy';
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Scaffold(
      backgroundColor: AppColors.componentBackColor,
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.blue,
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading policies...',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: 16,
                      color: AppColors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromRGBO(
                        248, 249, 252, 1.0), // AppColors.componentBackColor
                    Colors.white,
                  ],
                  stops: [0.0, 0.8],
                ),
              ),
              child: Column(
                children: [
                  _buildHeader(isSmallScreen),
                  const SizedBox(height: 16),
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildPolicyEditor(
                          policyType: 'terms',
                          controller: _termsController,
                          isModified: _termsModified,
                          onContentChanged: () {
                            setState(() => _termsModified = true);
                          },
                        ),
                        _buildPolicyEditor(
                          policyType: 'privacy',
                          controller: _privacyController,
                          isModified: _privacyModified,
                          onContentChanged: () {
                            setState(() => _privacyModified = true);
                          },
                        ),
                        _buildPolicyEditor(
                          policyType: 'refund',
                          controller: _refundController,
                          isModified: _refundModified,
                          onContentChanged: () {
                            setState(() => _refundModified = true);
                          },
                        ),
                        _buildPolicyEditor(
                          policyType: 'publisher_terms',
                          controller: _publisherTermsController,
                          isModified: _publisherTermsModified,
                          onContentChanged: () {
                            setState(() => _publisherTermsModified = true);
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildHeader(bool isSmallScreen) {
    return Container(
      margin: const EdgeInsets.all(24.0),
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Color.fromRGBO(232, 240, 254, 0.3), // AppColors.blueLightest
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(232, 240, 254, 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.policy,
                  color: AppColors.blue,
                  size: isSmallScreen ? 24 : 28,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Policies Management',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: isSmallScreen ? 24 : 32,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Color.fromRGBO(232, 240, 254, 0.5),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 1,
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        tabs: [
          _buildTab('Terms & Conditions', Icons.description),
          _buildTab('Privacy Policy', Icons.privacy_tip),
          _buildTab('Refund Policy', Icons.monetization_on),
          _buildTab('Publisher Terms', Icons.people),
        ],
        labelColor: AppColors.blue,
        unselectedLabelColor: Colors.grey,
        labelStyle: const TextStyle(
          fontFamily: 'Space',
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontFamily: 'Space',
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicator: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(26, 115, 232, 0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        dividerColor: Colors.transparent,
        indicatorPadding: const EdgeInsets.all(4),
      ),
    );
  }

  Widget _buildTab(String text, IconData icon) {
    return Tab(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 8),
          Text(text),
        ],
      ),
    );
  }

  Widget _buildPolicyEditor({
    required String policyType,
    required TextEditingController controller,
    required bool isModified,
    required VoidCallback onContentChanged,
  }) {
    return Container(
      margin: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(26, 115, 232, 0.04),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 1,
          ),
        ],
      ),
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Color.fromRGBO(232, 240, 254, 0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getPolicyIcon(policyType),
                      color: AppColors.blue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    _getPolicyTitle(policyType),
                    style: const TextStyle(
                      fontFamily: 'Space',
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.dark,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: isModified ? () => _savePolicy(policyType) : null,
                icon: const Icon(Icons.save),
                label: const Text('Save Changes'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.blue,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                  disabledBackgroundColor: Color.fromRGBO(232, 240, 254, 0.5),
                  disabledForegroundColor: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Color.fromRGBO(232, 240, 254, 1.0)),
                borderRadius: BorderRadius.circular(16),
                color: Color.fromRGBO(248, 249, 252, 0.5),
              ),
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: controller,
                onChanged: (value) => onContentChanged(),
                maxLines: null,
                expands: true,
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 15,
                  color: AppColors.dark,
                ),
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: 'Enter markdown content here...',
                  hintStyle: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 15,
                    color: Colors.grey[400],
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(232, 240, 254, 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.preview,
                  color: AppColors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Preview',
                style: TextStyle(
                  fontFamily: 'Space',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              border: Border.all(color: Color.fromRGBO(232, 240, 254, 1.0)),
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(26, 115, 232, 0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Markdown(
              data: controller.text,
              selectable: true,
              styleSheet: MarkdownStyleSheet(
                p: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 15,
                  color: AppColors.dark,
                ),
                h1: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
                h2: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
                h3: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.dark,
                ),
                listBullet: const TextStyle(
                  fontFamily: 'Space',
                  fontSize: 15,
                  color: AppColors.blue,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPolicyIcon(String policyType) {
    switch (policyType) {
      case 'terms':
        return Icons.description;
      case 'privacy':
        return Icons.privacy_tip;
      case 'refund':
        return Icons.monetization_on;
      case 'publisher_terms':
        return Icons.people;
      default:
        return Icons.policy;
    }
  }
}
