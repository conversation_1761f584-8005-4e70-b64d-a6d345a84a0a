// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDK9KUSBWvoEtz1WGmFp4aEbF0IjFCA_kw',
    appId: '1:180868556261:web:54758fe0ff94f9e384bb0c',
    messagingSenderId: '180868556261',
    projectId: 'guestposts-84915',
    authDomain: 'guestposts-84915.firebaseapp.com',
    storageBucket: 'guestposts-84915.firebasestorage.app',
    measurementId: 'G-2XTFN44EP8',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD1U8YVx4v6hJ9SDknHn3wO1VF5uX6cedQ',
    appId: '1:180868556261:android:771c5e03bacd82dc84bb0c',
    messagingSenderId: '180868556261',
    projectId: 'guestposts-84915',
    storageBucket: 'guestposts-84915.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC8H1NgMdchZPe31H5dyRMuT71UGTbKoSc',
    appId: '1:180868556261:ios:200558341244b29d84bb0c',
    messagingSenderId: '180868556261',
    projectId: 'guestposts-84915',
    storageBucket: 'guestposts-84915.firebasestorage.app',
    iosBundleId: 'com.example.guestPosts',
  );
}
