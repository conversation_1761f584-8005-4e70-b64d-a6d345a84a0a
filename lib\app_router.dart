import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:guest_posts_buyer/core/services/auth_service.dart';
import 'package:guest_posts_buyer/features/auth/login_screen.dart';
import 'package:guest_posts_buyer/features/auth/register_screen.dart';
import 'package:guest_posts_buyer/features/landing/landing_page.dart';
import 'package:guest_posts_buyer/features/publisher_acc/home/<USER>';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/auth',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authService = AuthService();
      final isAuthenticated = authService.isLoggedIn;

      final isPublicRoute = state.uri.toString().startsWith('/auth') ||
          state.uri.toString().startsWith('/auth/register');

      if (isAuthenticated && isPublicRoute) {
        return '/dashboard'; // Ensure this matches the route structure
      }
      if (!isAuthenticated && !isPublicRoute) {
        return '/auth';
      }
      return null; // No redirect needed
    },
    routes: [
      GoRoute(
        path: '/',
        redirect: (context, state) => '/auth',
      ),
      GoRoute(
        path: '/auth',
        builder: (context, state) => const LoginScreen(),
        routes: [
          GoRoute(
            path: 'register',
            builder: (context, state) => SignUpScreen(
              authService: AuthService(),
            ), // Add const if possible
          ),
        ],
      ),

        GoRoute(
        path: '/landing-preview',
        builder: (context, state) => const LandingPage(),
        routes: [
          GoRoute(
            path: 'register',
            builder: (context, state) => SignUpScreen(
              authService: AuthService(),
            ), // Add const if possible
          ),
        ],
      ),

      // GoRoute(
      //   path:
      //       '/:page/:arg?', // Dynamic route for pages like /dashboard, /profile, etc.
      //   builder: (context, state) {
      //     final page = state.pathParameters['page'] ?? 'dashboard';
      //     final arg = state.uri.queryParameters['arg'] ?? '';
      //     return HomeScreen(
      //       initialPage: page,
      //       arg: arg,
      //     );
      //   },
      // ),
      GoRoute(
        path:
            '/:page', // Dynamic route for pages like /dashboard, /profile, etc.
        builder: (context, state) {
          final page = state.pathParameters['page'] ?? 'dashboard';
          final arg = state.uri.queryParameters['id'] ?? '';

          return BaseScreen(
            initialPage: page,
            arg: arg,
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Route not found: ${state.uri}'),
      ),
    ), // Add error handling for debugging
  );
}
