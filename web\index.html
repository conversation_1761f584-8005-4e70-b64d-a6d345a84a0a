<!DOCTYPE html>
<html>

<head>

  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="guest_posts">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>guest_posts</title>
  <link rel="manifest" href="manifest.json">
</head>

<body>
  <meta name="google-signin-client_id"
    content="180868556261-s4uikfc9qsaihip5crc33d368lvkdgm0.apps.googleusercontent.com">

  <script src="flutter_bootstrap.js" async></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js"></script>
  <script>
    var firebaseConfig = {
      apiKey: 'AIzaSyDK9KUSBWvoEtz1WGmFp4aEbF0IjFCA_kw',
      appId: '1:180868556261:web:54758fe0ff94f9e384bb0c',
      messagingSenderId: '180868556261',
      projectId: 'guestposts-84915',
      authDomain: 'guestposts-84915.firebaseapp.com',
      storageBucket: 'guestposts-84915.firebasestorage.app',
      measurementId: 'G-2XTFN44EP8',

    };

    firebase.initializeApp(firebaseConfig);
  </script>

</body>

</html>