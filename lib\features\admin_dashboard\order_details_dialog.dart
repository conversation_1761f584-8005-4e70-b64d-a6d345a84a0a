import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:intl/intl.dart';

class OrderDetailsDialog extends StatefulWidget {
  final OrderModel order;
  final WebsiteModel? website;
  final String buyerName;
  final String buyerEmail;
  final UserModel? publisher;
  final Function(String) onChangeStatus;
  final Function(String?) onAssignPublisher;
  final Function(bool, String?, String?) onUpdateDispute;
  final FirestoreService firestoreService;

  const OrderDetailsDialog({
    super.key,
    required this.order,
    this.website,
    required this.buyerName,
    required this.buyerEmail,
    this.publisher,
    required this.onChangeStatus,
    required this.onAssignPublisher,
    required this.onUpdateDispute,
    required this.firestoreService,
  });

  @override
  State<OrderDetailsDialog> createState() => _OrderDetailsDialogState();
}

class _OrderDetailsDialogState extends State<OrderDetailsDialog> {
  int _selectedTab = 0;

  Future<List<Map<String, dynamic>>> _fetchAuditLogs(String orderId) async {
    try {
      final logs = await widget.firestoreService.getSubCollection(
        collectionPath: 'orders',
        documentId: orderId,
        subCollectionPath: 'audit_logs',
        queryBuilder: (query) => query.orderBy('timestamp', descending: true),
      );
      return logs;
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error fetching audit logs: $e'),
          backgroundColor: Colors.red,
        ),
      );
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isWideScreen = screenWidth > 800;
        final dialogWidth = isWideScreen ? 700.0 : screenWidth * 0.9;

        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: dialogWidth,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, AppColors.light],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.blue, AppColors.blue.withOpacity(0.8)],
                    ),
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(16)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'Order #${widget.order.orderId ?? "N/A"}',
                          style: const TextStyle(
                            fontFamily: 'Space',
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                DefaultTabController(
                  length: 2,
                  child: Column(
                    children: [
                      TabBar(
                        onTap: (index) => setState(() => _selectedTab = index),
                        labelColor: AppColors.dark,
                        unselectedLabelColor: AppColors.grey,
                        indicatorColor: AppColors.blue,
                        tabs: const [
                          Tab(text: 'Details'),
                          Tab(text: 'Audit Logs'),
                        ],
                      ),
                      SizedBox(
                        height: _selectedTab == 0 ? 400 : 300,
                        child: _selectedTab == 0
                            ? SingleChildScrollView(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildDetailRow('Order ID',
                                        widget.order.orderId ?? 'N/A'),
                                    _buildDetailRow('Website URL',
                                        widget.website?.url ?? 'N/A'),
                                    _buildDetailRow(
                                        'Buyer Name', widget.buyerName),
                                    _buildDetailRow(
                                        'Buyer Email', widget.buyerEmail),
                                    _buildDetailRow('Publisher Name',
                                        widget.publisher?.name ?? 'N/A'),
                                    _buildDetailRow('Publisher Email',
                                        widget.publisher?.email ?? 'N/A'),
                                    _buildDetailRow('Total Price',
                                        '\$${widget.order.totalPrice.toStringAsFixed(2)}'),
                                    _buildDetailRow(
                                        'Status', widget.order.status),
                                    _buildDetailRow('Dispute Status',
                                        widget.order.disputeStatus ?? 'None'),
                                    _buildDetailRow('Order Date',
                                        _formatDate(widget.order.orderDate)),
                                    _buildDetailRow('Last Updated',
                                        _formatDate(widget.order.lastUpdated)),
                                    _buildDetailRow(
                                        'Is Disputed',
                                        (widget.order.isDisputed ?? false)
                                            .toString()),
                                    if (widget.order.disputeNote != null)
                                      _buildDetailRow('Dispute Note',
                                          widget.order.disputeNote!),
                                  ],
                                ),
                              )
                            : FutureBuilder<List<Map<String, dynamic>>>(
                                future: _fetchAuditLogs(widget.order.orderId!),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return const Center(
                                        child: CircularProgressIndicator());
                                  }
                                  if (snapshot.hasError) {
                                    return Center(
                                        child:
                                            Text('Error: ${snapshot.error}'));
                                  }
                                  final logs = snapshot.data ?? [];
                                  if (logs.isEmpty) {
                                    return const Center(
                                        child: Text('No audit logs found'));
                                  }
                                  return SingleChildScrollView(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      children: logs
                                          .map((log) => ListTile(
                                                title: Text(
                                                  log['action'] ?? 'N/A',
                                                  style: const TextStyle(
                                                    fontFamily: 'Space',
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                subtitle: Text(
                                                  log['details'] ??
                                                      'No details',
                                                  style: const TextStyle(
                                                    fontFamily: 'Space',
                                                  ),
                                                ),
                                                trailing: Text(
                                                  _formatDate(log['timestamp']
                                                      as Timestamp?),
                                                  style: const TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ))
                                          .toList(),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      DropdownButton<String>(
                        value: widget.order.status,
                        items:
                            ['Pending', 'In Progress', 'Completed', 'Cancelled']
                                .map((status) => DropdownMenuItem(
                                      value: status,
                                      child: Text(status),
                                    ))
                                .toList(),
                        onChanged: (value) {
                          widget.onChangeStatus(value!);
                        },
                      ),
                      const SizedBox(width: 8),
                      FutureBuilder<List<Map<String, dynamic>>>(
                        future: widget.firestoreService.getCollection(
                          collectionPath: 'users',
                          queryBuilder: (query) =>
                              query.where('isPublisher', isEqualTo: true),
                        ),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            );
                          }
                          if (snapshot.hasError) {
                            debugPrint(
                                'Error loading publishers: ${snapshot.error}');
                            return const Text('Error loading publishers');
                          }

                          // Map publishers, ensuring correct UID
                          final publishers = snapshot.data
                                  ?.map((data) {
                                    // Ensure 'uid' is the Firestore document ID if not in data
                                    final uid = data['uid'] ?? data['id'] ?? '';
                                    if (uid.isEmpty) {
                                      debugPrint(
                                          'Missing UID in publisher data: $data');
                                      return null;
                                    }
                                    return UserModel.fromMap(data, uid);
                                  })
                                  .where((user) => user != null)
                                  .cast<UserModel>()
                                  .toList() ??
                              [];

                          // Validate publisherId: set to null if not found in publishers
                          String? selectedPublisherId =
                              widget.order.publisherId;
                          if (selectedPublisherId != null &&
                              !publishers
                                  .any((p) => p.uid == selectedPublisherId)) {
                            debugPrint(
                                'Publisher ID $selectedPublisherId not found in publishers list');
                            selectedPublisherId = null;
                          }

                          return DropdownButton<String?>(
                            value: selectedPublisherId,
                            hint: const Text('Assign Publisher'),
                            items: [
                              const DropdownMenuItem(
                                value: null,
                                child: Text('Unassign'),
                              ),
                              ...publishers.map((publisher) => DropdownMenuItem(
                                    value: publisher.uid,
                                    child: Text(publisher.name),
                                  )),
                            ],
                            onChanged: (value) {
                              widget.onAssignPublisher(value);
                            },
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          final noteController = TextEditingController();
                          final disputeStatusController =
                              TextEditingController();
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(widget.order.isDisputed ?? false
                                  ? 'Update Dispute'
                                  : 'Flag Dispute'),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(widget.order.isDisputed ?? false
                                      ? 'Update the dispute for this order?'
                                      : 'Flag this order as disputed?'),
                                  DropdownButtonFormField<String>(
                                    value: widget.order.disputeStatus ?? 'Open',
                                    decoration: const InputDecoration(
                                      labelText: 'Dispute Status',
                                    ),
                                    items: ['Open', 'In Review', 'Resolved']
                                        .map((status) => DropdownMenuItem(
                                              value: status,
                                              child: Text(status),
                                            ))
                                        .toList(),
                                    onChanged: (value) {
                                      disputeStatusController.text = value!;
                                    },
                                  ),
                                  TextField(
                                    controller: noteController,
                                    decoration: const InputDecoration(
                                      labelText: 'Dispute Note (optional)',
                                    ),
                                    maxLines: 3,
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    widget.onUpdateDispute(
                                      disputeStatusController.text !=
                                          'Resolved',
                                      noteController.text.isNotEmpty
                                          ? noteController.text
                                          : null,
                                      disputeStatusController.text.isNotEmpty
                                          ? disputeStatusController.text
                                          : 'Open',
                                    );
                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                      widget.order.isDisputed ?? false
                                          ? 'Update'
                                          : 'Flag',
                                      style: TextStyle(
                                          color:
                                              widget.order.isDisputed ?? false
                                                  ? AppColors.blue
                                                  : AppColors.red)),
                                ),
                              ],
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: widget.order.isDisputed ?? false
                              ? AppColors.blue
                              : AppColors.red,
                        ),
                        child: Text(widget.order.isDisputed ?? false
                            ? 'Update Dispute'
                            : 'Flag Dispute'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 200,
            child: Text(
              label,
              style: const TextStyle(
                fontFamily: 'Space',
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: AppColors.dark,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontFamily: 'Space',
                fontSize: 16,
                color: AppColors.dark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy').format(date);
  }
}
