
  // Widget _buildFilters() {
  //   final categories = _websites.expand((w) => w.categories).toSet().toList()
  //     ..sort()
  //     ..insert(0, 'All');
  //   final countries = _websites.map((w) => w.country).toSet().toList()
  //     ..sort()
  //     ..insert(0, 'All');

  //   return Card(
  //     color: Colors.white,
  //     elevation: 0,
  //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
  //     child: Padding(
  //       padding: const EdgeInsets.all(8),
  //       child: Wrap(
  //         spacing: 10,
  //         runSpacing: 12,
  //         children: [
  //           _buildModernTextField(
  //             controller: _daMinController,
  //             hintText: 'DA Min',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _daMaxController,
  //             hintText: 'DA Max',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _drMinController,
  //             hintText: 'DR Min',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _drMaxController,
  //             hintText: 'DR Max',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _trafficMinController,
  //             hintText: 'Traffic Min',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _trafficMaxController,
  //             hintText: 'Traffic Max',
  //             keyboardType: TextInputType.number,
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _priceMinController,
  //             hintText: 'Price Min',
  //             keyboardType:
  //                 const TextInputType.numberWithOptions(decimal: true),
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernTextField(
  //             controller: _priceMaxController,
  //             hintText: 'Price Max',
  //             keyboardType:
  //                 const TextInputType.numberWithOptions(decimal: true),
  //             onChanged: (_) => setState(() => _currentPage = 1),
  //           ),
  //           _buildModernDropdown(
  //             value: _selectedCategory,
  //             hintText: 'Category',
  //             items: categories,
  //             onChanged: (value) {
  //               setState(() {
  //                 _selectedCategory = value;
  //                 _currentPage = 1;
  //               });
  //             },
  //           ),
  //           _buildModernDropdown(
  //             value: _selectedCountry,
  //             hintText: 'Country',
  //             items: countries,
  //             onChanged: (value) {
  //               setState(() {
  //                 _selectedCountry = value;
  //                 _currentPage = 1;
  //               });
  //             },
  //           ),
  //           ElevatedButton(
  //             onPressed: () {
  //               _daMinController.clear();
  //               _daMaxController.clear();
  //               _drMinController.clear();
  //               _drMaxController.clear();
  //               _trafficMinController.clear();
  //               _trafficMaxController.clear();
  //               _priceMinController.clear();
  //               _priceMaxController.clear();
  //               setState(() {
  //                 _selectedCategory = null;
  //                 _selectedCountry = null;
  //                 _currentPage = 1;
  //               });
  //               _loadWebsites();
  //             },
  //             style: ElevatedButton.styleFrom(
  //               backgroundColor: Colors.amber,
  //               foregroundColor: Colors.black,
  //               padding:
  //                   const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
  //               shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(12),
  //               ),
  //             ),
  //             child: const Text('Clear Filters'),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildModernTextField({
  //   required TextEditingController controller,
  //   required String hintText,
  //   required TextInputType keyboardType,
  //   required Function(String) onChanged,
  // }) {
  //   return SizedBox(
  //     width: 120,
  //     child: TextField(
  //       controller: controller,
  //       keyboardType: keyboardType,
  //       onChanged: onChanged,
  //       decoration: InputDecoration(
  //         hintText: hintText,
  //         filled: true,
  //         fillColor: AppTheme.backgroundColor,
  //         contentPadding:
  //             const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //         border: OutlineInputBorder(
  //           borderRadius: BorderRadius.circular(12),
  //           borderSide: BorderSide.none,
  //         ),
  //       ),
  //       style: const TextStyle(
  //         fontFamily: 'Space',
  //         fontSize: 14,
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildModernDropdown({
  //   required String? value,
  //   required String hintText,
  //   required List<String> items,
  //   required Function(String?) onChanged,
  // }) {
  //   return SizedBox(
  //     width: 200,
  //     child: DropdownButtonFormField<String>(
  //       value: value,
  //       decoration: InputDecoration(
  //         hintText: hintText,
  //         filled: true,
  //         fillColor: AppTheme.backgroundColor,
  //         contentPadding:
  //             const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //         border: OutlineInputBorder(
  //           borderRadius: BorderRadius.circular(12),
  //           borderSide: BorderSide.none,
  //         ),
  //       ),
  //       items: items
  //           .map((item) => DropdownMenuItem(
  //                 value: item,
  //                 child: Text(
  //                   item,
  //                   style: const TextStyle(
  //                     fontFamily: 'Space',
  //                     fontSize: 14,
  //                   ),
  //                 ),
  //               ))
  //           .toList(),
  //       onChanged: onChanged,
  //     ),
  //   );
  // }
