import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/improved_user_details_dialog.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/user_activity_dialog.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/widgets/improved_data_table.dart';
import 'package:intl/intl.dart';
import 'package:csv/csv.dart';
import 'package:universal_html/html.dart' as html;
import 'package:url_launcher/url_launcher.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  String _selectedRole = 'All';
  final TextEditingController _searchController = TextEditingController();
  final _scrollController = ScrollController();
  final _scrollController2 = ScrollController();

  List<UserModel> _users = [];
  List<UserModel> _adminUsers = []; // New list for admin users
  bool _isLoading = false;
  Timer? _debounce;
  String? _adminUid;

  @override
  void initState() {
    super.initState();
    _checkAdminAccess();
    _searchController.addListener(_onSearchChanged);
  }

  Future<void> _checkAdminAccess() async {
    final user = _auth.currentUser;
    if (user == null) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );
      // Uncomment for production
      // if (userData == null || userData['isAdmin'] != true) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(
      //       content: Text('Access denied: Admins only'),
      //       backgroundColor: Colors.red,
      //     ),
      //   );
      //   context.go('/login');
      //   return;
      // }

      setState(() {
        _adminUid = user.uid;
        _isLoading = true;
      });
      await _loadUsers();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking admin access: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {});
    });
  }

  Future<void> _loadUsers() async {
    try {
      setState(() => _isLoading = true);
      final usersData = await _firestoreService.getCollection(
        collectionPath: 'users',
      );
      setState(() {
        final allUsers = usersData
            .map((data) => UserModel.fromMap(data, data['uid']))
            .toList();
        _adminUsers = allUsers.where((user) => user.isAdmin).toList();
        _users = allUsers.where((user) => !user.isAdmin).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading users: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _loadUsers,
          ),
        ),
      );
    }
  }

  List<UserModel> _filteredUsers(List<UserModel> users) {
    switch (_selectedRole) {
      case 'Publisher':
        return users.where((user) => user.isPublisher).toList();
      case 'Advertiser':
        return users.where((user) => !user.isPublisher).toList();
      case 'Blocked':
        return users.where((user) => user.isBlocked).toList();
      case 'All':
      default:
        return users;
    }
  }

  List<UserModel> _searchedUsers(List<UserModel> users) {
    if (_searchController.text.isEmpty) return users;
    final searchTerm = _searchController.text.toLowerCase();
    return users
        .where((user) =>
            user.email.toLowerCase().contains(searchTerm) ||
            user.name.toLowerCase().contains(searchTerm) ||
            user.uid.toLowerCase().contains(searchTerm) ||
            user.mobileNumber.toLowerCase().contains(searchTerm) ||
            (user.additionalDetails?['provider']?.toString().toLowerCase() ??
                    '')
                .contains(searchTerm))
        .toList();
  }

  Future<void> _toggleBlockUser(UserModel user) async {
    try {
      await _firestoreService.updateDocument(
        collectionPath: 'users',
        documentId: user.uid,
        data: {'isBlocked': !user.isBlocked},
      );

      setState(() {
        if (user.isAdmin) {
          final index = _adminUsers.indexWhere((u) => u.uid == user.uid);
          if (index != -1) {
            _adminUsers[index] =
                _adminUsers[index].copyWith(isBlocked: !user.isBlocked);
          }
        } else {
          final index = _users.indexWhere((u) => u.uid == user.uid);
          if (index != -1) {
            _users[index] = _users[index].copyWith(isBlocked: !user.isBlocked);
          }
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(user.isBlocked ? 'User unblocked' : 'User blocked'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _changeUserRole(UserModel user, bool isPublisher) async {
    try {
      await _firestoreService.updateDocument(
        collectionPath: 'users',
        documentId: user.uid,
        data: {'isPublisher': isPublisher},
      );

      setState(() {
        final index = _users.indexWhere((u) => u.uid == user.uid);
        if (index != -1) {
          _users[index] = _users[index].copyWith(isPublisher: isPublisher);
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'User role changed to ${isPublisher ? 'Publisher' : 'Advertiser'}'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error changing role: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteUser(UserModel user) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
            'Are you sure you want to delete ${user.email}? This cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    try {
      await _firestoreService.deleteDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User deleted')),
      );
      await _loadUsers();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting user: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _viewActivityLogs(UserModel user) async {
    showDialog(
      context: context,
      builder: (context) => UserActivityDialog(
        user: user,
        firestoreService: _firestoreService,
      ),
    );
  }

  void _exportToCsv(List<UserModel> users, {bool isAdminTable = false}) {
    final List<List<dynamic>> rows = [
      [
        'UID',
        'Name',
        'Email',
        'Email Verified',
        'Mobile Number',
        'Profile Picture URL',
        'Role',
        'Created At',
        'Last Login',
        'Provider',
        'Main Balance',
        'Reserved Balance',
        'Buyer Funds',
        'Reserved Funds',
        'Status',
        'Is Admin',
      ],
      ...users.map((user) => [
            user.uid,
            user.name,
            user.email,
            user.emailVerified ? 'Yes' : 'No',
            user.mobileNumber.isEmpty ? 'N/A' : user.mobileNumber,
            user.profilePictureUrl.isEmpty ? 'N/A' : user.profilePictureUrl,
            user.isPublisher ? 'Publisher' : 'Advertiser',
            _formatDate(user.createdAt),
            _formatDate(user.lastLogin),
            user.additionalDetails?['provider']?.toString() ?? 'N/A',
            _formatCurrency(user.mainBalance),
            _formatCurrency(user.reservedBalance),
            _formatCurrency(user.buyerFunds),
            _formatCurrency(user.reservedFunds),
            user.isBlocked ? 'Blocked' : 'Active',
            user.isAdmin ? 'Yes' : 'No',
          ]),
    ];

    String csv = const ListToCsvConverter().convert(rows);
    final bytes = utf8.encode(csv);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..download =
          '${isAdminTable ? 'admin' : 'standard'}_users_export_${DateTime.now().toIso8601String()}.csv';
    html.document.body!.append(anchor);
    anchor.click();
    anchor.remove();
    html.Url.revokeObjectUrl(url);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Widget _buildTab(String title, bool isSelected) {
    IconData icon;
    Color color = AppColors.blue;

    switch (title) {
      case 'All':
        icon = Icons.people;
        break;
      case 'Publisher':
        icon = Icons.publish;
        color = AppColors.green;
        break;
      case 'Advertiser':
        icon = Icons.campaign;
        color = AppColors.blue;
        break;
      case 'Blocked':
        icon = Icons.block;
        color = AppColors.red;
        break;
      default:
        icon = Icons.people;
    }

    final lightColor = color == AppColors.blue
        ? const Color.fromRGBO(232, 240, 254, 1.0)
        : color == AppColors.green
            ? const Color.fromRGBO(232, 245, 233, 1.0)
            : color == AppColors.red
                ? const Color.fromRGBO(255, 235, 238, 1.0)
                : const Color.fromRGBO(245, 248, 253, 1.0);

    return Tooltip(
      message: 'Filter by $title',
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () => setState(() => _selectedRole = title),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? lightColor : Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: isSelected
                    ? color
                    : const Color.fromRGBO(232, 240, 254, 0.8),
                width: isSelected ? 1.5 : 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isSelected ? color : AppColors.grey,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: 14,
                    color: isSelected ? color : AppColors.dark,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAdminSummary(List<UserModel> users, double screenWidth) {
    final totalUsers = users.length + _adminUsers.length;
    final blockedUsers = users.where((user) => user.isBlocked).length +
        _adminUsers.where((user) => user.isBlocked).length;
    final activeUsers = totalUsers - blockedUsers;
    final publishers = users.where((user) => user.isPublisher).length;
    final advertisers = users.where((user) => !user.isPublisher).length;

    final crossAxisCount = screenWidth < 600
        ? 1
        : screenWidth < 900
            ? 2
            : 5;

    final cardWidth = screenWidth < 600
        ? screenWidth * 0.9
        : screenWidth / crossAxisCount - 32;

    return GridView.count(
      crossAxisCount: crossAxisCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          title: 'Total Users',
          value: totalUsers.toString(),
          color: Colors.blue.shade700,
          icon: Icons.people,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Active Users',
          value: activeUsers.toString(),
          color: Colors.orange.shade700,
          icon: Icons.people,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Publishers',
          value: publishers.toString(),
          color: Colors.green.shade600,
          icon: Icons.publish,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Advertisers',
          value: advertisers.toString(),
          color: Colors.amber.shade700,
          icon: Icons.campaign,
          cardWidth: cardWidth,
        ),
        _buildSummaryCard(
          title: 'Blocked Users',
          value: blockedUsers.toString(),
          color: Colors.red.shade600,
          icon: Icons.block,
          cardWidth: cardWidth,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
    required double cardWidth,
  }) {
    final isSmallScreen = cardWidth < 200;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: cardWidth,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.componentBackColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade800,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 24 : 32,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double? value) {
    if (value == null) return '0.00';
    return NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(value);
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final screenWidth = constraints.maxWidth;
      final isSmallScreen = screenWidth < 600;
      final isWideScreen = screenWidth > 800;

      if (_auth.currentUser == null || _adminUid == null) {
        return const Center(
            child: Text('Please sign in to view the dashboard'));
      }

      final filteredUsers = _filteredUsers(_users);
      final searchedUsers = _searchedUsers(filteredUsers);
      final searchedAdminUsers = _searchedUsers(_adminUsers);

      return Scaffold(
          backgroundColor: Colors.white,
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Container(
                      color: Colors.white,
                      padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(
                                        26, 115, 232, 0.08),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                    spreadRadius: 0,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8)),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: const Icon(
                                          Icons.people,
                                          color: AppColors.blue,
                                          size: 28,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Text(
                                        'User Management',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 24 : 32,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.dark,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Tooltip(
                                        message:
                                            'Export admin user data as CSV',
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                232, 240, 254, 1.0),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: IconButton(
                                            icon: const Icon(Icons.download,
                                                color: AppColors.blue),
                                            onPressed: () => _exportToCsv(
                                                searchedAdminUsers,
                                                isAdminTable: true),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Tooltip(
                                        message:
                                            'Export standard user data as CSV',
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                232, 240, 254, 1.0),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: IconButton(
                                            icon: const Icon(Icons.download,
                                                color: AppColors.blue),
                                            onPressed: () =>
                                                _exportToCsv(searchedUsers),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Tooltip(
                                        message: 'Refresh data',
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                232, 240, 254, 1.0),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: IconButton(
                                            icon: const Icon(Icons.refresh,
                                                color: AppColors.blue),
                                            onPressed: _loadUsers,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            // Search Section
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(
                                        26, 115, 232, 0.08),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                    spreadRadius: 0,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8)),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: const Icon(
                                          Icons.search,
                                          color: AppColors.blue,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Search Users',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 18 : 20,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.dark,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  TextField(
                                    controller: _searchController,
                                    decoration: InputDecoration(
                                      hintText:
                                          'Search users by name, email, or ID',
                                      hintStyle: TextStyle(
                                        fontFamily: 'Space',
                                        fontSize: isSmallScreen ? 14 : 16,
                                        color: AppColors.grey,
                                      ),
                                      fillColor: const Color.fromRGBO(
                                          245, 248, 253, 1.0),
                                      filled: true,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          width: 1,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          width: 1,
                                        ),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                          color: AppColors.blue,
                                          width: 1,
                                        ),
                                      ),
                                      prefixIcon: const Icon(Icons.search,
                                          color: AppColors.blue),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 12),
                                    ),
                                    style: TextStyle(
                                      fontFamily: 'Space',
                                      fontSize: isSmallScreen ? 14 : 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            _buildAdminSummary(_users, screenWidth),
                            const SizedBox(height: 24),
                            // Admin Users Table
                            if (_adminUsers.isNotEmpty) ...[
                              Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color.fromRGBO(
                                          26, 115, 232, 0.08),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                      spreadRadius: 0,
                                    ),
                                  ],
                                  border: Border.all(
                                      color: const Color.fromRGBO(
                                          232, 240, 254, 0.8)),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                232, 245, 233, 1.0),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: const Icon(
                                            Icons.admin_panel_settings,
                                            color: AppColors.green,
                                            size: 18,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Admin Users',
                                          style: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: isSmallScreen ? 18 : 20,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.dark,
                                          ),
                                        ),
                                        const Spacer(),
                                        Text(
                                          '${searchedAdminUsers.length} admin${searchedAdminUsers.length != 1 ? 's' : ''}',
                                          style: TextStyle(
                                            fontFamily: 'Space',
                                            fontSize: 14,
                                            color: AppColors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    if (searchedAdminUsers.isEmpty)
                                      Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.person_off,
                                                size: isSmallScreen ? 48 : 64,
                                                color: Colors.grey[400]),
                                            const SizedBox(height: 16),
                                            Text(
                                              'No admin users found',
                                              style: TextStyle(
                                                  fontFamily: 'Space',
                                                  fontSize:
                                                      isSmallScreen ? 16 : 18,
                                                  color: Colors.grey[600]),
                                            ),
                                          ],
                                        ),
                                      )
                                    else
                                      Scrollbar(
                                        controller: _scrollController,
                                        thumbVisibility: true,
                                        child: SingleChildScrollView(
                                          controller: _scrollController,
                                          scrollDirection: Axis.horizontal,
                                          child: DataTable(
                                            columnSpacing: isWideScreen
                                                ? 40
                                                : isSmallScreen
                                                    ? 20
                                                    : 30,
                                            headingRowColor:
                                                WidgetStateProperty.all(
                                                    const Color(0xFFE8F0FE)),
                                            dataRowColor:
                                                WidgetStateProperty.resolveWith(
                                              (states) {
                                                if (states.contains(
                                                    WidgetState.hovered)) {
                                                  return const Color(
                                                      0xFFF5F8FD);
                                                }
                                                return Colors.white;
                                              },
                                            ),
                                            border: TableBorder(
                                              horizontalInside: BorderSide(
                                                color:
                                                    Colors.grey.withAlpha(40),
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            columns: [
                                              DataColumn(
                                                label: Text(
                                                  'UID',
                                                  style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: AppColors.dark,
                                                  ),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Name',
                                                  style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: AppColors.dark,
                                                  ),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Email',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: AppColors.dark),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Email Verified',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: AppColors.dark),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Mobile Number',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Role',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Created At',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Last Login',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Provider',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Main Balance',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Reserved Balance',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Buyer Funds',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Reserved Funds',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Status',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Is Admin',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  'Actions',
                                                  style: TextStyle(
                                                      fontFamily: 'Space',
                                                      fontSize: isSmallScreen
                                                          ? 12
                                                          : 14,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87),
                                                ),
                                              ),
                                            ],
                                            rows: searchedAdminUsers
                                                .map((user) => _buildDataRow(
                                                    user, isSmallScreen))
                                                .toList(),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 32),
                            ],
                            // Standard Users Table
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color.fromRGBO(
                                        26, 115, 232, 0.08),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                    spreadRadius: 0,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        232, 240, 254, 0.8)),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              232, 240, 254, 1.0),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: const Icon(
                                          Icons.people,
                                          color: AppColors.blue,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        'Standard Users',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: isSmallScreen ? 18 : 20,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.dark,
                                        ),
                                      ),
                                      const Spacer(),
                                      Text(
                                        '${searchedUsers.length} user${searchedUsers.length != 1 ? 's' : ''}',
                                        style: TextStyle(
                                          fontFamily: 'Space',
                                          fontSize: 14,
                                          color: AppColors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: [
                                        _buildTab(
                                            'All', _selectedRole == 'All'),
                                        const SizedBox(width: 12),
                                        _buildTab('Publisher',
                                            _selectedRole == 'Publisher'),
                                        const SizedBox(width: 12),
                                        _buildTab('Advertiser',
                                            _selectedRole == 'Advertiser'),
                                        const SizedBox(width: 12),
                                        _buildTab('Blocked',
                                            _selectedRole == 'Blocked'),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  if (searchedUsers.isEmpty)
                                    Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.person_off,
                                              size: isSmallScreen ? 48 : 64,
                                              color: Colors.grey[400]),
                                          const SizedBox(height: 16),
                                          Text(
                                            'No standard users found',
                                            style: TextStyle(
                                                fontFamily: 'Space',
                                                fontSize:
                                                    isSmallScreen ? 16 : 18,
                                                color: Colors.grey[600]),
                                          ),
                                        ],
                                      ),
                                    )
                                  else
                                    Scrollbar(
                                      controller: _scrollController2,
                                      thumbVisibility: true,
                                      child: SingleChildScrollView(
                                        controller: _scrollController2,
                                        scrollDirection: Axis.horizontal,
                                        child: DataTable(
                                          columnSpacing: isWideScreen
                                              ? 40
                                              : isSmallScreen
                                                  ? 20
                                                  : 30,
                                          headingRowColor:
                                              WidgetStateProperty.all(
                                                  const Color(0xFFE8F0FE)),
                                          dataRowColor:
                                              WidgetStateProperty.resolveWith(
                                            (states) {
                                              if (states.contains(
                                                  WidgetState.hovered)) {
                                                return const Color(0xFFF5F8FD);
                                              }
                                              return Colors.white;
                                            },
                                          ),
                                          border: TableBorder(
                                            horizontalInside: BorderSide(
                                              color: Colors.grey.withAlpha(40),
                                              width: 1,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          columns: [
                                            DataColumn(
                                              label: Text(
                                                'UID',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Name',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Email',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Email Verified',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Mobile Number',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Role',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Created At',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Last Login',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Provider',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Main Balance',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Reserved Balance',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Buyer Funds',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Reserved Funds',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Status',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Is Admin',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                            DataColumn(
                                              label: Text(
                                                'Actions',
                                                style: TextStyle(
                                                    fontFamily: 'Space',
                                                    fontSize:
                                                        isSmallScreen ? 12 : 14,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87),
                                              ),
                                            ),
                                          ],
                                          rows: searchedUsers
                                              .map((user) => _buildDataRow(
                                                  user, isSmallScreen))
                                              .toList(),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ]))));
    });
  }

  DataRow _buildDataRow(UserModel user, bool isSmallScreen) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            user.uid,
            style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            user.name,
            style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            user.email,
            style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          PopupMenuButton<bool>(
            initialValue: user.emailVerified,
            onSelected: (value) async {
              try {
                await _firestoreService.updateDocument(
                  collectionPath: 'users',
                  documentId: user.uid,
                  data: {'emailVerified': value},
                );

                setState(() {
                  if (user.isAdmin) {
                    final index =
                        _adminUsers.indexWhere((u) => u.uid == user.uid);
                    if (index != -1) {
                      _adminUsers[index] =
                          _adminUsers[index].copyWith(emailVerified: value);
                    }
                  } else {
                    final index = _users.indexWhere((u) => u.uid == user.uid);
                    if (index != -1) {
                      _users[index] =
                          _users[index].copyWith(emailVerified: value);
                    }
                  }
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Email verification status changed to ${value ? 'Verified' : 'Not Verified'}'),
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error changing email verification: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: true,
                child: Text('Verified'),
              ),
              const PopupMenuItem(
                value: false,
                child: Text('Not Verified'),
              ),
            ],
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                color: user.emailVerified
                    ? const Color(0xFFDCFCE7) // Light green background
                    : const Color(0xFFFEE2E2), // Light red background
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: user.emailVerified
                      ? const Color(0xFF86EFAC) // Medium green border
                      : const Color(0xFFFCA5A5), // Medium red border
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    user.emailVerified ? Icons.check_circle : Icons.cancel,
                    size: 14,
                    color: user.emailVerified ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    user.emailVerified ? 'Yes' : 'No',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 12 : 14,
                      fontWeight: FontWeight.bold,
                      color: user.emailVerified ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            user.mobileNumber.isEmpty ? 'N/A' : user.mobileNumber,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          PopupMenuButton<bool>(
            initialValue: user.isPublisher,
            onSelected: (value) => _changeUserRole(user, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: true,
                child: Text('Publisher'),
              ),
              const PopupMenuItem(
                value: false,
                child: Text('Advertiser'),
              ),
            ],
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                color: user.isPublisher
                    ? const Color(0xFFDCFCE7) // Light green background
                    : const Color(0xFFE0F2FE), // Light blue background
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: user.isPublisher
                      ? const Color(0xFF86EFAC) // Medium green border
                      : const Color(0xFF93C5FD), // Medium blue border
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    user.isPublisher ? Icons.publish : Icons.campaign,
                    size: 14,
                    color: user.isPublisher ? Colors.green : AppColors.blue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    user.isPublisher ? 'Publisher' : 'Advertiser',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 12 : 14,
                      fontWeight: FontWeight.bold,
                      color: user.isPublisher ? Colors.green : AppColors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        DataCell(
          Text(
            _formatDate(user.createdAt),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontStyle: FontStyle.italic,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            _formatDate(user.lastLogin),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontStyle: FontStyle.italic,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            user.additionalDetails.containsKey('provider')
                ? user.additionalDetails['provider'].toString()
                : 'N/A',
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            _formatCurrency(user.mainBalance),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            _formatCurrency(user.reservedBalance),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            _formatCurrency(user.buyerFunds),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Text(
            _formatCurrency(user.reservedFunds),
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 14 : 16,
              color: AppColors.dark,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: user.isBlocked
                  ? const Color(0xFFFEE2E2) // Light red background
                  : const Color(0xFFDCFCE7), // Light green background
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: user.isBlocked
                    ? const Color(0xFFFCA5A5) // Medium red border
                    : const Color(0xFF86EFAC), // Medium green border
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  user.isBlocked ? Icons.block : Icons.check_circle,
                  size: 14,
                  color: user.isBlocked ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  user.isBlocked ? 'Blocked' : 'Active',
                  style: TextStyle(
                    fontFamily: 'Space',
                    fontSize: isSmallScreen ? 12 : 14,
                    fontWeight: FontWeight.bold,
                    color: user.isBlocked ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ),
        !user.isAdmin
            ? DataCell(
                PopupMenuButton<bool>(
                  initialValue: user.isAdmin,
                  onSelected: (value) async {
                    try {
                      await _firestoreService.updateDocument(
                        collectionPath: 'users',
                        documentId: user.uid,
                        data: {'isAdmin': value},
                      );

                      setState(() {
                        if (value) {
                          // Move to admin list
                          final index =
                              _users.indexWhere((u) => u.uid == user.uid);
                          if (index != -1) {
                            final updatedUser =
                                _users[index].copyWith(isAdmin: value);
                            _users.removeAt(index);
                            _adminUsers.add(updatedUser);
                          }
                        } else {
                          // Move to standard list
                          final index =
                              _adminUsers.indexWhere((u) => u.uid == user.uid);
                          if (index != -1) {
                            final updatedUser =
                                _adminUsers[index].copyWith(isAdmin: value);
                            _adminUsers.removeAt(index);
                            _users.add(updatedUser);
                          }
                        }
                      });

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              'User role changed to ${value ? 'Admin' : 'User'}'),
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error changing role: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: true,
                      child: Text('Admin'),
                    ),
                    const PopupMenuItem(
                      value: false,
                      child: Text('User'),
                    ),
                  ],
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: user.isAdmin
                          ? const Color(0xFFDCFCE7) // Light green background
                          : const Color(0xFFE0F2FE), // Light blue background
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: user.isAdmin
                            ? const Color(0xFF86EFAC) // Medium green border
                            : const Color(0xFF93C5FD), // Medium blue border
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          user.isAdmin
                              ? Icons.admin_panel_settings
                              : Icons.person,
                          size: 14,
                          color: user.isAdmin ? Colors.green : AppColors.blue,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          user.isAdmin ? 'Admin' : 'User',
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: isSmallScreen ? 12 : 14,
                            fontWeight: FontWeight.bold,
                            color: user.isAdmin ? Colors.green : AppColors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : DataCell(SizedBox.shrink()),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ImprovedActionButton(
                icon: Icons.info,
                color: AppColors.blue,
                tooltip: 'View User Details',
                onPressed: () => _showUserDetailsDialog(user.toMap()),
                size: isSmallScreen ? 18 : 20,
              ),
              !user.isAdmin
                  ? ImprovedActionButton(
                      icon: user.isBlocked ? Icons.lock_open : Icons.lock,
                      color: user.isBlocked ? Colors.green : Colors.red,
                      tooltip: user.isBlocked ? 'Unblock User' : 'Block User',
                      onPressed: () => _toggleBlockUser(user),
                      size: isSmallScreen ? 18 : 20,
                    )
                  : const SizedBox.shrink(),
              !user.isAdmin
                  ? ImprovedActionButton(
                      icon: Icons.delete,
                      color: Colors.red,
                      tooltip: 'Delete User',
                      onPressed: () => _deleteUser(user),
                      size: isSmallScreen ? 18 : 20,
                    )
                  : const SizedBox.shrink(),
              ImprovedActionButton(
                icon: Icons.history,
                color: AppColors.blue,
                tooltip: 'View Activity Logs',
                onPressed: () => _viewActivityLogs(user),
                size: isSmallScreen ? 18 : 20,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showUserDetailsDialog(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => ImprovedUserDetailsDialog(
        uid: user['uid'],
        name: user['name'],
        email: user['email'],
        emailVerified: user['emailVerified'],
        mobileNumber: user['mobileNumber'],
        profilePictureUrl: user['profilePictureUrl'],
        isPublisher: user['isPublisher'],
        createdAt: user['createdAt'],
        lastLogin: user['lastLogin'],
        additionalDetails: user['additionalDetails'],
        isBlocked: user['isBlocked'],
        isAdmin: user['isAdmin'],
        assignedOrderIds: user['assignedOrderIds'],
        mainBalance: user['mainBalance'],
        reservedBalance: user['reservedBalance'],
        buyerFunds: user['buyerFunds'],
        reservedFunds: user['reservedFunds'],
      ),
    );
  }
}

class UserDetailsDialog extends StatefulWidget {
  final String uid;
  final String name;
  final String email;
  final bool emailVerified;
  final String mobileNumber;
  final String profilePictureUrl;
  final bool isPublisher;
  final Timestamp? createdAt;
  final Timestamp? lastLogin;
  final Map<String, dynamic> additionalDetails;
  final bool isBlocked;
  final bool isAdmin;
  final List<String> assignedOrderIds;
  final double mainBalance;
  final double reservedBalance;
  final double buyerFunds;
  final double reservedFunds;

  const UserDetailsDialog({
    Key? key,
    required this.uid,
    required this.name,
    required this.email,
    required this.emailVerified,
    required this.mobileNumber,
    required this.profilePictureUrl,
    required this.isPublisher,
    this.createdAt,
    this.lastLogin,
    required this.additionalDetails,
    required this.isBlocked,
    required this.isAdmin,
    required this.assignedOrderIds,
    required this.mainBalance,
    required this.reservedBalance,
    required this.buyerFunds,
    required this.reservedFunds,
  }) : super(key: key);

  @override
  _UserDetailsDialogState createState() => _UserDetailsDialogState();
}

class _UserDetailsDialogState extends State<UserDetailsDialog> {
  int _orderCount = 0;
  int _websiteCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchCounts();
  }

  Future<void> _fetchCounts() async {
    setState(() => _isLoading = true);
    try {
      // Fetch order count (use assignedOrderIds if publisher, else query orders)
      if (widget.isPublisher) {
        _orderCount = widget.assignedOrderIds.length;
      } else {
        final orderSnapshot = await FirebaseFirestore.instance
            .collection('orders')
            .where('buyerId', isEqualTo: widget.uid)
            .get();
        _orderCount = orderSnapshot.docs.length;
      }

      // Fetch website count
      final websiteSnapshot = await FirebaseFirestore.instance
          .collection('websites')
          .where('publisherId', isEqualTo: widget.uid)
          .get();
      _websiteCount = websiteSnapshot.docs.length;

      if (mounted) {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching counts: $e'),
            backgroundColor: AppColors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 8,
      backgroundColor: Colors.white,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isSmallScreen ? 600 : 800,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.yellow.withOpacity(0.2),
                    AppColors.yellow.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'User Details - ${widget.name}',
                    style: TextStyle(
                      fontFamily: 'Space',
                      fontSize: isSmallScreen ? 22 : 26,
                      fontWeight: FontWeight.bold,
                      color: AppColors.dark,
                    ),
                  ),
                  Tooltip(
                    message: 'Close',
                    child: IconButton(
                      icon: const Icon(Icons.close, color: AppColors.grey),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSection('General Information', isSmallScreen, [
                            _buildDetailRow(
                              'Profile Picture',
                              widget.profilePictureUrl.isEmpty
                                  ? const CircleAvatar(
                                      radius: 40,
                                      backgroundColor: AppColors.grey,
                                      child: Icon(Icons.person,
                                          size: 40, color: Colors.white),
                                    )
                                  : CircleAvatar(
                                      radius: 40,
                                      backgroundImage: NetworkImage(
                                          widget.profilePictureUrl),
                                      onBackgroundImageError:
                                          (error, stackTrace) => const Icon(
                                              Icons.error,
                                              color: AppColors.red),
                                    ),
                              isWidget: true,
                            ),
                            _buildDetailRow(
                              'Name',
                              widget.name,
                            ),
                            _buildDetailRow(
                              'Email',
                              widget.email,
                              isLink: true,
                              link: 'mailto:${widget.email}',
                            ),
                            _buildDetailRow(
                              'Email Verified',
                              widget.emailVerified ? 'Yes' : 'No',
                            ),
                            _buildDetailRow(
                              'Mobile Number',
                              widget.mobileNumber.isEmpty
                                  ? 'N/A'
                                  : widget.mobileNumber,
                              isLink: widget.mobileNumber.isNotEmpty,
                              link: 'tel:${widget.mobileNumber}',
                            ),
                            _buildDetailRow(
                              'User ID',
                              widget.uid,
                            ),
                          ]),
                          _buildSection('Account Status', isSmallScreen, [
                            _buildDetailRow(
                              'Created At',
                              _formatDate(widget.createdAt),
                            ),
                            _buildDetailRow(
                              'Last Login',
                              _formatDate(widget.lastLogin),
                            ),
                            _buildDetailRow(
                              'Is Blocked',
                              widget.isBlocked ? 'Yes' : 'No',
                              tooltip: widget.isBlocked
                                  ? 'User account is currently blocked'
                                  : 'User account is active',
                            ),
                            _buildDetailRow(
                              'Is Admin',
                              widget.isAdmin ? 'Yes' : 'No',
                              tooltip: widget.isAdmin
                                  ? 'User has admin privileges'
                                  : 'User is not an admin',
                            ),
                          ]),
                          _buildSection('Publisher Details', isSmallScreen, [
                            _buildDetailRow(
                              'Is Publisher',
                              widget.isPublisher ? 'Yes' : 'No',
                            ),
                            _buildDetailRow(
                              'Assigned Orders',
                              _orderCount.toString(),
                            ),
                            _buildDetailRow(
                              'Main Balance',
                              '\$${widget.mainBalance.toStringAsFixed(2)}',
                            ),
                            _buildDetailRow(
                              'Reserved Balance',
                              '\$${widget.reservedBalance.toStringAsFixed(2)}',
                            ),
                            _buildDetailRow(
                              'Websites',
                              _websiteCount.toString(),
                            ),
                          ]),
                          _buildSection('Buyer Details', isSmallScreen, [
                            _buildDetailRow(
                              'Orders Placed',
                              _orderCount.toString(),
                            ),
                            _buildDetailRow(
                              'Buyer Funds',
                              '\$${widget.buyerFunds.toStringAsFixed(2)}',
                            ),
                            _buildDetailRow(
                              'Reserved Funds',
                              '\$${widget.reservedFunds.toStringAsFixed(2)}',
                            ),
                          ]),
                          if (widget.additionalDetails.isNotEmpty)
                            _buildSection('Additional Details', isSmallScreen, [
                              ...widget.additionalDetails.entries
                                  .map(
                                    (entry) => _buildDetailRow(
                                      entry.key,
                                      entry.value.toString(),
                                    ),
                                  )
                                  .toList(),
                            ]),
                        ],
                      ),
                    ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(top: BorderSide(color: Colors.grey[200]!)),
                borderRadius:
                    const BorderRadius.vertical(bottom: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.grey,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'Close',
                        style: TextStyle(
                          fontFamily: 'Space',
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
      String title, bool isSmallScreen, List<Widget> children) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Space',
              fontSize: isSmallScreen ? 18 : 20,
              fontWeight: FontWeight.bold,
              color: AppColors.dark,
            ),
          ),
          const SizedBox(height: 12),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.light.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, dynamic value,
      {bool isLink = false,
      String? link,
      bool isWidget = false,
      String? tooltip}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 180,
            child: Tooltip(
              message: tooltip ?? label,
              child: Text(
                label,
                style: const TextStyle(
                  fontFamily: 'Space',
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: AppColors.dark,
                ),
              ),
            ),
          ),
          Expanded(
            child: isWidget
                ? value
                : isLink && link != null
                    ? InkWell(
                        onTap: () => _launchURL(link),
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            fontFamily: 'Space',
                            fontSize: 14,
                            color: AppColors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )
                    : Text(
                        value.toString(),
                        style: const TextStyle(
                          fontFamily: 'Space',
                          fontSize: 14,
                          color: AppColors.dark,
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not launch $url'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    }
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }
}
